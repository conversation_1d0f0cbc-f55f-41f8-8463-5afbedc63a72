[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "waiy_infra"
version = "0.3.0"
description = "WAIY智能体框架"
readme = "README.md"
authors = [
    {name = "WAIY Team", email = "<EMAIL>"}
]
license = {text = "Proprietary"}
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "License :: Alibaba Cloud",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = [
    "openai-agents[litellm]==0.2.5",
    "openai==1.99.9",
    "pydantic-settings>=2.0.0",
    "pydantic>=2.0.0",
    "rich>=12.0.0",
    "fastapi>=0.100.0",
    "uvicorn>=0.20.0",
    "alibabacloud_iqs20241111>=1.3.0",
    "alibabacloud_tea_openapi",
    "python-dotenv",
    "md2pdf",
    "gradio>=4.0.0",
    "httpx",
    "markdown",
    "nacos-sdk-python==0.1.16",
    "logfire>=3.21.1",
    "opentelemetry-instrumentation-fastapi",
    "loguru",
    "sqlalchemy>=2.0.0",
    "alembic>=1.8.0",
    "python-gnupg",
    "requests",
    "gmssl",
    "pycryptodomex",
    "keycenter-office==2.3.9",
    "tavily-python>=0.7.8",
    "alibabacloud-wuyingaiinner20250709>=1.2.1",
    "waiy-memory==0.2.7",
    "aliyun-akless-credential-provider-python-sdk==1.10.2",
    "wuying-agentbay-sdk>=0.5.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.19.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "mypy>=0.990",
    "matplotlib>=3.9.4",
    "seaborn>=0.13.2",
]

[project.scripts]
waiy = "main:main"
waiy-api = "api:main"

[tool.setuptools]
package-dir = {"" = "."}

[tool.black]
line-length = 100
target-version = ["py312"]

[tool.isort]
profile = "black"
line_length = 100

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false

[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"
extra-index-url = ["http://yum.tbsite.net/aliyun-pypi/simple/"]
allow-insecure-host = ["mirrors.aliyun.com", "yum.tbsite.net"]
prerelease = "allow"
index-strategy = "unsafe-best-match"
