import os
import signal
import socket
import subprocess
import sys
import threading
import time


def wait_for_port(port, host="127.0.0.1", timeout=30):
    start = time.time()
    while time.time() - start < timeout:
        try:
            with socket.create_connection((host, port), timeout=1):
                return True
        except OSError:
            time.sleep(0.5)
    raise TimeoutError(f"等待端口 {port} 超时")


def check_and_kill_port(port):
    """检查并清理占用指定端口的进程"""
    try:
        # 查找占用端口的进程
        result = subprocess.run(
            ["lsof", "-ti", f":{port}"], capture_output=True, text=True, timeout=5
        )
        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split("\n")
            print(f"发现端口 {port} 被进程占用: {', '.join(pids)}")

            # 杀死所有占用端口的进程
            for pid in pids:
                if pid.strip():
                    try:
                        subprocess.run(["kill", "-9", pid.strip()], timeout=5)
                        print(f"已清理进程 {pid.strip()}")
                    except Exception as e:
                        print(f"清理进程 {pid.strip()} 失败: {e}")

            # 等待一下确保端口释放
            time.sleep(1)
            print(f"端口 {port} 已清理")
        else:
            print(f"端口 {port} 未被占用")
    except Exception as e:
        print(f"检查端口 {port} 时出错: {e}")


# 全局变量存储进程
api_process = None
webui_process = None
cleanup_done = False
exit_flag = False


def cleanup_processes():
    """清理所有子进程"""
    global api_process, webui_process, cleanup_done

    if cleanup_done:
        return

    cleanup_done = True
    print("\n正在清理进程...")

    # 强制杀死所有子进程
    if webui_process and webui_process.poll() is None:
        print("强制关闭Web UI...")
        webui_process.kill()
        try:
            webui_process.wait(timeout=1)
        except subprocess.TimeoutExpired:
            pass

    if api_process and api_process.poll() is None:
        print("强制关闭API服务...")
        api_process.kill()
        try:
            api_process.wait(timeout=1)
        except subprocess.TimeoutExpired:
            pass

    print("所有进程已清理完成")


def force_exit():
    """强制退出函数"""
    cleanup_processes()
    print("强制退出程序")
    os._exit(1)


def signal_handler(signum, frame):
    """信号处理器"""
    global exit_flag

    if exit_flag:
        # 如果已经在退出流程中，直接强制退出
        print(f"\n重复信号 {signum}，立即强制退出")
        os._exit(1)

    exit_flag = True
    print(f"\n接收到信号 {signum}，正在清理...")

    # 在新线程中执行清理，避免阻塞信号处理器
    cleanup_thread = threading.Thread(target=force_exit)
    cleanup_thread.daemon = True
    cleanup_thread.start()

    # 给清理线程一点时间
    time.sleep(0.5)
    os._exit(1)


if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

    print("parmas:", sys.argv)
    if len(sys.argv) > 1:
        env = sys.argv[1][2:]
    else:
        # 没传默认走预发
        env = "pre"

    print("env:", env)

    try:
        if env == "local":
            # 启动前先清理端口
            print("检查并清理端口占用...")
            check_and_kill_port(8000)

            # 启动API服务（后台运行）
            print("正在启动API服务...")
            api_process = subprocess.Popen(["python", "api.py"])
            wait_for_port(8000)  # 等待API服务真正启动
            print("✅ API服务已启动: http://localhost:8000")

        # 启动独立的Web UI（前台运行）
        print("正在启动独立Web UI...")
        webui_process = subprocess.Popen(["python", "web_ui.py", env])

        # 等待Web UI进程
        webui_process.wait()

    except KeyboardInterrupt:
        print("\n接收到中断信号...")
        force_exit()
    except Exception as e:
        print(f"执行过程中出错: {e}")
        force_exit()
    finally:
        if not exit_flag:
            cleanup_processes()
            print("程序正常退出")
