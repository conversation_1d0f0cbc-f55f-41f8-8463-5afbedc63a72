#!/usr/bin/env python
"""
WAIY智能体框架API服务入口
"""
import argparse
import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.api import run_api_server
from src.api.apps import ApplicationRegistry
from src.common.logging import logger


async def list_available_apps():
    """列出所有可用的应用"""
    logger.info("可用的应用")
    app_list = await ApplicationRegistry.list_all_apps()

    for app_meta in app_list:
        logger.info(f"{app_meta.id}: {app_meta.name} - {app_meta.description}")

    return app_list


def main():
    logger.info("启动WAIY智能体框架API服务")

    parser = argparse.ArgumentParser(description="WAIY智能体框架API服务")
    parser.add_argument("--list", action="store_true", help="列出所有可用的应用")

    args = parser.parse_args()

    if args.list:
        # 列出所有可用应用
        asyncio.run(list_available_apps())
        sys.exit(0)

    try:
        logger.info("启动所有应用的API服务")
        run_api_server()
    except Exception as e:
        logger.error(f"API服务运行出错: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
