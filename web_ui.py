import argparse
import http.server
import os
import socketserver
import threading
from urllib.parse import unquote

from src.common.logging import logger
from src.web_ui.app import create_gradio_app


class StaticFileHandler(http.server.SimpleHTTPRequestHandler):
    """自定义静态文件处理器"""

    def __init__(self, *args, static_dir=None, **kwargs):
        self.static_dir = static_dir
        super().__init__(*args, **kwargs)

    def do_GET(self):
        if self.path.startswith("/web-ui-custom-static/"):
            # 移除URL前缀
            file_path = self.path[len("/web-ui-custom-static/") :]
            file_path = unquote(file_path)
            full_path = os.path.join(self.static_dir, file_path)

            if os.path.exists(full_path) and os.path.isfile(full_path):
                self.send_response(200)
                if file_path.endswith(".html"):
                    self.send_header("Content-type", "text/html")
                elif file_path.endswith(".css"):
                    self.send_header("Content-type", "text/css")
                elif file_path.endswith(".js"):
                    self.send_header("Content-type", "application/javascript")
                else:
                    self.send_header("Content-type", "application/octet-stream")
                self.end_headers()

                with open(full_path, "rb") as f:
                    self.wfile.write(f.read())
                return

        # 如果不是静态文件请求，返回404
        self.send_error(404)


def start_static_server(static_dir, port=7861):
    """启动静态文件服务器"""

    def handler(*args, **kwargs):
        return StaticFileHandler(*args, static_dir=static_dir, **kwargs)

    with socketserver.TCPServer(("", port), handler) as httpd:
        logger.info(f"静态文件服务器启动在端口 {port}")
        httpd.serve_forever()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="参数示例")
    parser.add_argument("env", help="环境配置")
    args = parser.parse_args()
    env = args.env
    logger.info("env:", args.env)

    if env == "local":
        base_url = "http://localhost:8000"
    if env == "pre":
        base_url = "https://pre-waiy-infra.wuying.aliyun.com"
    if env == "prod":
        base_url = "https://pre-waiy-infra.wuying.aliyun.com"

    logger.info("base_url:", base_url)

    # 为独立运行的Web UI创建静态文件目录和服务

    # 确定web_ui模块的基础目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    web_ui_base_dir = os.path.join(current_dir, "src", "web_ui")
    static_dir = os.path.join(web_ui_base_dir, "static")
    temp_reports_dir = os.path.join(static_dir, "temp_reports")

    # 创建必要的目录
    os.makedirs(temp_reports_dir, exist_ok=True)
    logger.info(f"静态文件目录已创建: {static_dir}")
    logger.info(f"报告目录已创建: {temp_reports_dir}")

    # 启动静态文件服务器（在后台线程中）
    static_thread = threading.Thread(
        target=start_static_server, args=(static_dir, 7861), daemon=True
    )
    static_thread.start()
    logger.info("静态文件服务器启动在端口7861")
    logger.info("静态文件访问地址: http://localhost:7861/web-ui-custom-static/")

    # 为本地模式创建带静态文件支持的应用
    app = create_gradio_app(api_base_url=base_url, static_base_url="http://localhost:7861")

    logger.info("启动独立Web UI在端口7860")
    app.launch(server_port=7860, share=False)
