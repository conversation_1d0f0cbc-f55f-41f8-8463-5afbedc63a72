#!/usr/bin/env python
"""
WAIY智能体框架主程序入口
可以启动任意应用
"""
import argparse
import asyncio
import os
import sys
import time
from typing import Any, Dict, Type

from src.common.logging.waiy_trace import setup_waiy_tracing

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.api.apps import ApplicationRegistry
from src.applications.base import BaseApplication
from src.common.logging import logger


async def run_application(app_class: Type[BaseApplication]) -> int:
    """
    运行指定的应用

    Args:
        app_class: 应用类

    Returns:
        退出码
    """
    setup_waiy_tracing(detailed_mode=True)

    app_instance = app_class()
    app_meta = app_instance.metadata

    logger.info(f"{app_meta.name} v{app_meta.version}")
    logger.info(f"{app_meta.description}")

    try:

        # 初始化应用
        logger.info("正在初始化系统组件...")
        if not await app_instance.initialize():
            logger.error("初始化失败，无法启动应用")
            return 1

        logger.success(f"智能体已准备就绪！")
        logger.info("输入'exit'退出，输入'history'查看历史记录")

        # 主循环
        while True:
            try:
                user_input = input("\n请输入您的请求: ")
                if user_input.lower() == "exit":
                    break

                start_time = time.time()
                logger.info("处理中...")

                # 运行Agent
                result = await app_instance.process_message(user_input)

                elapsed = time.time() - start_time
                logger.info(f"{result['response']}")
                logger.success(f"完成！(用时 {elapsed:.2f} 秒)")

            except KeyboardInterrupt:
                logger.warning("操作已中断")
                continue
            except Exception as e:
                logger.error(f"处理请求时出错: {e}")
                # logger.error("处理请求时出错", exc_info=True)
                continue

    except KeyboardInterrupt:
        logger.warning("程序已被用户中断")
    except Exception as e:
        # logger.error(f"程序运行时出错: {e}")
        logger.error("程序运行时出错", exc_info=True)
        return 1
    finally:
        # 清理资源
        await app_instance.cleanup()
        logger.info(f"已退出{app_meta.name}")

    return 0


async def list_available_apps():
    """列出所有可用的应用"""
    logger.info("可用的应用")
    app_list = await ApplicationRegistry.list_all_apps()
    available_apps = {}

    for app_meta in app_list:
        app_id = app_meta.id
        app_class = await ApplicationRegistry.get_app_class(app_id)
        if app_class:
            available_apps[app_id] = app_class
            logger.info(f"{app_id}: {app_meta.name} - {app_meta.description}")

    if not available_apps:
        logger.error("未找到可用的应用")

    return available_apps


async def main():
    """主程序入口点"""
    logger.info("启动WAIY智能体框架")

    parser = argparse.ArgumentParser(description="WAIY智能体框架")
    parser.add_argument("--app", type=str, help="要启动的应用ID")
    parser.add_argument("--list", action="store_true", help="列出所有可用的应用")

    args = parser.parse_args()

    # 获取所有可用应用
    available_apps = {}
    if args.list or not args.app:
        available_apps = await list_available_apps()
        if args.list:
            return 0
    else:
        app_list = await ApplicationRegistry.list_all_apps()
        for app_meta in app_list:
            app_id = app_meta.id
            app_class = await ApplicationRegistry.get_app_class(app_id)
            if app_class:
                available_apps[app_id] = app_class

    app_id = args.app
    if not app_id:
        # 如果没有指定应用ID，提供交互式选择
        app_id = input("请选择要启动的应用ID: ").strip()

    # 检查应用ID是否有效
    if app_id not in available_apps:
        logger.error(f"未知的应用ID: {app_id}")
        return 1

    # 运行选择的应用
    app_class = available_apps[app_id]
    return await run_application(app_class)


if __name__ == "__main__":
    # 运行异步主函数
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
