"""
生命周期管理模块
用于处理服务的上下线和心跳检测
"""

import os
import signal

from src.common.logging import logger

root_path = os.path.expanduser("~")


def offline(signum, frame):
    """
    处理下线信号
    当接收到系统信号时，执行服务下线逻辑

    Args:
        signum: 信号编号
        frame: 当前帧
    """
    logger.info(f"接收到信号: {signum}，准备下线服务")
    # 删除在线状态文件
    online_file = f"{root_path}/app.online"
    if os.path.exists(online_file):
        try:
            os.remove(online_file)
            logger.info("成功移除在线状态文件")
        except Exception as e:
            logger.error(f"移除在线状态文件失败: {e}")

    logger.info("服务已下线")


def check():
    """
    检查服务在线状态

    Returns:
        str|None: 在线文件内容或None（如果文件不存在）
    """
    online_file = f"{root_path}/app.online"
    if os.path.exists(online_file):
        try:
            with open(online_file, "r") as f:
                return f.read().strip()
        except Exception as e:
            logger.error(f"读取在线状态文件失败: {e}")
            return None
    else:
        return None


def online():
    """
    标记服务为在线状态
    创建在线状态文件并写入成功标记
    """
    online_file = f"{root_path}/app.online"
    if not os.path.exists(online_file):
        logger.info(f"生成在线状态文件: {online_file}")
        try:
            with open(online_file, "w") as f:
                f.write("success")
            logger.info("成功创建在线状态文件")
        except Exception as e:
            logger.error(f"创建在线状态文件失败: {e}")
    logger.info("服务已上线")


def register_signal():
    """
    注册信号处理器
    捕获系统信号用于服务下线处理
    """
    signal.signal(signal.SIGTERM, offline)
    signal.signal(signal.SIGINT, offline)
    signal.signal(signal.SIGQUIT, offline)
    logger.info("已注册信号处理器")
