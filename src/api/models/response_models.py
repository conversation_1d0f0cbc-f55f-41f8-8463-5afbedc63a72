"""
API响应数据模型
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """基础响应模型"""

    success: bool = Field(True, description="操作是否成功")
    message: Optional[str] = Field(None, description="响应消息")


class MessageResponse(BaseModel):
    """消息响应模型"""

    response: str
    trace_id: Optional[str] = None
    session_id: Optional[str] = None
    turns: int = 0


class AsyncTaskResponse(BaseModel):
    """异步任务响应模型"""

    session_id: str = Field(..., description="会话ID")
    trace_id: str = Field(..., description="追踪ID")
    status: str = Field(..., description="任务状态")


class ErrorResponse(BaseResponse):
    """错误响应模型"""

    success: bool = Field(False, description="操作是否成功")
    error: str = Field(..., description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class TerminateResponse(BaseModel):
    """终止异步任务响应"""

    success: bool
    message: str
    run_id: str


class TaskStatusResponse(BaseModel):
    """任务状态响应"""

    run_id: str
    status: str  # running, completed, cancelled, failed, not_found, error
    message: str
