"""
资源处理相关的数据模型
定义资源请求、响应和处理结果的数据结构
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, field_validator

from src.common.config import settings


class ResourceErrorCode(str, Enum):
    """资源处理错误码"""

    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    INVALID_RESOURCE_TYPE = "INVALID_RESOURCE_TYPE"
    RESOURCE_ACCESS_DENIED = "RESOURCE_ACCESS_DENIED"
    RESOURCE_PROCESSING_ERROR = "RESOURCE_PROCESSING_ERROR"


class KnowledgeBaseResource(BaseModel):
    """知识库资源"""

    type: Literal["knowledge_base"] = "knowledge_base"
    kb_id: str = Field(..., description="知识库ID")
    query_parameters: Dict[str, Any] = Field(
        default_factory=lambda: {"doc_memory": {}}, description="查询参数"
    )
    top_k: int = Field(default=5, description="返回结果数量", ge=1, le=20)


class FileResource(BaseModel):
    """文件资源"""

    type: Literal["file"] = "file"
    content: str = Field(..., description="解析后的文件内容")
    file_name: Optional[str] = Field(None, description="原始文件名")
    file_type: Optional[str] = Field(None, description="文件类型(pdf, docx, txt等)")
    file_size: Optional[int] = Field(None, description="文件大小(字节)", ge=0)
    address: Optional[str] = Field(None, description="文件地址/路径")
    upload_time: Optional[datetime] = Field(None, description="上传时间")

    @field_validator("content")
    @classmethod
    def validate_content(cls, v):
        """验证文件内容类型和长度"""
        # 验证内容为字符串
        if not isinstance(v, str):
            raise ValueError("文件内容必须为字符串类型")

        # 验证文件内容长度不超过配置限制
        max_size = settings.resource.max_file_content_size
        content_size = len(v.encode("utf-8"))
        if content_size > max_size:
            raise ValueError(
                f"文件内容超过{max_size // (1024*1024)}MB限制 (当前: {content_size // (1024*1024)}MB)"
            )

        return v


# 资源请求联合类型
ResourceRequest = Union[KnowledgeBaseResource, FileResource]


class KnowledgeBaseResult(BaseModel):
    """知识库检索结果"""

    success: bool = Field(..., description="检索是否成功")
    kb_ids: List[str] = Field(..., description="知识库ID列表")
    query: str = Field(..., description="检索查询语句")
    docs: List[str] = Field(default_factory=list, description="检索结果文档列表")
    total_results: int = Field(default=0, description="检索结果总数")
    search_time: float = Field(default=0.0, description="检索耗时(秒)")
    error_message: Optional[str] = Field(None, description="错误信息")

    @property
    def content(self) -> str:
        """获取合并后的内容文本"""
        if not self.docs:
            return ""
        return "\n\n".join(f"[文档{i+1}]\n{doc}" for i, doc in enumerate(self.docs))


class FileParseResult(BaseModel):
    """文件处理结果"""

    success: bool = Field(..., description="处理是否成功")
    content: str = Field(default="", description="文件内容")
    file_name: Optional[str] = Field(None, description="文件名")
    file_type: Optional[str] = Field(None, description="文件类型")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    address: Optional[str] = Field(None, description="文件地址")
    upload_time: Optional[datetime] = Field(None, description="上传时间")
    error_message: Optional[str] = Field(None, description="错误信息")


class KnowledgeBaseInfo(BaseModel):
    """知识库信息（用于响应）"""

    kb_ids: List[str] = Field(..., description="知识库ID列表")
    query: str = Field(..., description="检索查询")
    success: bool = Field(..., description="检索是否成功")
    result_count: int = Field(default=0, description="结果数量")
    search_time: float = Field(default=0.0, description="检索耗时")
    error_message: Optional[str] = Field(None, description="错误信息")


class FileResourceInfo(BaseModel):
    """文件资源信息（用于响应）"""

    filename: Optional[str] = Field(None, description="文件名")
    file_type: Optional[str] = Field(None, description="文件类型")
    file_size: Optional[int] = Field(None, description="文件大小")
    address: Optional[str] = Field(None, description="文件地址")
    upload_time: Optional[datetime] = Field(None, description="上传时间")
    success: bool = Field(True, description="处理是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")


class ResourceInfo(BaseModel):
    """资源处理信息（用于响应）"""

    knowledge_bases: List[KnowledgeBaseInfo] = Field(default_factory=list, description="知识库信息")
    files: List[FileResourceInfo] = Field(default_factory=list, description="文件信息")
