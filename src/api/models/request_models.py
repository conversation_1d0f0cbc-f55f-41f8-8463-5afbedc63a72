"""
API请求数据模型
"""

from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field, field_validator, model_validator

from .resource_models import ResourceRequest

class ModelSetting(BaseModel):
    """模型设置配置"""

    model_level: Optional[str] = Field(default=None, description="模型等级信息")

    @field_validator("model_level", mode="before")
    @classmethod
    def validate_model_level(cls, v):
        """验证并映射model_level字段值"""
        # 定义映射规则
        level_mapping = {
            "balance": "medium",
            "high-performance": "high"
        }
        
        # 如果值为None或空字符串，返回"low"
        if v is None or v == "":
            return "low"
        
        # 如果不是字符串类型，转换为字符串
        if not isinstance(v, str):
            v = str(v)
        
        # 进行映射转换
        mapped_value = level_mapping.get(v, v)
        
        # 如果映射后的值不在允许的范围内，默认返回"low"
        if mapped_value not in ["low", "medium", "high"]:
            return "low"
        
        return mapped_value

    @model_validator(mode="after")
    def set_default_model_level(self):
        """确保model_level总是有值"""
        if self.model_level is None:
            self.model_level = "low"
        return self

class RuntimeResource(BaseModel):
    """运行时资源配置"""

    type: Literal["agentbay", "desktop", "none"] = Field(..., description="云资源类型")
    token: Optional[str] = Field(None, description="云资源凭证")
    region: Optional[str] = Field(None, description="云资源地域")
    cloud_resource_id: Optional[str] = Field(None, description="云资源唯一标识")

    @model_validator(mode="after")
    def validate_required_fields(self):
        """验证不同场景下的必填字段"""
        if self.type == "desktop":
            if not self.region:
                raise ValueError("desktop场景下region字段为必填")
            if not self.cloud_resource_id:
                raise ValueError("desktop场景下cloud_resource_id字段为必填")
            if not self.token:
                raise ValueError("desktop场景下token字段为必填")
        elif self.type == "agentbay":
            if not self.token:
                raise ValueError("agentbay场景下token字段为必填")
        # none类型不需要验证任何字段
        return self


class MessageRequest(BaseModel):
    """消息请求模型"""

    message: str
    requestId: str
    traceId: Optional[str] = None
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="请求上下文")
    resources: Optional[List[ResourceRequest]] = Field(default_factory=list, description="资源列表")

    @field_validator("context", mode="before")
    @classmethod
    def validate_context(cls, v):
        """验证并规范化context字段"""
        # 如果context为None或null，返回空字典
        if v is None:
            return {}
        # 如果不是字典类型，抛出错误
        if not isinstance(v, dict):
            raise ValueError("context必须是字典类型")

        # 如果包含runtime_resource，验证其格式
        if "runtime_resource" in v and v["runtime_resource"] is not None:
            try:
                # 在网关层进行格式校验，确保数据格式正确
                RuntimeResource.model_validate(v["runtime_resource"])
            except Exception as e:
                raise ValueError(f"runtime_resource格式错误: {str(e)}")

        # 如果包含model_setting，验证其格式
        if "model_setting" in v and v["model_setting"] is not None:
            try:
                # 在网关层进行格式校验，确保数据格式正确
                ModelSetting.model_validate(v["model_setting"])
            except Exception as e:
                raise ValueError(f"model_setting格式错误: {str(e)}")

        return v


class TerminateRequest(BaseModel):
    """终止异步任务请求"""

    requestId: str
    traceId: Optional[str] = None
    run_id: str
    reason: Optional[str] = None
