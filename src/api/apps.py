"""
API应用路由注册器
自动发现和注册已有应用路由
"""

import asyncio
import importlib
import inspect
import os
import uuid
from typing import Any, AsyncGenerator, Dict, List, Optional, Type

from agents import gen_trace_id
from fastapi import APIRouter, Depends, FastAPI, HTTPException
from memory import Memory
from pydantic import BaseModel

from src.api.models import (
    AsyncTaskResponse,
    MessageRequest,
    MessageResponse,
    TaskStatusResponse,
    TerminateRequest,
    TerminateResponse,
)
from src.applications.base import BaseApplication
from src.common.logging import logger
from src.common.logging.logger import set_trace_id
from src.common.models import AppMetadata

from ..core.memory.initialization import get_memory


class AsyncTaskManager:
    def __init__(self):
        self.memory: Memory = get_memory()

    async def get_task_status(self, run_id: str) -> Optional[str]:
        """通过memory获取任务状态"""
        logger.info(f"开始查询任务状态，run_id={run_id}")

        try:
            run = self.memory.get_run(run_id)
            logger.debug(f"从memory获取到run对象，run_id={run_id}, run对象类型={type(run)}")

            if run:
                logger.debug(f"run对象存在，run_id={run_id}, 属性列表={dir(run)}")

                if hasattr(run, "run_status"):
                    status = run.run_status
                    logger.info(f"成功获取任务状态，run_id={run_id}, status={status}")
                    return status
                else:
                    logger.warning(
                        f"run对象缺少run_status属性，run_id={run_id}, 可用属性={[attr for attr in dir(run) if not attr.startswith('_')]}"
                    )
                    return None
            else:
                logger.warning(f"未找到对应的run对象，run_id={run_id}")
                return None

        except Exception as e:
            logger.error(
                f"获取任务状态时发生错误，run_id={run_id}, 错误类型={type(e).__name__}, 错误信息={str(e)}",
                exc_info=True,
            )
            return None

    async def cancel_task(self, run_id: str, reason: str = "user_cancelled") -> bool:
        """通过memory检查任务状态并添加停止事件"""
        logger.info(f"开始取消任务，run_id={run_id}, reason={reason}")

        try:
            run = self.memory.get_run(run_id)
            logger.debug(f"从memory获取到run对象，run_id={run_id}, run对象类型={type(run)}")

            if run:
                logger.debug(f"run对象存在，run_id={run_id}, 属性列表={dir(run)}")

                if hasattr(run, "run_status"):
                    current_status = run.run_status
                    logger.info(f"任务当前状态，run_id={run_id}, status={current_status}")

                    if current_status == "running":
                        logger.info(f"任务正在运行，准备添加停止事件，run_id={run_id}")

                        # 构造RunStoppedEvent并写入memory
                        try:
                            from memory.events import RunStoppedEvent

                            # 获取session_id和run_id，提供默认值
                            session_id = getattr(run, "session_id", run_id)
                            actual_run_id = getattr(run, "run_id", run_id)

                            logger.debug(
                                f"构造停止事件，session_id={session_id}, run_id={actual_run_id}, role=system"
                            )

                            stop_event = RunStoppedEvent(
                                session_id=session_id, run_id=actual_run_id, role="system"
                            )

                            # 添加事件到memory
                            self.memory.add_event(stop_event)
                            logger.info(
                                f"成功添加停止事件到memory，session_id={session_id}, run_id={actual_run_id}, reason={reason}"
                            )
                            return True

                        except Exception as e:
                            logger.error(
                                f"添加停止事件到memory时发生错误，run_id={run_id}, 错误类型={type(e).__name__}, 错误信息={str(e)}",
                                exc_info=True,
                            )
                            return False
                    else:
                        logger.info(
                            f"任务不在运行状态，无法取消，run_id={run_id}, 当前状态={current_status}"
                        )
                        return False
                else:
                    logger.warning(
                        f"run对象缺少run_status属性，run_id={run_id}, 可用属性={[attr for attr in dir(run) if not attr.startswith('_')]}"
                    )
                    return False
            else:
                logger.warning(f"未找到对应的run对象，run_id={run_id}")
                return False

        except Exception as e:
            logger.error(
                f"取消任务时发生错误，run_id={run_id}, 错误类型={type(e).__name__}, 错误信息={str(e)}",
                exc_info=True,
            )
            return False


# 全局任务管理器实例
task_manager = AsyncTaskManager()


class AppListResponse(BaseModel):
    """应用列表响应"""

    applications: List[AppMetadata]


class ApplicationLoadError(Exception):
    """应用加载错误"""

    def __init__(self, app_name: str, reason: str):
        self.app_name = app_name
        self.reason = reason
        super().__init__(f"Failed to load application '{app_name}': {reason}")


class ApplicationRegistry:
    """应用注册表"""

    # 类级资源缓存：保存应用类，而不是实例
    _app_classes: Dict[str, Type[BaseApplication]] = {}
    _app_metadata: Dict[str, AppMetadata] = {}  # 预加载的应用元数据
    _initialized: bool = False

    @classmethod
    async def initialize(cls) -> None:
        """初始化应用注册表，预加载所有应用信息"""
        if cls._initialized:
            return

        logger.info("开始预加载应用信息...")
        try:
            await cls._preload_applications()
            cls._initialized = True
            logger.info(f"成功预加载 {len(cls._app_metadata)} 个应用")
        except Exception as e:
            # TODO: [ccc] 预加载应用失败，要增加告警
            logger.error("预加载应用失败: {}", str(e), exc_info=True)
            raise

    @classmethod
    async def _preload_applications(cls) -> None:
        """预加载所有应用的元数据和类"""
        app_dir = os.path.join(os.path.dirname(__file__), "..", "applications")

        if not os.path.exists(app_dir):
            logger.warning(f"应用目录不存在: {app_dir}")
            return

        for item in os.listdir(app_dir):
            module_path = os.path.join(app_dir, item)
            if os.path.isdir(module_path) and not item.startswith("__"):
                try:
                    await cls._load_application_from_module(item)
                except ApplicationLoadError as e:
                    logger.error("加载应用失败: {}", str(e))
                    raise
                except Exception as e:
                    logger.error("加载应用 '{}' 时发生未知错误: {}", item, str(e), exc_info=True)
                    raise

    @classmethod
    async def _load_application_from_module(cls, module_name: str) -> None:
        """从模块加载应用"""
        try:
            # 尝试导入应用模块
            module = importlib.import_module(f"src.applications.{module_name}.app")
        except ImportError as e:
            raise ApplicationLoadError(module_name, f"无法导入模块: {e}")

        # 查找应用类
        app_class = None
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if (
                inspect.isclass(attr)
                and issubclass(attr, BaseApplication)
                and attr != BaseApplication
            ):
                app_class = attr
                break

        if not app_class:
            raise ApplicationLoadError(module_name, "未找到BaseApplication子类")

        try:
            # 创建临时实例获取元数据
            temp_instance = app_class()
            metadata = temp_instance.metadata

            # 缓存应用类和元数据
            cls._app_classes[metadata.id] = app_class
            cls._app_metadata[metadata.id] = metadata

            logger.debug(f"成功加载应用: {metadata.id} - {metadata.name}")
        except Exception as e:
            raise ApplicationLoadError(module_name, f"创建应用实例失败: {e}")

    @classmethod
    async def get_app_class(cls, app_id: str) -> Type[BaseApplication]:
        """
        获取应用类

        Args:
            app_id: 应用ID

        Returns:
            应用类

        Raises:
            HTTPException: 如果应用不存在
        """
        # 确保已初始化
        if not cls._initialized:
            await cls.initialize()

        # 检查缓存
        if app_id in cls._app_classes:
            return cls._app_classes[app_id]

        raise HTTPException(status_code=404, detail=f"应用 '{app_id}' 不存在")

    @classmethod
    async def list_all_apps(cls) -> List[AppMetadata]:
        """列出所有应用"""
        # 确保已初始化
        if not cls._initialized:
            await cls.initialize()

        return list(cls._app_metadata.values())


def create_app_routes(app_id: str, app_metadata: AppMetadata) -> APIRouter:
    """
    为指定应用创建路由的工厂函数

    Args:
        app_id: 应用ID
        app_metadata: 应用元数据

    Returns:
        应用的API路由器
    """
    # 创建子路由
    app_router = APIRouter(prefix=f"/{app_id}", tags=[app_metadata.name])

    # 获取应用信息
    @app_router.get("/info", response_model=AppMetadata)
    async def get_app_info():
        """获取应用信息"""
        # 直接从预加载的元数据中获取，无需创建实例
        if app_id in ApplicationRegistry._app_metadata:
            return ApplicationRegistry._app_metadata[app_id]

        # 如果预加载的元数据不存在，创建临时实例获取
        try:
            AppClass = await ApplicationRegistry.get_app_class(app_id)
            temp_instance = AppClass()
            metadata = temp_instance.metadata
            await temp_instance.cleanup()
            return metadata
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取应用 '{app_id}' 信息失败: {str(e)}")

    # 处理消息
    @app_router.post("/message", response_model=MessageResponse)
    async def process_message(request: MessageRequest):
        """处理消息"""
        # 从请求上下文获取或生成session_id
        session_id = (request.context or {}).get("session_id") or f"session-{uuid.uuid4()}"
        trace_id = request.traceId or gen_trace_id()
        
        # 设置 trace_id 到日志上下文，确保从这里开始的全链路都能追踪
        set_trace_id(trace_id)

        app_instance = None
        request_id = request.requestId

        try:
            # 安全地处理context，防止为None的情况
            context = (request.context or {}).copy()
            context["session_id"] = session_id
            context["trace_id"] = trace_id
            # 添加resources到context中，确保request.resources不为None
            if request.resources is not None:
                context["resources"] = [resource.model_dump() for resource in request.resources]
            else:
                context["resources"] = []
            # 获取应用类
            AppClass = await ApplicationRegistry.get_app_class(app_id)

            # 创建新应用实例，主要通过context传递参数
            app_instance = AppClass(max_turns=25, context=context)
            
            logger.info(
                f"创建应用 '{app_id}' 的新实例 {request_id} 用于处理请求 | session_id={session_id}"
            )

            # 初始化实例（添加超时控制）
            try:
                init_success = await asyncio.wait_for(app_instance.initialize(), timeout=30.0)
                if not init_success:
                    raise RuntimeError("应用初始化返回False")
            except asyncio.TimeoutError:
                raise HTTPException(status_code=503, detail=f"应用 '{app_id}' 初始化超时")
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"应用 '{app_id}' 初始化失败: {str(e)}")

            logger.debug(f"应用 '{app_id}' 实例 {request_id} 初始化成功")

            # 处理消息
            result = await app_instance.process_message(request.message)
            
            # 记录处理完成
            logger.info(f"消息处理完成 | session_id={session_id} | app_id={app_id} | turns={result.get('turns', 0)}")
            
            return MessageResponse(
                response=result["response"],
                trace_id=result.get("trace_id"),
                session_id=result.get("session_id"),
                turns=result.get("turns", 0),
                resource_info=result.get("resource_info"),
            )

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error("处理应用 '%s' 消息时发生未知错误: %s", app_id, str(e), exc_info=True)
            raise HTTPException(status_code=500, detail=f"处理应用 '{app_id}' 消息失败")
        finally:
            # 不管成功还是异常，都确保清理实例
            if app_instance:
                try:
                    await asyncio.wait_for(app_instance.cleanup(), timeout=10.0)
                    logger.debug(f"应用 '{app_id}' 实例 {request_id} 已清理")
                except asyncio.TimeoutError:
                    logger.warning(f"应用 '{app_id}' 实例 {request_id} 清理超时")
                except Exception as e:
                    logger.error("清理应用 '{}' 实例 {} 时出错: {}", app_id, request_id, str(e))


    # 异步处理消息
    @app_router.post("/message/async", response_model=AsyncTaskResponse)
    async def process_message_async(request: MessageRequest):
        """异步处理消息，立即返回session_id"""
        # 生成或使用提供的session_id
        session_id = (request.context or {}).get("session_id") or f"session-{uuid.uuid4()}"
        trace_id = request.traceId or gen_trace_id()
        
        # 设置 trace_id 到日志上下文
        set_trace_id(trace_id)
        request_id = request.requestId

        async def background_process():
            # 在后台任务中也需要设置 trace_id，因为这是不同的 asyncio 任务
            set_trace_id(trace_id)
            
            app_instance = None
            try:
                # 安全地处理context，防止为None的情况
                context = (request.context or {}).copy()
                context["session_id"] = session_id
                context["trace_id"] = trace_id
                # 添加resources到context中，确保request.resources不为None
                if request.resources is not None:
                    context["resources"] = [resource.model_dump() for resource in request.resources]
                else:
                    context["resources"] = []

                logger.info(f"开始异步处理消息，session_id={session_id}, request_id={request_id}")

                # 创建新的应用实例用于后台处理
                app_class = await ApplicationRegistry.get_app_class(app_id)
                app_instance = app_class(max_turns=25, context=context)

                # 设置run_id，用于任务终止检查
                app_instance.run_id = trace_id

                # 在分布式部署中，不再需要注册任务到本地task_manager
                # 任务状态通过memory模块的get_run来判断
                logger.info(f"异步处理消息，session_id={session_id}, run_id={trace_id}")

                # 使用超时控制初始化
                init_success = await asyncio.wait_for(app_instance.initialize(), timeout=30.0)
                if init_success:
                    result = await app_instance.process_message(request.message)
                    logger.info(f"异步处理完成，session_id={session_id}")
                else:
                    logger.error(f"异步处理时应用初始化失败，session_id={session_id}")

            except asyncio.CancelledError:
                logger.info(f"任务被取消，session_id={session_id}")

            except asyncio.TimeoutError:
                logger.error("异步处理超时，session_id={}", session_id)
            except Exception as e:
                logger.exception(
                    f"异步处理应用 {app_id} 消息时发生未知错误: {str(e)}", exc_info=True
                )
            finally:
                # 在分布式部署中，不再需要清理本地task_manager
                # 清理应用实例
                if app_instance:
                    try:
                        await asyncio.wait_for(app_instance.cleanup(), timeout=10.0)
                    except Exception as e:
                        logger.error(
                            "清理异步处理应用实例时出错，session_id=%s, 错误信息=%s",
                            session_id,
                            str(e),
                        )

        asyncio.create_task(background_process())

        return AsyncTaskResponse(session_id=session_id, trace_id=trace_id, status="processing")

    @app_router.post("/terminate/async", response_model=TerminateResponse)
    async def terminate_async_task(request: TerminateRequest):
        """终止异步任务"""
        run_id = request.run_id
        request_id = request.requestId
        trace_id = request.traceId or gen_trace_id()
        
        # 设置 trace_id 到日志上下文（这里 run_id 实际上就是 trace_id）
        set_trace_id(trace_id)

        if not run_id:
            raise HTTPException(status_code=400, detail="run_id不能为空")

        try:
            # 通过memory获取任务信息
            run = task_manager.memory.get_run(run_id)
            if not run or not hasattr(run, "run_status") or run.run_status != "running":
                return TerminateResponse(
                    success=False, message=f"任务 {run_id} 不在运行中", run_id=run_id
                )

            # 直接调用task_manager的cancel_task方法
            try:
                logger.info(f"正在终止任务，run_id={run_id} request_id={request_id} ")
                cancelled = await task_manager.cancel_task(run_id, reason="user_cancelled")

                if cancelled:
                    logger.info(f"成功终止任务，run_id={run_id}")
                    return TerminateResponse(
                        success=True, message=f"任务 {run_id} 已成功终止", run_id=run_id
                    )
                else:
                    # 检查任务状态
                    status = await task_manager.get_task_status(run_id)
                    logger.info(f"任务状态: {status}")
                    if status == "completed":
                        return TerminateResponse(
                            success=False,
                            message=f"任务 {run_id} 已完成，无法终止",
                            run_id=run_id,
                        )
                    elif status == "cancelled":
                        return TerminateResponse(
                            success=False,
                            message=f"任务 {run_id} 已被取消",
                            run_id=run_id,
                        )
                    elif status == "failed":
                        return TerminateResponse(
                            success=False,
                            message=f"任务 {run_id} 已失败，无法终止",
                            run_id=run_id,
                        )
                    elif status == "running":
                        return TerminateResponse(
                            success=False,
                            message=f"任务 {run_id} 正在运行，无法终止",
                            run_id=run_id,
                        )
                    else:
                        return TerminateResponse(
                            success=False,
                            message=f"任务 {run_id} 不存在",
                            run_id=run_id,
                        )

            except Exception as e:
                logger.error("终止任务时发生错误: {}", str(e))
                return TerminateResponse(
                    success=False, message=f"终止任务时发生错误: {str(e)}", run_id=run_id
                )

        except Exception as e:
            logger.error("终止任务时发生错误，run_id={}, 错误={}", run_id, str(e))
            return TerminateResponse(
                success=False, message=f"终止任务时发生错误: {str(e)}", run_id=run_id
            )


    @app_router.get("/task/status", response_model=TaskStatusResponse)
    async def get_task_status(run_id: str):
        """获取任务状态"""

        try:
            status = await task_manager.get_task_status(run_id)
            return TaskStatusResponse(
                run_id=run_id,
                status=status or "not_found",
                message=f"任务状态: {status}" if status else "任务不存在",
            )
        except Exception as e:
            logger.error(f"获取任务状态时发生错误，run_id={run_id}, 错误={str(e)}")
            return TaskStatusResponse(
                run_id=run_id, status="error", message=f"获取状态时发生错误: {str(e)}"
            )

    # 应用关闭时的清理
    @app_router.on_event("shutdown")
    async def shutdown_event():
        """应用关闭时清理所有任务"""
        # 在分布式部署中，不再需要清理本地task_manager
        pass

    return app_router


async def register_app_routes(app: FastAPI):
    """
    注册应用API路由

    Args:  app: FastAPI应用实例
    """
    # 确保应用注册表已初始化
    await ApplicationRegistry.initialize()

    # 创建主路由
    router = APIRouter(prefix="/apps", tags=["应用"])

    # 列出所有应用
    @router.get("", response_model=AppListResponse)
    async def list_apps():
        """列出所有可用应用"""
        apps = await ApplicationRegistry.list_all_apps()
        return AppListResponse(applications=apps)

    # 为每个应用创建子路由
    for app_metadata in await ApplicationRegistry.list_all_apps():
        app_id = app_metadata.id

        # 使用工厂函数创建应用路由，避免闭包问题
        app_router = create_app_routes(app_id, app_metadata)

        # 添加子路由
        router.include_router(app_router)
        logger.info(f"已为应用 '{app_id}' 创建API路由")

    # 注册主路由
    app.include_router(router)
