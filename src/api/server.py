"""
API服务器
使用FastAPI实现的RESTful API服务
"""

import asyncio
import os
from contextlib import asynccontextmanager

import logfire
import uvicorn
from aliyunaklesscredprovider.core import AklessCredproviderFactory
from aliyunaklesscredprovider.core.config import CredentialProviderConfig
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, PlainTextResponse

from src.api import lifecycle

from ..common.config import settings
from ..common.logging import log_level, logfire, logger, setup_waiy_tracing
from .apps import register_app_routes
from .models import ErrorResponse

# 初始化阿里云AKless SDK方法
def init_aliyun_akless_sdk():
    try:
        logger.info(f"初始化阿里云AKless SDK...")
        AklessCredproviderFactory.get_opensdk_v2_credential_with_config(
            CredentialProviderConfig(
            ram_role_arn=settings.service.ram_role,
            region_id=settings.service.region_id,
                app_name=settings.service.app_name, 
                app_env=settings.service.app_env)
        )
        logger.info(f"初始化阿里云AKless SDK成功")
    except Exception as e:
        logger.error(f"初始化阿里云AKless SDK失败: {e}")
        raise e

# 服务在线状态标志
is_online: bool = False

"""知识点：
FastAPI启动时，会调用lifespan函数
执行启动事件代码（记录日志、设置跟踪、注册路由）
遇到yield后暂停，此时应用正常运行
当应用接收到关闭信号时，继续执行yield后的代码
执行关闭事件代码（记录日志、清理资源）
"""


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan事件管理器 - 处理应用的启动和关闭事件
    """
    # 启动事件
    logger.info("API服务启动")

    # 注册信号处理器，用于优雅关闭
    lifecycle.register_signal()

    # 设置跟踪
    setup_waiy_tracing()

    # 注册所有应用路由
    logger.info("正在注册所有可用应用")
    try:
        # 列出所有可用应用
        from .apps import ApplicationRegistry

        apps = await ApplicationRegistry.list_all_apps()
        logger.info(f"发现 {len(apps)} 个可用应用: {', '.join([app.id for app in apps])}")

        await register_app_routes(app)

        # 验证路由注册情况
        app_routes = [
            route for route in app.routes if getattr(route, "path", "").startswith("/apps")
        ]
        logger.info(
            f"已注册 {len(app_routes)} 个API路由: {', '.join([route.path for route in app_routes])}"
        )
    except Exception as e:
        logger.error(f"注册应用路由时出错: {e}", exc_info=True)

    yield  # 服务运行中
    """知识点：
    yield语句是异步上下文管理器的关键部分：
    - 在yield之前的代码是应用启动时执行的
    - yield之后的代码是应用关闭时执行的
    - 当FastAPI应用运行时，执行会在这个点暂停，直到应用关闭
    """

    # 关闭事件
    logger.info("API服务关闭")

    # 在请求作用域实例模式下，所有资源都由依赖注入框架自动清理
    logger.info("请求作用域实例已自动清理")


# 创建FastAPI应用
app = FastAPI(
    title="WAIY智能体框架API",
    description="基于多智能体协同的PC自动化助手API",
    version="0.1.0",
    lifespan=lifespan,
)

# 自动追踪FastApi调用
logfire.instrument_fastapi(app)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.api.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 添加请求超时中间件
@app.middleware("http")
async def timeout_middleware(request: Request, call_next):
    """请求超时中间件"""
    try:
        # 设置超时时间（秒）
        timeout = settings.api.request_timeout if hasattr(settings.api, "request_timeout") else 60

        # 使用asyncio.wait_for设置超时
        return await asyncio.wait_for(call_next(request), timeout=timeout)
    except asyncio.TimeoutError:
        logger.error(f"请求处理超时: {request.url}")
        return JSONResponse(
            status_code=504,
            content=ErrorResponse(
                success=False, error="请求处理超时", error_code="REQUEST_TIMEOUT"
            ).model_dump(),
        )


# 添加全局异常处理
@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"API错误: {exc}", exc_info=True)

    # 如果是HTTPException，直接使用它的详情
    if isinstance(exc, HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content=ErrorResponse(
                success=False, error=str(exc.detail), error_code=str(exc.status_code)
            ).model_dump(),
        )

    # 对于其他异常，返回500错误
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            success=False, error=str(exc), error_code="INTERNAL_SERVER_ERROR"
        ).model_dump(),
    )


@app.get("/")
async def root():
    """API根路径处理器"""
    return {
        "name": "WAIY智能体框架API",
        "version": "0.1.0",
        "description": "基于多智能体协同的PC自动化助手API",
        "docs_url": "/docs",
        "web_ui_url": "/web-ui",  # 添加Web UI链接
    }


@app.get("/status.taobao", response_class=PlainTextResponse)
async def status_taobao():
    """
    服务心跳检测接口
    符合淘宝应用规范的心跳检测接口

    如果check返回None代表第一次请求，调用online函数，返回online文件内容
    如果check返回online文件内容，说明已经上线
    """
    global is_online
    result = lifecycle.check()

    if not result and not is_online:
        # 第一次请求，标记为在线
        lifecycle.online()
        is_online = True
        return lifecycle.check() or "success"
    else:
        if result:
            return result
        else:
            # 文件不存在但状态是在线，尝试重新创建
            if is_online:
                lifecycle.online()
                return lifecycle.check() or "success"
            return "not found", 404


def run_api_server():
    """
    运行API服务器
    """
    init_aliyun_akless_sdk()
    
    uvicorn.run(
        "src.api.server:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.debug,
        log_level=log_level.lower()
    )


if __name__ == "__main__":
    run_api_server()
