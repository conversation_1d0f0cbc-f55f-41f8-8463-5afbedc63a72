# AIPPT

基于OpenAI Agents框架的专业PPT大纲生成应用，通过自然语言交互帮助用户快速创建高质量的演示文稿大纲。

## 功能特点

### 🎯 核心功能
- **智能大纲生成**: 基于用户需求自动生成结构化PPT大纲
- **多维度质量评估**: 从结构、内容、逻辑等多个维度评估大纲质量  
- **信息搜索集成**: 支持阿里云搜索等多种信息源
- **个性化定制**: 支持目标受众、演示时长等个性化设置

### 🚀 技术特点
- **单智能体架构**: 整合多智能体功能的高效单智能体版本
- **结构化输出**: 标准Markdown格式的PPT大纲输出
- **质量保证**: 内置质量评估和优化建议机制
- **易于集成**: 基于WAIY框架，易于扩展和集成

## PPT大纲格式

生成的PPT大纲采用标准Markdown格式：

```markdown
# PPT标题

## 第一章：章节标题
### 1.1 内容页标题
- 正文内容要点1
- 正文内容要点2

#### 子标题
- 详细内容1
- 详细内容2

## 第二章：章节标题
### 2.1 内容页标题
- 正文内容要点1
- 正文内容要点2
```