"""
AIPPT应用
专注于PPT大纲生成功能
"""

from agents import ModelSettings

from src.applications.base import BaseApplication
from src.common.logging import logger
from src.common.models import AppMetadata
from src.core.plugins import aliyun_search
from .prompts import AIPPT_INSTRUCTIONS
from ...common.config.llm_config import llm_config_global

class AIPPTApp(BaseApplication):
    """AIPPT应用类"""

    @property
    def metadata(self) -> AppMetadata:
        """获取应用元数据"""
        return AppMetadata(
            id="ai_ppt",
            name="AI PPT",
            version="1.0.0",
            description="一键搞定PPT",
            tags=["PPT", "大纲", "搜索"],
        )

    async def setup_agents(self) -> None:
        """设置智能体"""
        logger.info("正在初始化AIPPT智能体...")

        # 创建主智能体
        self.primary_agent = await self.create_agent(
            name="aippt_generator",
            instructions=AIPPT_INSTRUCTIONS,
            is_primary=True,
            tools=[aliyun_search],  # 集成阿里云搜索
            model=llm_config_global.get_model("main", self.model_setting.model_level),
            model_settings=ModelSettings(
                temperature=0.5,  # 较低的创造性，确保输出准确
                max_tokens=4096,  # 足够的响应长度用于详细大纲
            ),
        )

        logger.info("AIPPT智能体初始化完成") 