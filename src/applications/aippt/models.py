"""
AIPPT数据模型
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class PPTRequirements(BaseModel):
    """从用户输入提取的PPT需求信息"""
    topic: str = Field(..., description="主题")
    page_range: str = Field(default="10-15页", description="页数")
    tone: str = Field(default="专业", description="语气")
    audience: str = Field(default="大众", description="受众")
    scenario: str = Field(default="通用", description="场景")
    language: str = Field(default="中文", description="语言")


class AIPPTRequest(BaseModel):
    """AIPPT请求数据模型"""
    user_input: str = Field(..., description="用户输入的PPT需求描述")
    max_search_results: int = Field(default=10, description="最大搜索结果数量")


class PPTOutline(BaseModel):
    """PPT大纲数据模型"""
    title: str = Field(..., description="PPT标题")
    outline_content: str = Field(..., description="大纲内容(Markdown格式)")
    requirements: PPTRequirements = Field(..., description="解析的PPT需求")
    key_points: List[str] = Field(..., description="关键要点列表")
    estimated_pages: int = Field(..., description="预计页数")


class AIPPTResponse(BaseModel):
    """AIPPT响应数据模型"""
    success: bool = Field(..., description="是否成功")
    outline: Optional[PPTOutline] = Field(None, description="生成的PPT大纲")
    error_message: Optional[str] = Field(None, description="错误信息")
    search_summary: Optional[str] = Field(None, description="搜索结果摘要") 