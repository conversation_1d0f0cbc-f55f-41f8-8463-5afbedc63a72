"""
客户服务应用主类
实现基于多智能体协作的客户服务系统
"""

import time
from typing import Any, Dict, Optional

from agents import ModelSettings, function_tool

from src.applications.base import BaseApplication

from ...common.models import AppMetadata
from ...core.plugins import aliyun_search, get_task_history
from .prompts import (
    REFUND_INSTRUCTIONS,
    SALES_INSTRUCTIONS,
    SUPPORT_INSTRUCTIONS,
    TRIAGE_INSTRUCTIONS,
)
from ...common.config.llm_config import llm_config_global


class CustomerServiceApp(BaseApplication):
    """客户服务应用 - 使用handoff机制的多智能体应用"""

    # 模拟产品数据
    products = {
        "p001": {"name": "高级笔记本电脑", "price": 7999, "in_stock": True},
        "p002": {"name": "无线耳机", "price": 799, "in_stock": False},
        "p003": {"name": "智能手表", "price": 1299, "in_stock": True},
    }

    @property
    def metadata(self) -> AppMetadata:
        """获取应用元数据"""
        return AppMetadata(
            id="customer_service",
            name="客户服务系统",
            version="1.0.0",
            description="多智能体客户服务系统",
            tags=["客服", "多智能体"],
            mcp_servers=["file_system", "browser"],
        )

    async def setup_agents(self) -> None:
        """设置所有智能体"""
        # 顺序很重要：先创建被依赖的智能体，再创建依赖它们的智能体

        # 1. 创建专家智能体
        sales_agent = await self.create_agent(
            name="sales",
            instructions=SALES_INSTRUCTIONS,
            tools=[self.get_product_info, self.place_order],
            mcp_servers=["file_system"],
            model=llm_config_global.get_model("main", self.model_setting.model_level),
            model_settings=ModelSettings(temperature=0.5),
        )

        support_agent = await self.create_agent(
            name="support",
            instructions=SUPPORT_INSTRUCTIONS,
            tools=[aliyun_search, self.get_troubleshooting_guide],
            mcp_servers=["browser"],
            model=llm_config_global.get_model("main", self.model_setting.model_level),
            model_settings=ModelSettings(temperature=0.5),
        )

        refund_agent = await self.create_agent(
            name="refund",
            instructions=REFUND_INSTRUCTIONS,
            tools=[self.process_refund, self.check_order_status],
            mcp_servers=["file_system"],
            model=llm_config_global.get_model("main", self.model_setting.model_level),
            model_settings=ModelSettings(temperature=0.5),
        )

        # 2. 创建分诊智能体 (引用上面已创建的智能体)
        self.primary_agent = await self.create_agent(
            name="triage",
            instructions=TRIAGE_INSTRUCTIONS,
            is_primary=True,
            handoffs=[sales_agent, support_agent, refund_agent],
            tools=[get_task_history],
            model=llm_config_global.get_model("main", self.model_setting.model_level),
            model_settings=ModelSettings(temperature=0.5),
        )

    # 工具函数定义
    @function_tool
    async def process_refund(self, order_id: str) -> str:
        """处理订单退款"""
        return f"已为订单 {order_id} 处理退款，退款将在3-5个工作日到账。"

    @function_tool
    async def check_order_status(self, order_id: str) -> Dict[str, Any]:
        """查询订单状态"""
        # 模拟查询结果
        return {
            "order_id": order_id,
            "status": "已发货",
            "payment": "已支付",
            "shipping": "顺丰快递",
            "tracking": "SF123456789",
        }

    @function_tool
    async def get_product_info(self, product_id: str) -> Dict[str, Any]:
        """获取产品信息"""

        return self.products.get(product_id, {"error": "产品不存在"})

    @function_tool
    async def place_order(self, product_id: str, quantity: int = 1) -> Dict[str, Any]:
        """下单购买产品"""
        product = self.products.get(product_id, {"error": "产品不存在"})
        if "error" in product:
            return {"success": False, "message": product["error"]}

        if not product["in_stock"]:
            return {"success": False, "message": "产品缺货"}

        # 模拟下单
        order_id = f"ORD-{product_id}-{int(time.time())}"
        return {
            "success": True,
            "order_id": order_id,
            "product": product["name"],
            "quantity": quantity,
            "total_price": product["price"] * quantity,
        }

    @function_tool
    async def get_troubleshooting_guide(self, product_type: str, issue: str) -> str:
        """获取产品故障排除指南"""
        guides = {
            "laptop": {
                "won't_start": "1. 检查电源适配器连接\n2. 尝试移除电池后重新启动\n3. 按住电源键30秒后再启动",
                "blue_screen": "1. 记录错误代码\n2. 重启电脑\n3. 若仍出现问题，尝试系统恢复",
                "overheating": "1. 确保通风口没有堵塞\n2. 使用散热垫\n3. 清理内部灰尘",
            },
            "phone": {
                "battery_drain": "1. 检查后台运行应用\n2. 降低屏幕亮度\n3. 关闭不必要的连接功能",
                "slow_performance": "1. 清理缓存\n2. 关闭不必要的应用\n3. 检查存储空间",
                "not_charging": "1. 更换充电线\n2. 清理充电口\n3. 检查充电适配器",
            },
        }

        if product_type in guides and issue in guides[product_type]:
            return guides[product_type][issue]
        else:
            return "抱歉，未找到相关故障排除指南。请联系我们的技术支持人员获取更多帮助。"
