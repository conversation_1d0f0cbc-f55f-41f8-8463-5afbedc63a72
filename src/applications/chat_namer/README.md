# Chat Namer App - 会话标题生成器

## 概述

Chat Namer 是一个专门用于为会话生成简洁标题的智能体应用。它能够根据用户的首条消息内容，自动判断语言并生成合适的标题，便于前端显示会话列表。

## 功能特性

- **智能语言检测**：自动识别用户输入的主要语言
- **动态语言适配**：根据检测结果使用相应语言生成标题
- **默认中文支持**：对于无法判断语言的输入默认使用中文
- **输入验证**：处理空输入、过长输入等边界情况
- **输出优化**：自动清理标题格式，确保显示效果

## 使用方法

### 命令行使用

```bash
# 启动应用
python main.py --app chat_namer

# 或交互式选择
python main.py
```

### API 调用

```bash
# 启动API服务
python api.py

# 发送请求
curl -X POST "http://localhost:8000/apps/chat_namer/message" \
     -H "Content-Type: application/json" \
     -d '{
       "message": "你好，我想了解一下你们的产品价格"
     }'
```

### 响应示例

```json
{
  "response": "产品价格咨询",
  "trace_id": "trace-xxx",
  "session_id": "session-xxx",
  "turns": 1
}
```

## 语言处理规则

### 使用用户语言的情况
- 输入内容明确且充分地使用某种特定语言（英语、法语、日语等）

### 默认使用中文的情况
- 语言模糊或混合使用
- 输入包含代码、数学公式等非语言内容
- 输入过短无法可靠判断语言（如 "hi", "ok"）
- 输入主要包含特殊字符、数字或符号

## 标题生成示例

| 输入类型 | 示例输入 | 生成标题 |
|---------|----------|----------|
| 中文商务咨询 | "你好，我想问一下你们这里的笔记本电脑都有哪些型号..." | 咨询笔记本型号价格和保修 |
| 英文政策询问 | "Hello, I'd like to know more about the return policy..." | Online Return Policy Inquiry |
| 代码问题 | "```python\nfor i in range(10):\n    print(i)\n```\nwhat's wrong?" | Python代码调试问题 |
| 简短问候 | "hi" | 打招呼 |
| 混合语言 | "Hello 你好, I want to 购买 some products" | 产品购买咨询 |

## 技术特性

- **模型优化**：使用fast_model + 低温度设置确保一致性
- **长度控制**：输出限制为3-10个单词（中文6-15字符）
- **输入限制**：单次输入最大2000字符
- **错误处理**：完善的异常处理和降级策略
- **输出清理**：自动移除前缀、引号和多余空白

## 性能优化

- **低延迟**：使用快速模型，平均响应时间 < 1秒
- **成本控制**：限制max_tokens为50，降低调用成本
- **健壮性**：完善的输入验证和错误处理机制

## 适用场景

- 聊天应用的会话列表显示
- 客服系统的对话分类
- 知识库问答的主题生成
- 任何需要快速生成内容摘要的场景 