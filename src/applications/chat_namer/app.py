"""
Chat Namer App
"""

import re
from typing import Any, Dict

from agents import ModelSettings

from src.applications.base import BaseApplication
from src.applications.chat_namer.prompts import NAMER_INSTRUCTIONS
from src.common.logging import logger
from src.common.models import AppMetadata, Visibility
from ...common.config.llm_config import llm_config_global

class ChatNamerApp(BaseApplication):
    """
    An application to generate a title for a chat session based on the first message.
    """

    @property
    def metadata(self) -> AppMetadata:
        """Get application metadata."""
        return AppMetadata(
            id="chat_namer",
            name="会话标题生成器",
            version="1.0.0",
            description="根据会话的初始消息生成一个简洁的标题。",
            tags=["utility", "chat"],
            visibility=Visibility.PRIVATE,  # 设置为私有应用，不对前端显示
        )

    async def setup_agents(self) -> None:
        """Set up the agent for this application."""
        self.primary_agent = await self.create_agent(
            name="title_generator",
            instructions=NAMER_INSTRUCTIONS,
            is_primary=True,
            model=llm_config_global.get_model("fast", self.model_setting.model_level),
            model_settings=ModelSettings(
                temperature=0.1,  # 低温度确保一致性
                max_tokens=50,  # 限制输出长度，节省成本
            ),
        )

    async def _do_process_message(
        self, message: str
    ) -> Dict[str, Any]:
        """
        处理用户消息，生成会话标题

        Args:
            message: 用户的输入消息

        Returns:
            包含生成标题的响应
        """
        # 输入验证
        if not message or not message.strip():
            return {
                "response": "新会话",
                "turns": 1,
            }

        # 输入长度限制（避免过长输入影响性能）
        if len(message) > 2000:
            message = message[:2000] + "..."
            logger.info("输入消息过长，已截断处理")

        try:
            # 调用父类方法处理消息
            result = await super()._do_process_message(message)

            # 输出后处理
            title = result.get("response", "").strip()
            title = self._clean_title(title)

            # 验证标题质量
            if not title or len(title) < 2:
                title = "新会话"
                logger.warning("生成的标题质量不佳，使用默认标题")

            result["response"] = title
            return result

        except Exception as e:
            logger.error(f"生成标题时发生错误: {e}")
            return {
                "response": "新会话",
                "turns": 1,
            }

    def _clean_title(self, title: str) -> str:
        """
        清理和验证生成的标题

        Args:
            title: 原始标题

        Returns:
            清理后的标题
        """
        if not title:
            return ""

        # 移除可能的前缀
        title = re.sub(r"^(标题[：:]|Title[：:]|主题[：:])", "", title).strip()

        # 移除引号
        title = title.strip('"\'""' "")

        # 移除多余的空白字符
        title = re.sub(r"\s+", " ", title).strip()

        # 长度限制
        if len(title) > 30:
            title = title[:30] + "..."

        return title
