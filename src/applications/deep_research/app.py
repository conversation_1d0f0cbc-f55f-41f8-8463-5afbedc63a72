from __future__ import annotations

import asyncio
import json
import time
from typing import Any, Dict, List, Optional, override

from agents import ModelSettings, RunConfig, Runner, custom_span, gen_trace_id, trace
from openai import models
from rich.console import Console

from src.applications.deeper_research.agents.utils.parse_output import create_type_parser

from ...common.config import DATA_DIR
from ...common.config.llm_config import (
    llm_config_global,
    model_supports_structured_output,
)
from ...common.logging import logger
from ...common.models import AppMetadata
from ...core.agent.baseclass import WaiyRunner
from ...core.plugins import aliyun_search, web_search
from ..base import BaseApplication
from .prompts.planner_agent import PLANNER_PROMPT, WebSearchItem, WebSearchPlan
from .prompts.search_agent import SEARCHER_PROMPT
from .prompts.web_agent import INSTRUCTIONS as WEB_PROMPTS
from .prompts.writer_agent import WRITER_PROMPT, ReportData


class ResearchManager(BaseApplication):
    """深度研究应用 - 提供多智能体协作的通用研究服务"""

    @property
    def metadata(self) -> AppMetadata:
        """获取应用元数据"""
        return AppMetadata(
            id="deep-research",
            name="Deep Researcher",
            version="1.0.0",
            description="深度研究工具",
            tags=["互联网", "研究"],
            mcp_servers=["file_system", "python", "browser"],
        )

    async def setup_agents(self) -> None:
        """初始化所有智能体"""
        await self.create_agent(
            name="PlannerAgent",
            instructions=PLANNER_PROMPT,
            output_type=(
                WebSearchPlan if model_supports_structured_output(llm_config_global.get_model("main", self.model_setting.model_level)) else None
            ),
            output_parser=(
                create_type_parser(WebSearchPlan)
                if not model_supports_structured_output(llm_config_global.get_model("main", self.model_setting.model_level))
                else None
            ),
            model=llm_config_global.get_model("main", self.model_setting.model_level),
            model_settings=ModelSettings(
                temperature=0.0,
            ),
        )

        await self.create_agent(
            name="SearchAgent",
            instructions=SEARCHER_PROMPT,
            tools=[aliyun_search],
            model=llm_config_global.get_model("fast", self.model_setting.model_level),
            model_settings=ModelSettings(
                tool_choice="required",
                temperature=0.0,
            ),
        )

        await self.create_agent(
            name="WriterAgent",
            instructions=WRITER_PROMPT,
            model=llm_config_global.get_model("reasoning", self.model_setting.model_level),
            output_type=(
                ReportData
                if model_supports_structured_output(llm_config_global.get_model("reasoning", self.model_setting.model_level))
                else None
            ),
            output_parser=(
                create_type_parser(ReportData)
                if not model_supports_structured_output(llm_config_global.get_model("reasoning", self.model_setting.model_level))
                else None
            ),
        )

        await self.create_agent(
            name="WebAgent",
            instructions=WEB_PROMPTS,
            model=llm_config_global.get_model("code", self.model_setting.model_level),
            model_settings=ModelSettings(max_tokens=32768, temperature=0.0),
        )

    @override
    async def _do_process_message(
        self, message: str
    ) -> Dict[str, Any]:
        """处理用户消息，执行完整的研究流程"""
        span = custom_span(name="WebAgent")
        span.start(mark_as_current=True)
        logger.info("Financial research starting, trace_id=%s", self.context.get("trace_id"))

        # 执行研究流程
        search_plan = await self._plan_searches(message)
        search_results = await self._perform_searches(search_plan)
        report = await self._write_report(message, search_results)

        result = f"摘要：\n{report.short_summary} \n以下为报告全文： \n{report.markdown_report}\n以下为后续问题： \n{report.follow_up_questions}"
        web_report = await WaiyRunner.run(self.all_agents["WebAgent"], result)

        self._save_report(report)

        # 返回结果
        self._display_final_report(report)

        span.end(mark_as_current=True)

        return {
            "response": json.dumps(
                {"report": result, "web_report": web_report.final_output_as(str)}
            ),
            "follow_up_questions": report.follow_up_questions,
        }

    async def _plan_searches(self, query: str) -> WebSearchPlan:
        """制定搜索计划"""
        logger.info("Planning searches...")

        result = await WaiyRunner.run(self.all_agents["PlannerAgent"], f"Query: {query}")

        search_count = len(result.final_output.searches)
        logger.info(f"Will perform {search_count} searches")

        return result.final_output_as(WebSearchPlan)

    async def _perform_searches(self, search_plan: WebSearchPlan) -> List[str]:
        """执行所有搜索任务"""
        with custom_span("Search the web"):
            logger.info("Web Searching...")

            # 创建所有搜索任务
            tasks = [self._search(item) for item in search_plan.searches]

            # 并行执行所有搜索任务
            results = []
            completed = 0
            total = len(tasks)

            for result in await asyncio.gather(*tasks):
                if result:
                    results.append(result)
                completed += 1
                logger.info(f"Search {completed}/{total} completed")

            logger.info("web search completed")
            return results

    async def _search(self, item: WebSearchItem) -> str | None:
        """执行单个搜索任务"""
        try:
            input_query = f"Search term: {item.query}\nReason for searching: {item.reason}"
            result = await WaiyRunner.run(
                self.all_agents["SearchAgent"],
                input_query,
            )
            return str(result.final_output)
        except Exception:
            return None

    async def _write_report(self, query: str, search_results: List[str]) -> ReportData:
        """根据搜索结果撰写报告"""
        logger.info("Thinking about report...")

        input_data = f"{WRITER_PROMPT} {query}\n初步研究结果: {search_results}"
        result = await WaiyRunner.run(
            self.all_agents["WriterAgent"],
            input_data,
        )

        logger.info("writer think completed")
        return result.final_output_as(ReportData)

    def _display_final_report(self, report: ReportData) -> None:
        """显示最终报告"""
        final_report = f"Report summary\n\n{report.short_summary}"
        logger.info(f"final_report {final_report}")

        logger.info("=====REPORT=====")
        logger.info(f"Report: {report.markdown_report}")
        logger.info("=====FOLLOW UP QUESTIONS=====")
        follow_up_questions = "\n".join(report.follow_up_questions)
        logger.info(f"Follow up questions: {follow_up_questions}")

    @staticmethod
    def _save_report(report: ReportData) -> None:
        """保存报告到指定文件"""
        file_path = DATA_DIR / f"report-dr({report.short_summary[:10]}).mdx"
        try:
            with open(file_path, "w", encoding="utf-8") as file:
                file.write(report.markdown_report)
            logger.info(f"Report saved successfully to {file_path}")
        except Exception as e:
            logger.error(f"Failed to save report: {e}")
