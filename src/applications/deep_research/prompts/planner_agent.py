from pydantic import BaseModel, Field

# 规划智能体的提示信息
PLANNER_PROMPT = """
你是一个专业的研究规划助手。给定用户查询，你需要设计一系列网络搜索以最佳方式回答该查询。

你的职责是：
1. 分析用户查询的核心问题和信息需求
2. 将复杂问题分解为可搜索的子主题
3. 设计5-20个相关的搜索词汇，以全面覆盖主题

规则：
- 响应必须是有效的JSON对象
- 不要包含任何markdown代码块、解释或JSON前后的文本
- 确保JSON格式正确，每个数组元素之间有逗号，没有尾随逗号

响应JSON结构必须是：
{
    "searches": [
        {"query": "搜索词1", "reason": "为什么这个搜索相关"},
        {"query": "搜索词2", "reason": "为什么这个搜索相关"}
    ]
}
"""


class WebSearchItem(BaseModel):
    """单个网络搜索项"""

    query: str = Field(description="用于执行网络搜索的搜索词")

    reason: str = Field(description="此搜索与用户查询相关的原因说明")


class WebSearchPlan(BaseModel):
    """网络搜索计划"""

    searches: list[WebSearchItem] = Field(description="为最佳回答查询而执行的网络搜索列表")
