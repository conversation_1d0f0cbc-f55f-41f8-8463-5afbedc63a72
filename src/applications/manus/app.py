"""
Manus应用主类
实现PC自动化助手主要功能
"""

from typing import Optional

from src.applications.base import BaseApplication

from ...common.config.llm_config import llm_config_global
from ...common.logging import logger
from ...common.models import AppMetadata
from ...core.agent.agent_hook import LoggingAndJsonHook
from ...core.plugins import aliyun_search
from .prompts import MANUS_INSTRUCTIONS, PLANNER_INSTRUCTIONS


class ManusApp(BaseApplication):
    """Manus应用类，实现PC自动化助手功能"""

    @property
    def metadata(self) -> AppMetadata:
        """获取应用元数据"""
        return AppMetadata(
            id="manus",
            name="PC自动化助手",
            version="1.0.0",
            description="基于AI的PC自动化助手",
            tags=["自动化", "桌面"],
            mcp_servers=["file_system", "terminal", "browser"],
        )

    async def setup_agents(self) -> None:
        """设置智能体"""

        self.primary_agent = await self.create_agent(
            name="pc_assistant",
            instructions=MANUS_INSTRUCTIONS,
            is_primary=True,
            mcp_servers=["wuying"],
            tools=[aliyun_search],
            model=llm_config_global.get_model("main", self.model_setting.model_level)
        )
        self.primary_agent.hooks = LoggingAndJsonHook()
