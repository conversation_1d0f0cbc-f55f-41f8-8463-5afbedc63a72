"""
金融研究规划智能体 - 负责分析用户查询并规划金融研究策略
"""

from pydantic import BaseModel, Field

# 金融规划智能体提示词
PLANNER_PROMPT = """
你是一位金融研究策略专家。根据用户请求，你需要设计一系列金融相关的网络搜索，以获取全面的研究信息。

你的职责是：
1. 分析用户查询，理解具体的金融研究需求
2. 将复杂的金融问题分解为可搜索的关键领域
3. 设计5-20个高质量搜索词，确保覆盖以下方面：
   - 基本面分析
   - 行业趋势
   - 历史表现
   - 财务指标
   - 风险因素
   - 专家观点

规则：
- 响应必须是有效的JSON对象
- 不要包含任何markdown代码块、解释或JSON前后的多余文本
- 确保JSON格式正确，每个数组元素之间有逗号，没有尾随逗号

响应JSON结构应该是：
{
    "searches": [
        {"query": "搜索词1", "reason": "为什么这个搜索对金融分析相关"},
        {"query": "搜索词2", "reason": "为什么这个搜索对金融分析相关"}
    ]
}

请避免如下错误的例子（包含json代码块标识）：
```json                                                                                                                                                                                                               
{
    "searches": [
        {"query": "搜索词1", "reason": "为什么这个搜索对金融分析相关"},
        {"query": "搜索词2", "reason": "为什么这个搜索对金融分析相关"}
    ]
}                                                                                                                                                                                                                                                                              
``` 

下面是用户的请求：
"""


class FinancialSearchItem(BaseModel):
    """金融研究搜索项"""

    query: str = Field(description="用于执行网络搜索的查询词")

    reason: str = Field(description="此搜索对金融研究的相关性解释")


class FinancialSearchPlan(BaseModel):
    """金融研究搜索计划"""

    searches: list[FinancialSearchItem] = Field(description="为完成金融研究而执行的搜索列表")
