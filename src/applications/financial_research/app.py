from __future__ import annotations

import asyncio
import time
from typing import Any, Dict, List, Optional, Tuple

from agents import ModelSettings, RunResult, custom_span, gen_trace_id, trace
from rich.console import Console

from ... import aliyun_search
from ...common.config import DATA_DIR, settings
from ...common.config.llm_config import llm_config_global
from ...common.logging import logger
from ...common.models import AppMetadata
from ...core.agent.agent_hook import Logging<PERSON>nd<PERSON>sonHook
from ...core.agent.baseclass import W<PERSON>yRunner
from ..base import BaseApplication
from .prompts.financials_agent import FINANCIALS_PROMPT, FinAnalysisSummary
from .prompts.planner_agent import PLANNER_PROMPT, FinancialSearchItem, FinancialSearchPlan
from .prompts.risk_agent import RISK_PROMPT, RiskAnalysisSummary
from .prompts.search_agent import SEARCH_PROMPT
from .prompts.verifier_agent import VERIFIER_PROMPT, VerificationResult
from .prompts.writer_agent import WRITER_PROMPT, FinancialReportData


class FinancialResearchManager(BaseApplication):
    """金融研究管理器 - 协调规划、搜索、分析、写作和验证的完整工作流"""

    @property
    def metadata(self) -> AppMetadata:
        """获取应用元数据"""
        return AppMetadata(
            id="fin-research",
            name="Financial Researcher",
            version="1.0.0",
            description="金融研究工具",
            tags=["金融", "研究"],
            mcp_servers=["file_system", "python", "browser"],
        )

    async def setup_agents(self) -> None:
        """初始化所有需要的智能体"""
        await self.create_agent(
            name="FinAnalystAgent",
            instructions=FINANCIALS_PROMPT,
            output_type=FinAnalysisSummary,
            model=llm_config_global.get_model("main", self.model_setting.model_level),
        )

        await self.create_agent(
            name="RiskAnalystAgent",
            instructions=RISK_PROMPT,
            output_type=RiskAnalysisSummary,
            model=llm_config_global.get_model("main", self.model_setting.model_level),
        )
        planner = await self.create_agent(
            name="PlannerAgent",
            instructions=PLANNER_PROMPT,
            output_type=FinancialSearchPlan,
            model=llm_config_global.get_model("reasoning", self.model_setting.model_level),
        )
        planner.hooks = LoggingAndJsonHook()

        await self.create_agent(
            name="FinSearchAgent",
            instructions=SEARCH_PROMPT,
            tools=[aliyun_search],
            mcp_servers=["browser"],
            model=llm_config_global.get_model("main", self.model_setting.model_level),
            model_settings=ModelSettings(tool_choice="auto"),
        )

        await self.create_agent(
            name="VerificationAgent",
            instructions=VERIFIER_PROMPT,
            output_type=VerificationResult,
            model=llm_config_global.get_model("main", self.model_setting.model_level),
        )
        writer_gent = await self.create_agent(
            name="FinWriterAgent",
            instructions=WRITER_PROMPT,
            output_type=FinancialReportData,
            model=llm_config_global.get_model("reasoning", self.model_setting.model_level),
        )
        writer_gent.hooks = LoggingAndJsonHook()

    async def process_message(self, message: str) -> Dict[str, Any]:
        """处理用户消息，执行完整的金融研究流程"""

        logger.info("Financial research starting, trace_id=%s", self.context.get("trace_id"))

        # 执行研究流程
        search_plan = await self._plan_searches(message)
        search_results = await self._perform_searches(search_plan)
        report = await self._write_report(message, search_results)
        verification = await self._verify_report(report)
        self._save_report(report)

        # 输出结果
        self._display_final_report(report)

        return {
            "response": report.short_summary,
            "full_report": report.markdown_report,
            "follow_up_questions": report.follow_up_questions,
            "verification": verification,
        }

    async def _plan_searches(self, query: str) -> FinancialSearchPlan:
        """规划搜索策略"""
        logger.info("Planning searches...")

        result = await WaiyRunner.run(self.all_agents["PlannerAgent"], f"{PLANNER_PROMPT} {query}")

        search_count = len(result.final_output.searches)
        logger.info(f"Will perform {search_count} searches")

        return result.final_output_as(FinancialSearchPlan)

    async def _perform_searches(self, search_plan: FinancialSearchPlan) -> List[str]:
        """执行所有搜索任务"""
        with custom_span("Search the web"):
            logger.info("Web Searching...")

            # 创建所有搜索任务
            tasks = [self._search(item) for item in search_plan.searches]

            # 并行执行所有搜索任务
            results = []
            completed = 0
            total = len(tasks)

            for result in await asyncio.gather(*tasks):
                if result:
                    results.append(result)
                completed += 1
                logger.info(f"Searching... {completed}/{total} completed")

            logger.info("Web Search completed")
            return results

    async def _search(self, item: FinancialSearchItem) -> Optional[str]:
        """执行单个搜索任务"""
        try:
            input_data = f"Search term: {item.query}\nReason: {item.reason}"
            result = await WaiyRunner.run(self.all_agents["FinSearchAgent"], input_data)
            return str(result.final_output)
        except Exception:
            return None

    async def _create_expert_tools(self) -> Tuple[Any, Any]:
        """创建专家分析工具"""

        # 自定义输出提取器 - 提取分析摘要
        async def extract_summary(run_result: RunResult) -> str:
            return str(run_result.final_output.summary)

        # 创建财务分析工具
        fundamentals_tool = self.all_agents["FinAnalystAgent"].as_tool(
            tool_name="fundamentals_analysis",
            tool_description="获取关键财务指标的简短分析",
            custom_output_extractor=extract_summary,
        )

        # 创建风险分析工具
        risk_tool = self.all_agents["RiskAnalystAgent"].as_tool(
            tool_name="risk_analysis",
            tool_description="获取潜在风险因素的简短分析",
            custom_output_extractor=extract_summary,
        )

        return fundamentals_tool, risk_tool

    async def _write_report(self, query: str, search_results: List[str]) -> FinancialReportData:
        """撰写研究报告"""
        logger.info("Thinking about report...")

        # 创建专家工具
        fundamentals_tool, risk_tool = await self._create_expert_tools()

        # 创建带工具的写作智能体
        writer_with_tools = self.all_agents["FinWriterAgent"].clone(
            tools=[fundamentals_tool, risk_tool]
        )

        # 准备输入数据
        input_data = f"{WRITER_PROMPT} {query}\n初步研究结果: {search_results}"

        # 运行写作智能体
        result = await WaiyRunner.run(writer_with_tools, input_data)

        logger.info("writer think completed")
        return result.final_output_as(FinancialReportData)

    async def _verify_report(self, report: FinancialReportData) -> VerificationResult:
        """验证报告准确性"""
        logger.info("Verifying report...")

        result = await WaiyRunner.run(self.all_agents["VerificationAgent"], report.markdown_report)

        logger.info("verify report completed")
        return result.final_output_as(VerificationResult)

    def _display_final_report(self, report: FinancialReportData) -> None:
        """显示最终报告"""
        final_report = f"Report summary\n\n{report.short_summary}"
        logger.info(f"final_report {final_report}")

        # 打印完整报告
        logger.info(f"Report:{report.markdown_report}")

        # 打印后续问题
        logger.info("=====FOLLOW UP QUESTIONS=====")
        logger.info(report.follow_up_questions)

    def _save_report(self, report: FinancialReportData) -> None:
        """保存报告到指定文件"""
        file_path = DATA_DIR / "report.md"
        try:
            with open(file_path, "w", encoding="utf-8") as file:
                file.write(report.markdown_report)
            logger.info(f"Report saved successfully to {file_path}")
        except Exception as e:
            logger.error(f"Failed to save report: {e}")
