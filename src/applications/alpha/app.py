"""
Alpha应用，AIPC主Agent
实现基础对话和网页搜索功能的智能体应用
"""

from typing import Any, Dict

from agents import ModelSettings

from src.applications.base import BaseApplication
from ...common.config.llm_config import llm_config_global


 
from ...common.logging import logger
from ...common.models import AppMetadata
from ...core.plugins import aliyun_search
from ...core.plugins.pc_operator import pc_operator
from .prompts_new import ALPHA_INSTRUCTIONS, PC_AGENT_INSTRUCTIONS, PC_OPERATOR_TOOL_DESCRIPTION

class AlphaApp(BaseApplication):
    """Alpha应用类，实现基础对话和网页搜索功能"""

    @property
    def metadata(self) -> AppMetadata:
        """获取应用元数据"""
        return AppMetadata(
            id="alpha",
            name="Alpha智能助手",
            version="1.0.0",
            description="友好的AI助手，支持智能对话和网页搜索",
            tags=["对话", "搜索", "助手"],
            mcp_servers=["wuying"],
        )

    async def setup_agents(self) -> None:
        """设置智能体"""
        logger.info("正在初始化Alpha智能体...")

        # 创建计算机操作智能体
        pc_agent = await self.create_agent(
            name="pc_operator",
            instructions=PC_AGENT_INSTRUCTIONS,
            tools=[pc_operator],  # 添加pc_operator工具
            model=llm_config_global.get_model(model_provider="dashscope", model_name="qwen-plus"),
            model_settings=ModelSettings(
                max_tokens=2048,  # 适当的响应长度
            )
        )

        # 创建主智能体，集成网页搜索功能和PC智能体调度
        self.primary_agent = await self.create_agent(
            name="alpha_assistant",
            instructions=ALPHA_INSTRUCTIONS,
            is_primary=True,
            tools=[
                aliyun_search,  # 阿里云搜索
                pc_agent.as_tool(
                    tool_name="pc_agent",
                    tool_description=PC_OPERATOR_TOOL_DESCRIPTION,
                ),
            ],
            model=llm_config_global.get_model(model_provider="others", model_name="gpt-5"),
            model_settings=ModelSettings(
                max_tokens=4096,  # 充足的响应长度
            )
        )

        logger.info("Alpha智能体和PC操作智能体初始化完成")

    # 重载_do_process_message
    async def _do_process_message(self, message: str) -> Dict[str, Any]:
        """处理消息"""
        if not self.runtime_resource or not self.runtime_resource.get("type") or self.runtime_resource.get("type") == "none":
            logger.info("Agent当前的计算机操作运行环境未开启，移除pc_agent工具")
            # 去掉主Agent的pc_agent工具
            self.primary_agent.tools = [aliyun_search]
        
        # 调用父类方法并返回结果
        return await super()._do_process_message(message)

    async def cleanup(self) -> None:
        """清理资源"""
        logger.info(f"清理Alpha应用资源: session_id={self.session_id}")
        await super().cleanup()
