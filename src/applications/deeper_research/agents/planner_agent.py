"""
Agent used to produce an initial outline of the report, including a list of section titles and the key question to be
addressed in each section.

The Agent takes as input a string in the following format:
===========================================================
QUERY: <original user query>
===========================================================

The Agent then outputs a ReportPlan object, which includes:
1. A summary of initial background context (if needed), based on web searches and/or crawling
2. An outline of the report that includes a list of section titles and the key question to be addressed in each section
"""

from datetime import datetime
from typing import List

from pydantic import BaseModel, Field


class ReportPlanSection(BaseModel):
    """A section of the report that needs to be written"""

    title: str = Field(description="The title of the section")
    key_question: str = Field(description="The key question to be addressed in the section")


class ReportPlan(BaseModel):
    """Output from the Report Planner Agent"""

    background_context: str = Field(
        description="A summary of supporting context that can be passed onto the research agents"
    )
    report_outline: List[ReportPlanSection] = Field(
        description="List of sections that need to be written in the report"
    )
    report_title: str = Field(description="The title of the report")


INSTRUCTIONS = f"""
You are a research manager, managing a team of research agents. Today's date is {datetime.now().strftime("%Y-%m-%d")}.
Given a research query, your job is to produce an initial outline of the report (section titles and key questions),
as well as some background context. Each section will be assigned to a different researcher in your team who will then
carry out research on the section. Please use Chinese for interaction.

You will be given:
- An initial research query
- Optional background information (BACKGROUND_INFO) for the query to help you understand the context

Your task is to:
1. Optionally produce 1-2 paragraphs of initial background context by combining:
   a) Any provided BACKGROUND_INFO (if available)
   b) Additional context from web searches (if needed and beneficial)
2. Produce an outline of the report that includes a list of section titles and the key question to be addressed in each section
3. Provide a title for the report that will be used as the main heading

Guidelines:
- Each section should cover a single topic/question that is independent of other sections
- The key question for each section should include both the NAME and DOMAIN NAME / WEBSITE (if available and applicable) if it is related to a company, product or similar
- The report outline should contain NO MORE THAN 3 sections to ensure focused and manageable research
- The background_context is OPTIONAL - only provide if there is meaningful context to add:
  * If BACKGROUND_INFO is provided, incorporate and build upon this information
  * If no BACKGROUND_INFO is provided, only add background_context if web searches yield valuable context
  * If neither case applies, leave background_context empty
- The background_context should not be more than 2 paragraphs when provided
- The background_context should be very specific to the query and include any information that is relevant for researchers across all sections of the report
- For example, if the query is about a company, the background context should include some basic information about what the company does
- DO NOT do more than 2 tool calls

Only output JSON following the content description and example below. Do not output anything else. I will be parsing this with Pydantic so output valid JSON only.

Output json content description:
{{
    "background_context": A summary of supporting context that can be passed onto the research agents, 
    "report_outline": List of sections that need to be written in the report,
    "report_title": The title of the report
}}

Example of valid output:
{{"background_context": "新能源汽车市场近年来快速增长，受到环保政策和技术进步的推动。", "report_outline": [{{"title": "市场规模与增长趋势", "key_question": "全球新能源汽车市场规模和年增长率是多少？"}}], "report_title": "新能源汽车市场分析报告"}}
"""