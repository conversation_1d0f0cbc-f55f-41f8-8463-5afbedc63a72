import asyncio
import json
import time
from typing import Any, Callable, Dict, List, Optional

from agents import TContext
import uuid
from memory import Memory
from memory.events import CustomEvent

from src.common.logging import logger
from src.core.memory.initialization import get_memory

from ...core.agent.baseclass import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>unner
from ...core.memory.session import WaiySession
from .agents.long_writer_agent import write_report
from .agents.planner_agent import ReportPlan, ReportPlanSection
from .agents.proofreader_agent import ReportDraft, ReportDraftSection
from .agents.understanding_agent import UnderstandingOutput
from .iterative_research import IterativeResearcher


class DeepResearcher:
    """
    Manager for the deep research workflow that breaks down a query into a report plan with sections and then runs an iterative research loop for each section.
    """

    def __init__(
        self,
        agents: Dict[str, WaiyBaseAgent],
        session_id: str = "",
        trace_id: str = "",
        max_iterations: int = 5,
        max_time_minutes: int = 15,
        verbose: bool = True,
        tracing: bool = True,
        agent_context: Optional[TContext] = None,
        cancellation_checker: Optional[Callable] = None,
    ):
        self.session_id = session_id
        self.trace_id = trace_id
        self.max_iterations = max_iterations
        self.max_time_minutes = max_time_minutes
        self.verbose = verbose
        self.tracing = tracing
        self.all_agents = agents
        self.understanding_agent = agents["UnderstandingAgent"]
        self.planner_agent = agents["PlannerAgent"]
        self.proofreader_agent = agents["ProofreaderAgent"]
        self.long_writer_agent = agents["LongWriterAgent"]
        self.memory: Memory = get_memory()
        self.report_title = ""
        self.agent_context = agent_context
        self.cancellation_checker = cancellation_checker

    async def run(self, query: str) -> Dict[str, Any]:
        """Run the deep research workflow"""
        start_time = time.time()

        self._log_message("=== 开始深度研究工作流 ===")
        self._log_message(f"研究查询: {query}")
        self._log_message(
            f"配置: 最大迭代次数={self.max_iterations}, 最大研究时间={self.max_time_minutes}分钟"
        )

        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("DeepResearcher运行被取消")
                raise

        # First, provide understanding confirmation to the user
        self._log_message("=== 正在理解用户需求 ===")
        understanding_result: Optional[UnderstandingOutput] = await self._confirm_understanding(
            query
        )
        if understanding_result is None:
            self._log_message("理解确认智能体运行失败")  # 不抛出异常，继续执行后续任务
        else:
            self._log_message(f"理解确认: {understanding_result.user_reply}")
            self._add_custom_event(
                "understanding_summary",
                json.dumps(
                    {
                        "user_reply": understanding_result.user_reply,
                        "research_plan": understanding_result.research_plan,
                        "requires_research": understanding_result.requires_research,
                        "research_topic": understanding_result.research_topic,
                        "research_bg_info": understanding_result.research_bg_info,
                    },
                    ensure_ascii=False,
                ),
            )
            # 如果不需要进一步研究，直接返回用户应答
            if not understanding_result.requires_research:
                return {
                    "needs_further_processing": False,
                    "content": understanding_result.user_reply,
                }
            else:
                self._log_message(f"理解确认: {understanding_result.research_plan}")

        # Then build the report plan which outlines the sections and compiles any relevant background context on the query
        self._log_message("=== 正在构建报告计划 ===")
        research_bg_info = understanding_result.research_bg_info if understanding_result else ""
        report_plan: ReportPlan = await self._build_report_plan(query, research_bg_info)
        self._log_message(f"报告计划构建完成，标题: {report_plan.report_title}")
        logger.info(
            f"[DEBUG] 准备调用 len(report_plan.report_outline), report_plan={report_plan}, report_outline={getattr(report_plan, 'report_outline', 'NO_ATTR') if report_plan else 'NO_OBJ'}"
        )
        self._log_message(f"计划包含 {len(report_plan.report_outline)} 个章节")
        logger.info(f"ReportPlan内容: {report_plan.model_dump_json()}")
        # Run the independent research loops concurrently for each section and gather the results
        self._log_message("=== 正在执行研究循环 ===")
        research_results: List[Dict] = await self._run_research_loops(report_plan)
        logger.info(f"[DEBUG] 准备调用 len(research_results), research_results={research_results}")
        self._log_message(f"研究循环完成，获得 {len(research_results)} 个章节的研究结果")
        self._log_message(f"research_results: {research_results}")

        # Create the final report from the original report plan and the drafts of each section
        self._log_message("=== 正在创建最终报告 ===")
        final_report: str = await self._create_final_report(query, report_plan, [result["content"] for result in research_results])

        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("DeepResearcher最终报告创建后被取消")
                raise

        logger.info(f"[DEBUG] 准备调用 len(final_report), final_report={final_report}")
        self._log_message(f"最终报告创建完成，长度: {len(final_report)} 字符")
        self._add_custom_event("final_report", final_report)

        elapsed_time = time.time() - start_time
        self._log_message(
            f"DeepResearcher 在 {int(elapsed_time // 60)} 分钟 {int(elapsed_time % 60)} 秒内完成"
        )

        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("DeepResearcher返回结果前被取消")
                raise

        return {"needs_further_processing": True, "content": final_report}

    async def _build_report_plan(self, query: str, research_bg_info: str = "") -> ReportPlan:
        """Build the initial report plan including the report outline (sections and key questions) and background context"""
        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("DeepResearcher构建报告计划前被取消")
                raise

        self._log_message("=== 构建报告计划 ===")

        # 构建包含背景信息的消息
        user_message = f"QUERY: {query}"
        if research_bg_info:
            user_message += f"\n\nBACKGROUND_INFO: {research_bg_info}"
            logger.info(
                f"[DEBUG] 准备调用 len(research_bg_info), research_bg_info={research_bg_info}"
            )
            self._log_message(f"已包含背景信息: {len(research_bg_info)} 字符")

        self._log_message(f"正在调用计划智能体...")
        result = await WaiyRunner.run(self.planner_agent, user_message, disable_memory=True)

        report_plan = result.final_output_as(ReportPlan)
        self._log_message(f"计划智能体已生成报告计划")

        self.report_title = report_plan.report_title

        if self.verbose:
            logger.info(
                f"[DEBUG] 准备调用 len(report_plan.report_outline), report_plan={report_plan}, report_outline={getattr(report_plan, 'report_outline', 'NO_ATTR') if report_plan else 'NO_OBJ'}"
            )
            num_sections = len(report_plan.report_outline)
            message_log = "\n\n".join(
                f"章节: {section.title}\n关键问题: {section.key_question}"
                for section in report_plan.report_outline
            )
            if report_plan.background_context:
                message_log += f"\n\n报告构建包含以下背景上下文：\n{report_plan.background_context}"
            else:
                message_log += "\n\n报告构建未提供背景上下文。\n"
            self._log_message(f"报告计划创建完成，包含 {num_sections} 个章节:\n{message_log}")
        return report_plan

    async def _confirm_understanding(self, query: str):
        """Confirm understanding of the user's research query"""
        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("DeepResearcher启动意图理解前被取消")
                raise

        self._log_message("=== 确认理解用户需求 ===")
        user_message = f"QUERY: {query}"
        self._log_message(f"正在调用理解确认智能体...")
        result = await WaiyRunner.run(
            self.understanding_agent,
            user_message,
            context=self.agent_context,
            session=WaiySession(self.session_id),
        )

        if result is None:
            raise RuntimeError("理解确认智能体运行失败，返回了None")

        understanding_result = result.final_output_as(
            UnderstandingOutput, raise_if_incorrect_type=True
        )
        self._log_message(f"理解确认智能体已生成确认信息")

        return understanding_result

    async def _run_research_loops(self, report_plan: ReportPlan) -> List[Dict]:
        """For a given ReportPlan, run a research loop concurrently for each section and gather the results"""
        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("DeepResearcher启动研究循环前被取消")
                raise

        # Get report_plan_data with iterative_research_id for each section  
        report_plan_data = report_plan.model_dump()
        for i, section in enumerate(report_plan_data.get("report_outline", [])):
            iterative_research_id = f"iterative-{uuid.uuid4()}"
            section["iterative_research_id"] = iterative_research_id
        self._add_custom_event("report_plan", json.dumps(report_plan_data, ensure_ascii=False))

        async def run_research_for_section(index: int, section: ReportPlanSection, section_data: dict):
            # 检查是否被取消
            if self.cancellation_checker:
                try:
                    self.cancellation_checker()
                except asyncio.CancelledError:
                    self._log_message("章节研究循环启动前被取消")
                    raise

            self._log_message(f"为章节 '{section.title}' 启动迭代研究...")
            iterative_researcher = IterativeResearcher(
                index,
                self.all_agents,
                self.session_id,
                self.trace_id,
                max_iterations=self.max_iterations,
                max_time_minutes=self.max_time_minutes,
                verbose=self.verbose,
                tracing=False,  # Do not trace as this will conflict with the tracing we already have set up for the deep researcher
                cancellation_checker=self.cancellation_checker,
            )
            args = {
                "query": section.key_question,
                "output_length": "",
                "output_instructions": "",
                "background_context": report_plan.background_context,
            }

            # Only use custom span if tracing is enabled
            result = await iterative_researcher.run(**args)

            self._log_message(
                f"章节 '{section.title}' 的研究完成，结果长度: {len(result["content"]) if result and "content" in result else 'NO_CONTENT'} 字符"
            )
            # Use existing iterative_research_id from report_plan
            iterative_research_id = section_data.get("iterative_research_id")
            
            # Create iterative_research_report event with the iterative_research_id
            iterative_research_report_data = {
                "content": result["content"],
                "iterative_research_id": iterative_research_id
            }
            self._add_custom_event("iterative_research_report", json.dumps(iterative_research_report_data, ensure_ascii=False))
            
            return {
                "content": result["content"],
                "iterative_research_id": iterative_research_id,
                "section_title": section.title,
                "section_key_question": section.key_question
            }

        self._log_message("=== 初始化研究循环 ===")
        logger.info(
            f"[DEBUG] 准备调用 len(report_plan.report_outline), report_plan={report_plan}, report_outline={getattr(report_plan, 'report_outline', 'NO_ATTR') if report_plan else 'NO_OBJ'}"
        )
        self._log_message(f"将为 {len(report_plan.report_outline)} 个章节并行执行研究循环")

        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("DeepResearcher执行研究循环前被取消")
                raise

        # Run all research loops concurrently in a single gather call
        research_results = await asyncio.gather(
            *(
                run_research_for_section(i, section, report_plan_data["report_outline"][i])
                for i, section in enumerate(report_plan.report_outline)
            )
        )

        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("DeepResearcher研究循环完成后被取消")
                raise

        self._log_message(f"所有章节的研究循环已完成")
        for i, result in enumerate(research_results):
            self._log_message(
                f"章节 {i+1}: '{report_plan.report_outline[i].title}' - 结果长度: {len(result['content']) if result and 'content' in result else 'NO_RESULT'} 字符"
            )

        return research_results

    async def _create_final_report(
        self,
        query: str,
        report_plan: ReportPlan,
        section_drafts: List[str],
        use_long_writer: bool = True,
    ) -> str:
        """Create the final report from the original report plan and the drafts of each section"""
        try:
            # 检查是否被取消
            if self.cancellation_checker:
                try:
                    self.cancellation_checker()
                except asyncio.CancelledError:
                    self._log_message("DeepResearcher创建最终报告前被取消")
                    raise

            # Each section is a string containing the markdown for the section
            # From this we need to build a ReportDraft object to feed to the final proofreader agent
            report_draft = ReportDraft(sections=[])
            for i, section_draft in enumerate(section_drafts):
                report_draft.sections.append(
                    ReportDraftSection(
                        section_title=report_plan.report_outline[i].title,
                        section_content=section_draft,
                    )
                )

            logger.info(
                f"[DEBUG] 准备调用 len(report_draft.sections), report_draft={report_draft}, sections={getattr(report_draft, 'sections', 'NO_ATTR') if report_draft else 'NO_OBJ'}"
            )
            self._log_message(f"已整合 {len(report_draft.sections)} 个章节的草稿内容")

            self._log_message("\n=== 生成最终报告 ===")

            if use_long_writer:
                self._log_message("使用长文写作智能体生成最终报告...")
                final_output = await write_report(
                    self.long_writer_agent, query, report_plan.report_title, report_draft
                )
                self._log_message(f"长文写作智能体生成完成")
            else:
                user_prompt = f"QUERY:\n{query}\n\nREPORT DRAFT:\n{report_draft.model_dump_json()}"
                # Run the proofreader agent to produce the final report
                self._log_message("使用校对智能体生成最终报告...")

                final_report = await WaiyRunner.run(
                    self.proofreader_agent, user_prompt, disable_memory=True
                )

                if final_report is None:
                    raise RuntimeError("校对智能体运行失败，返回了None")

                final_output = final_report.final_output
                self._log_message(f"校对智能体生成完成")

            logger.info(f"[DEBUG] 准备调用 len(final_output), final_output={final_output}")
            self._log_message(f"最终报告完成，长度: {len(final_output)} 字符")

            return final_output
        except asyncio.CancelledError:
            self._log_message("最终报告撰写被取消")
            raise

    def _log_message(self, message: str) -> None:
        """Log a message if verbose is True"""
        if self.verbose:
            logger.info(message)

    def _add_custom_event(self, name: str, value: Any) -> None:
        # 创建自定义事件
        custom_event = CustomEvent(
            session_id=self.session_id,
            run_id=self.trace_id,
            name=name,
            content=value,
        )

        self.memory.add_event(custom_event, self.session_id)

    def get_report_title(self) -> str:
        return self.report_title
