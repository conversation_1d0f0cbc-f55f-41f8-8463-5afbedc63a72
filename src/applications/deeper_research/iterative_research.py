from __future__ import annotations

import asyncio
import json
import time
from typing import Any, Callable, Dict, List, Optional

from memory import Memory
from memory.events import CustomEvent
from pydantic import BaseModel, Field

from src.common.logging import logger
from src.core.memory.initialization import get_memory

from ...core.agent.baseclass import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .agents.knowledge_gap_agent import KnowledgeGapOutput
from .agents.tool_agents.search_agent import ToolAgentOutput
from .agents.tool_selector_agent import AgentSelectionPlan, AgentTask


class IterationData(BaseModel):
    """Data for a single iteration of the research loop."""

    gap: str = Field(description="The gap addressed in the iteration", default_factory=list)
    tool_calls: List[str] = Field(description="The tool calls made", default_factory=list)
    findings: List[str] = Field(
        description="The findings collected from tool calls", default_factory=list
    )
    thought: List[str] = Field(
        description="The thinking done to reflect on the success of the iteration and next steps",
        default_factory=list,
    )


class Conversation(BaseModel):
    """A conversation between the user and the iterative researcher."""

    history: List[IterationData] = Field(
        description="The data for each iteration of the research loop", default_factory=list
    )

    def add_iteration(self, iteration_data: Optional[IterationData] = None):
        if iteration_data is None:
            iteration_data = IterationData()
        self.history.append(iteration_data)

    def set_latest_gap(self, gap: str):
        self.history[-1].gap = gap

    def set_latest_tool_calls(self, tool_calls: List[str]):
        self.history[-1].tool_calls = tool_calls

    def set_latest_findings(self, findings: List[str]):
        self.history[-1].findings = findings

    def set_latest_thought(self, thought: str):
        self.history[-1].thought = thought

    def get_latest_gap(self) -> str:
        return self.history[-1].gap

    def get_latest_tool_calls(self) -> List[str]:
        return self.history[-1].tool_calls

    def get_latest_findings(self) -> List[str]:
        return self.history[-1].findings

    def get_latest_thought(self) -> str:
        return self.history[-1].thought

    def get_all_findings(self) -> List[str]:
        return [finding for iteration_data in self.history for finding in iteration_data.findings]

    def compile_conversation_history(self) -> str:
        """Compile the conversation history into a string."""
        conversation = ""
        for iteration_num, iteration_data in enumerate(self.history):
            conversation += f"[ITERATION {iteration_num + 1}]\n\n"
            if iteration_data.thought:
                conversation += f"{self.get_thought_string(iteration_num)}\n\n"
            if iteration_data.gap:
                conversation += f"{self.get_task_string(iteration_num)}\n\n"
            if iteration_data.tool_calls:
                conversation += f"{self.get_action_string(iteration_num)}\n\n"
            if iteration_data.findings:
                conversation += f"{self.get_findings_string(iteration_num)}\n\n"

        logger.info(f"conversation: {conversation}")
        return conversation

    def get_task_string(self, iteration_num: int) -> str:
        """Get the task for the current iteration."""
        if self.history[iteration_num].gap:
            return f"<task>\nAddress this knowledge gap: {self.history[iteration_num].gap}\n</task>"
        return ""

    def get_action_string(self, iteration_num: int) -> str:
        """Get the action for the current iteration."""
        if self.history[iteration_num].tool_calls:
            joined_calls = "\n".join(self.history[iteration_num].tool_calls)
            return (
                "<action>\nCalling the following tools to address the knowledge gap:\n"
                f"{joined_calls}\n</action>"
            )
        return ""

    def get_findings_string(self, iteration_num: int) -> str:
        """Get the findings for the current iteration."""
        if self.history[iteration_num].findings:
            joined_findings = "\n\n".join(self.history[iteration_num].findings)
            return f"<findings>\n{joined_findings}\n</findings>"
        return ""

    def get_thought_string(self, iteration_num: int) -> str:
        """Get the thought for the current iteration."""
        if self.history[iteration_num].thought:
            return f"<thought>\n{self.history[iteration_num].thought}\n</thought>"
        return ""

    def latest_task_string(self) -> str:
        """Get the latest task."""
        logger.info(f"[DEBUG] 准备调用 len(self.history), history={self.history}")
        return self.get_task_string(len(self.history) - 1)

    def latest_action_string(self) -> str:
        """Get the latest action."""
        logger.info(f"[DEBUG] 准备调用 len(self.history), history={self.history}")
        return self.get_action_string(len(self.history) - 1)

    def latest_findings_string(self) -> str:
        """Get the latest findings."""
        logger.info(f"[DEBUG] 准备调用 len(self.history), history={self.history}")
        return self.get_findings_string(len(self.history) - 1)

    def latest_thought_string(self) -> str:
        """Get the latest thought."""
        logger.info(f"[DEBUG] 准备调用 len(self.history), history={self.history}")
        return self.get_thought_string(len(self.history) - 1)


class IterativeResearcher:
    """Manager for the iterative research workflow that conducts research on a topic or subtopic by running a continuous research loop."""

    def __init__(
        self,
        index: int,
        agents: Dict[str, WaiyBaseAgent],
        session_id: str = "",
        trace_id: str = "",
        max_iterations: int = 2,
        max_time_minutes: int = 5,
        verbose: bool = True,
        tracing: bool = False,
        cancellation_checker: Optional[Callable] = None,
    ):
        self.index = index
        self.session_id = session_id
        self.trace_id = trace_id
        self.max_iterations: int = max_iterations
        self.max_time_minutes: int = max_time_minutes
        self.start_time: float = None
        self.iteration: int = 0
        self.conversation: Conversation = Conversation()
        self.should_continue: bool = True
        self.verbose: bool = verbose
        self.tracing: bool = tracing
        self.knowledge_gap_agent = agents["KnowledgeGapAgent"]
        self.tool_selector_agent = agents["ToolSelectorAgent"]
        self.thinking_agent = agents["ThinkingAgent"]
        self.writer_agent = agents["WriterAgent"]
        self.tool_agents = {
            "WebSearchAgent": agents["WebSearchAgent"],
            # "SiteCrawlerAgent": agents["SiteCrawlerAgent"],
        }
        self.memory: Memory = get_memory()
        self.cancellation_checker = cancellation_checker

    async def run(
        self,
        query: str,
        output_length: str = "",  # A text description of the desired output length, can be left blank
        output_instructions: str = "",  # Instructions for the final report (e.g. don't include any headings, just a couple of paragraphs of text)
        background_context: str = "",
    ) -> Dict[str, Any]:
        """Run the deep research workflow for a given query."""
        self.start_time = time.time()

        self._log_message("=== Starting Iterative Research Workflow ===")
        self._log_message(f"查询: {query}")
        if background_context:
            logger.info(
                f"[DEBUG] 准备调用 len(background_context), background_context={background_context}"
            )
            self._log_message(f"背景上下文长度: {len(background_context)} 字符")
        self._log_message(
            f"最大迭代次数: {self.max_iterations}, 最大研究时间: {self.max_time_minutes}分钟"
        )

        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher启动研究循环前被取消")
                raise

        # Iterative research loop
        while self.should_continue and self._check_constraints():
            # 检查是否被取消
            if self.cancellation_checker:
                try:
                    self.cancellation_checker()
                except asyncio.CancelledError:
                    self._log_message("IterativeResearcher迭代循环中被取消")
                    raise

            self.iteration += 1
            self._log_message(f"\n=== Starting Iteration {self.iteration} ===")

            # Set up blank IterationData for this iteration
            self.conversation.add_iteration()
            self._log_message(f"已创建第{self.iteration}次迭代的数据结构")

            # 1. Generate observations
            self._log_message(f"正在生成观察结果...")
            observations: str = await self._generate_observations(
                query, background_context=background_context
            )
            logger.info(f"[DEBUG] 准备调用 len(observations), observations={observations}")
            self._log_message(f"观察结果生成完成，长度: {len(observations)} 字符")

            # 2. Evaluate current gaps in the research
            self._log_message(f"正在评估研究中的知识缺口...")
            evaluation: KnowledgeGapOutput = await self._evaluate_gaps(
                query, background_context=background_context
            )
            self._log_message(
                f"知识缺口评估完成，研究完成状态: {'已完成' if evaluation.research_complete else '未完成'}"
            )
            if not evaluation.research_complete:
                logger.info(
                    f"[DEBUG] 准备调用 len(evaluation.outstanding_gaps), evaluation={evaluation}, outstanding_gaps={getattr(evaluation, 'outstanding_gaps', 'NO_ATTR') if evaluation else 'NO_OBJ'}"
                )
            self._log_message(f"发现 {len(evaluation.outstanding_gaps)} 个未解决的知识缺口")

            # Check if we should continue or break the loop
            if not evaluation.research_complete:
                next_gap = evaluation.outstanding_gaps[0]
                self._log_message(f"选定下一个要解决的知识缺口: {next_gap}")

                # 3. Select agents to address knowledge gap
                self._log_message(f"正在选择智能体来解决知识缺口...")
                selection_plan: AgentSelectionPlan = await self._select_agents(
                    next_gap, query, background_context=background_context
                )
                logger.info(
                    f"[DEBUG] 准备调用 len(selection_plan.tasks), selection_plan={selection_plan}, tasks={getattr(selection_plan, 'tasks', 'NO_ATTR') if selection_plan else 'NO_OBJ'}"
                )
                self._log_message(f"已选择 {len(selection_plan.tasks)} 个智能体任务")

                # 4. Run the selected agents to gather information
                self._log_message(f"正在执行选定的智能体工具...")
                results: Dict[str, ToolAgentOutput] = await self._execute_tools(
                    selection_plan.tasks
                )
                logger.info(f"[DEBUG] 准备调用 len(results), results={results}")
                self._log_message(f"智能体工具执行完成，获取了 {len(results)} 个结果")
                results_dict = {k: v.model_dump() for k, v in results.items()}
                json_str = json.dumps(results_dict, ensure_ascii=False)
                self._log_message(f"results: {json_str}")

            else:
                self.should_continue = False
                self._log_message("=== IterativeResearcher 标记为完成 - 正在生成最终输出 ===")

        # Create final report
        self._log_message(f"IterativeResearcher 正在创建最终报告...")

        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher创建最终报告前被取消")
                raise

        report = await self._create_final_report(
            query, length=output_length, instructions=output_instructions
        )

        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher最终报告创建后被取消")
                raise

                logger.info(f"[DEBUG] 准备调用 len(report), report={report}")
                self._log_message(f"IterativeResearcher 最终报告创建完成，长度: {len(report)} 字符")
                self._log_message(f"第{self.index} 个章节的report: {report}")
        elapsed_time = time.time() - self.start_time
        self._log_message(
            f"IterativeResearcher 在 {int(elapsed_time % 60)} 秒内完成，经过 {self.iteration} 次迭代。"
        )

        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher返回结果前被取消")
                raise

        return {
            "content": report
        }

    def _check_constraints(self) -> bool:
        """Check if we've exceeded our constraints (max iterations or time)."""
        if self.iteration >= self.max_iterations:
            self._log_message("\n=== Ending Research Loop ===")
            self._log_message(f"达到最大迭代次数 ({self.max_iterations})")
            return False

        elapsed_minutes = (time.time() - self.start_time) / 60
        if elapsed_minutes >= self.max_time_minutes:
            self._log_message("\n=== Ending Research Loop ===")
            self._log_message(f"达到最大时间限制 ({self.max_time_minutes} 分钟)")
            return False

        elapsed_minutes = (time.time() - self.start_time) / 60
        self._log_message(
            f"当前状态: 迭代 {self.iteration}/{self.max_iterations}, 已用时间 {elapsed_minutes:.2f}/{self.max_time_minutes} 分钟"
        )
        return True

    async def _evaluate_gaps(self, query: str, background_context: str = "") -> KnowledgeGapOutput:
        """Evaluate the current state of research and identify knowledge gaps."""
        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher评估知识缺口前被取消")
                raise

        background = f"BACKGROUND CONTEXT:\n{background_context}" if background_context else ""

        input_str = f"""
        Current Iteration Number: {self.iteration}
        Time Elapsed: {(time.time() - self.start_time) / 60:.2f} minutes of maximum {self.max_time_minutes} minutes

        ORIGINAL QUERY:
        {query}

        {background}

        HISTORY OF ACTIONS, FINDINGS AND THOUGHTS:
        {self.conversation.compile_conversation_history() or "No previous actions, findings or thoughts available."}
        """

        self._log_message(f"_evaluate_gaps 正在调用知识缺口智能体, 输入:{input_str}")
        result = await WaiyRunner.run(self.knowledge_gap_agent, input_str, disable_memory=True)
        evaluation = result.final_output_as(KnowledgeGapOutput)

        if not evaluation.research_complete:
            next_gap = evaluation.outstanding_gaps[0]
            self.conversation.set_latest_gap(next_gap)
            self._log_message(self.conversation.latest_task_string())
            self._log_message(f"知识缺口智能体发现的缺口: {'& '.join(evaluation.outstanding_gaps)}")
        else:
            self._log_message(f"知识缺口智能体认为研究已完成")

        return evaluation

    async def _select_agents(
        self, gap: str, query: str, background_context: str = ""
    ) -> AgentSelectionPlan:
        """Select agents to address the identified knowledge gap."""
        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher选择智能体前被取消")
                raise

        background = f"BACKGROUND CONTEXT:\n{background_context}" if background_context else ""

        input_str = f"""
        ORIGINAL QUERY:
        {query}

        KNOWLEDGE GAP TO ADDRESS:
        {gap}

        {background}

        HISTORY OF ACTIONS, FINDINGS AND THOUGHTS:
        {self.conversation.compile_conversation_history() or "No previous actions, findings or thoughts available."}
        """

        self._log_message(f"_select_agents 正在调用工具选择智能体: {input_str}...")
        result = await WaiyRunner.run(self.tool_selector_agent, input_str, disable_memory=True)

        selection_plan = result.final_output_as(AgentSelectionPlan)

        # Add the tool calls to the conversation
        self.conversation.set_latest_tool_calls(
            [
                f"[Agent] {task.agent} [Query] {task.query} [Entity] {task.entity_website if task.entity_website else 'null'}"
                for task in selection_plan.tasks
            ]
        )
        self._log_message(self.conversation.latest_action_string())

        for i, task in enumerate(selection_plan.tasks):
            self._log_message(f"计划执行任务 {i+1}: 智能体={task.agent}, 查询={task.query}")

        return selection_plan

    async def _execute_tools(self, tasks: List[AgentTask]) -> Dict[str, ToolAgentOutput]:
        """Execute the selected tools concurrently to gather information."""
        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher执行工具前被取消")
                raise

        # Create a task for each agent
        async_tasks = []
        for task in tasks:
            async_tasks.append(self._run_agent_task(task))

        logger.info(f"[DEBUG] 准备调用 len(async_tasks), async_tasks={async_tasks}")
        self._log_message(f"创建了 {len(async_tasks)} 个并发智能体任务")

        # Run all tasks concurrently
        num_completed = 0
        results = {}
        for future in asyncio.as_completed(async_tasks):
            # 检查是否被取消
            if self.cancellation_checker:
                try:
                    self.cancellation_checker()
                except asyncio.CancelledError:
                    self._log_message("IterativeResearcher工具执行循环中被取消")
                    raise

            gap, agent_name, result = await future
            results[f"{agent_name}_{gap}"] = result
            num_completed += 1
            self._log_message(
                f"<processing>\n工具执行进度: {num_completed}/{len(async_tasks) if async_tasks else 'NO_TASKS'} - 完成了 {agent_name} 智能体\n</processing>"
            )

        # Add findings from the tool outputs to the conversation
        findings = []
        for tool_output in results.values():
            finding = tool_output.output + "\nsources:\n" + "\n".join(tool_output.sources) # 使用qwen3必须手动加sources
            findings.append(finding)
        self.conversation.set_latest_findings(findings)
        logger.info(f"[DEBUG] 准备调用 len(findings), findings={findings}")
        self._log_message(f"所有工具执行完成，收集了 {len(findings)} 个发现")

        return results

    async def _run_agent_task(self, task: AgentTask) -> tuple[str, str, ToolAgentOutput]:
        """Run a single agent task and return the result."""
        try:
            # 检查是否被取消
            if self.cancellation_checker:
                try:
                    self.cancellation_checker()
                except asyncio.CancelledError:
                    self._log_message("IterativeResearcher执行智能体任务前被取消")
                    raise

            agent_name = task.agent
            self._log_message(f"正在执行智能体任务: {agent_name}, 查询: {task.query}")
            agent = self.tool_agents.get(agent_name)
            if agent:
                result = await WaiyRunner.run(agent, task.model_dump_json(), disable_memory=True)
                # Extract ToolAgentOutput from RunResult
                output = result.final_output_as(ToolAgentOutput)
                self._log_message(
                    f"智能体 {agent_name} 执行完成，获取了 {len(output.sources) if output and hasattr(output, 'sources') else 'NO_SOURCES'} 个信息源"
                )
            else:
                output = ToolAgentOutput(
                    output=f"No implementation found for agent {agent_name}", sources=[]
                )
                self._log_message(f"错误: 未找到智能体 {agent_name} 的实现")

            return task.gap, agent_name, output
        except Exception as e:
            # 检查是否被取消
            if self.cancellation_checker:
                try:
                    self.cancellation_checker()
                except asyncio.CancelledError:
                    self._log_message("IterativeResearcher智能体任务异常处理中被取消")
                    raise

            error_message = f"执行 {task.agent} 智能体时出错: {str(e)}"
            self._log_message(error_message)
            error_output = ToolAgentOutput(
                output=f"Error executing {task.agent} for gap '{task.gap}': {str(e)}", sources=[]
            )
            return task.gap, task.agent, error_output

    async def _generate_observations(self, query: str, background_context: str = "") -> str:
        """Generate observations from the current state of the research."""
        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher生成观察结果前被取消")
                raise

        background = f"BACKGROUND CONTEXT:\n{background_context}" if background_context else ""

        input_str = f"""
        You are starting iteration {self.iteration} of your research process.

        ORIGINAL QUERY:
        {query}

        {background}

        HISTORY OF ACTIONS, FINDINGS AND THOUGHTS:
        {self.conversation.compile_conversation_history() or "No previous actions, findings or thoughts available."}
        """
        self._log_message(f"_generate_observations 正在调用思考智能体， 输入{input_str}...")
        result = await WaiyRunner.run(self.thinking_agent, input_str, disable_memory=True)

        # Add the observations to the conversation
        observations = result.final_output
        self.conversation.set_latest_thought(observations)
        self._log_message(self.conversation.latest_thought_string())
        logger.info(f"[DEBUG] 准备调用 len(observations), observations={observations}")
        self._log_message(f"思考智能体生成了 {len(observations)} 字符的观察结果")
        return observations

    async def _create_final_report(
        self, query: str, length: str = "", instructions: str = ""
    ) -> str:
        """Create the final response from the completed draft."""
        # 检查是否被取消
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher创建最终报告前被取消")
                raise

        self._log_message("=== 正在生成最终报告 ===")

        length_str = f"* The full response should be approximately {length}.\n" if length else ""
        instructions_str = f"* {instructions}" if instructions else ""
        guidelines_str = (
            ("\n\nGUIDELINES:\n" + length_str + instructions_str).strip("\n")
            if length or instructions
            else ""
        )

        if length:
            self._log_message(f"输出长度要求: {length}")
        if instructions:
            self._log_message(f"输出指令要求: {instructions}")

        all_findings = (
            "\n\n".join(self.conversation.get_all_findings()) or "No findings available yet."
        )
        self._log_message(
            f"汇总了 {len(self.conversation.get_all_findings()) if self.conversation else 'NO_CONVERSATION'} 个发现，总长度 {len(all_findings) if all_findings else 'NO_FINDINGS'} 字符"
        )

        input_str = f"""
        Provide a response based on the query and findings below with as much detail as possible. {guidelines_str}

        QUERY: {query}

        FINDINGS:
        {all_findings}
        """

        self._log_message(f"_create_final_report 正在调用写作智能体生成最终报告: {input_str}...")
        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher创建最终报告前被取消2")
                raise
        result = await WaiyRunner.run(self.writer_agent, input_str, disable_memory=True)

        if self.cancellation_checker:
            try:
                self.cancellation_checker()
            except asyncio.CancelledError:
                self._log_message("IterativeResearcher创建最终报告后被取消")
                raise

        # 打印result结果，如果失败则返回空
        logger.info(f"WriterAgent result: {result}")

        if result is None:
            self._log_message("WriterAgent返回None，任务可能被取消")
            return ""

        if not hasattr(result, "final_output") or result.final_output is None:
            self._log_message("WriterAgent结果缺少final_output，任务可能被取消")
            return ""

        final_output = result.final_output
        logger.info(f"[DEBUG] 准备调用 len(final_output), final_output={final_output}")
        self._log_message(f"最终报告生成完成，长度 {len(final_output)} 字符")

        return final_output

    def _log_message(self, message: str) -> None:
        """Log a message if verbose is True"""
        if self.verbose:
            logger.info(message)

    def _add_custom_event(self, name: str, value: Any) -> None:
        # 创建自定义事件
        custom_event = CustomEvent(
            timestamp=int(time.time() * 1000),
            session_id=self.session_id,
            run_id=self.trace_id,
            name=name,
            content=value,
        )

        self.memory.add_event(custom_event, self.session_id)
