import argparse
import as<PERSON><PERSON>
import json
from typing import Any, Dict, Literal, Optional, override

from agents import ModelSettings, custom_span, gen_trace_id, trace
from memory.events import ArtifactEvent

from src.applications.base import BaseApplication
from src.applications.deeper_research.agents.knowledge_gap_agent import (
    INSTRUCTIONS as KNOWLEDGE_GAP_PROMPTS,
)
from src.applications.deeper_research.agents.knowledge_gap_agent import (
    KnowledgeGapOutput,
)
from src.applications.deeper_research.agents.long_writer_agent import (
    INSTRUCTIONS as LONG_WRITER_PROMPTS,
)
from src.applications.deeper_research.agents.long_writer_agent import (
    LongWriterOutput,
)
from src.applications.deeper_research.agents.planner_agent import INSTRUCTIONS as PLANNER_PROMPTS
from src.applications.deeper_research.agents.planner_agent import ReportPlan
from src.applications.deeper_research.agents.proofreader_agent import INSTRUCTIONS as PROOF_PROMPTS
from src.applications.deeper_research.agents.thinking_agent import INSTRUCTIONS as THINKING_PROMPTS
from src.applications.deeper_research.agents.tool_agents.search_agent import (
    INSTRUCTIONS as SEARCH_PROMPTS,
)
from src.applications.deeper_research.agents.tool_agents.search_agent import (
    ToolAgentOutput,
)
from src.applications.deeper_research.agents.tool_selector_agent import (
    INSTRUCTIONS as TOOL_SELECTOR_PROMPTS,
)
from src.applications.deeper_research.agents.tool_selector_agent import (
    AgentSelectionPlan,
)
from src.applications.deeper_research.agents.understanding_agent import (
    INSTRUCTIONS as UNDERSTANDING_PROMPTS,
)
from src.applications.deeper_research.agents.understanding_agent import (
    UnderstandingOutput,
)
from src.applications.deeper_research.agents.utils.parse_output import create_type_parser
from src.applications.deeper_research.agents.web_agent import INSTRUCTIONS as WEB_PROMPTS
from src.applications.deeper_research.agents.writer_agent import INSTRUCTIONS as WRITER_PROMPTS
from src.common.config.llm_config import (
    model_supports_structured_output,
    llm_config_global
)

from ...common.config import DATA_DIR
from ...common.logging import logger
from ...common.models import AppMetadata
from ...core.agent.agent_hook import LoggingAndJsonHook
from ...core.agent.baseclass import WaiyRunner
from ...core.plugins import aliyun_search
from .deep_research import DeepResearcher
from .iterative_research import IterativeResearcher


class DeeperResearchApp(BaseApplication):
    """DeeperResearch应用类，实现超级深度研究应用"""

    @property
    def metadata(self) -> AppMetadata:
        """获取应用元数据"""
        return AppMetadata(
            id="deeper",
            name="超级深度研究",
            version="1.0.0",
            description="基于搜索的深度研究助手",
            tags=["deep-research", "深度研究"],
            mcp_servers=["file_system", "terminal", "browser"],
        )

    async def setup_agents(self) -> None:
        """设置智能体"""

        self._check_cancellation()

        search_agent = await self.create_agent(
            name="WebSearchAgent",
            instructions=SEARCH_PROMPTS,
            tools=[aliyun_search],
            model=llm_config_global.get_model("fast", self.model_setting.model_level),
            output_type=(
                ToolAgentOutput
                if model_supports_structured_output(llm_config_global.get_model("fast", self.model_setting.model_level))
                else None
            ),
            output_parser=(
                create_type_parser(ToolAgentOutput)
                if not model_supports_structured_output(llm_config_global.get_model("fast", self.model_setting.model_level))
                else None
            ),
            hooks=LoggingAndJsonHook(),
            input_guardrails=[],
            output_guardrails=[],
            # model_settings=ModelSettings(
            #     extra_args={"enable_thinking": False}
            # ) # qwen3加上所有enable_thinking
        )
        await self.create_agent(
            name="PlannerAgent",
            instructions=PLANNER_PROMPTS,
            tools=[
                search_agent.as_tool(
                    tool_name="web_search",
                    tool_description="Use this tool to search the web for information relevant to the query - provide a query with 3-6 words as input",
                ),
            ],
            model=llm_config_global.get_model("reasoning", self.model_setting.model_level),
            output_type=(
                ReportPlan
                if model_supports_structured_output(llm_config_global.get_model("reasoning", self.model_setting.model_level))
                else None
            ),
            output_parser=(
                create_type_parser(ReportPlan)
                if not model_supports_structured_output(llm_config_global.get_model("reasoning", self.model_setting.model_level))
                else None
            ),
            hooks=LoggingAndJsonHook(),
            input_guardrails=[],
            output_guardrails=[],
            # model_settings=ModelSettings(
            #     extra_args={"enable_thinking": True}
            # ),
        )
        await self.create_agent(
            name="ProofreaderAgent",
            instructions=PROOF_PROMPTS,
            model=llm_config_global.get_model("fast", self.model_setting.model_level),
            hooks=LoggingAndJsonHook(),
            input_guardrails=[],
            output_guardrails=[],
            # model_settings=ModelSettings(
            #     extra_args={"enable_thinking": False}
            # )
        )
        await self.create_agent(
            name="LongWriterAgent",
            instructions=LONG_WRITER_PROMPTS,
            model=llm_config_global.get_model("fast", self.model_setting.model_level),
            output_type=(
                LongWriterOutput
                if model_supports_structured_output(llm_config_global.get_model("fast", self.model_setting.model_level))
                else None
            ),
            output_parser=(
                create_type_parser(LongWriterOutput)
                if not model_supports_structured_output(llm_config_global.get_model("fast", self.model_setting.model_level))
                else None
            ),
            hooks=LoggingAndJsonHook(),
            input_guardrails=[],
            output_guardrails=[],
            # model_settings=ModelSettings(
            #     extra_args={"enable_thinking": False}
            # )
        )
        await self.create_agent(
            name="KnowledgeGapAgent",
            instructions=KNOWLEDGE_GAP_PROMPTS,
            model=llm_config_global.get_model("fast", self.model_setting.model_level),
            output_type=(
                KnowledgeGapOutput
                if model_supports_structured_output(llm_config_global.get_model("fast", self.model_setting.model_level))
                else None
            ),
            output_parser=(
                create_type_parser(KnowledgeGapOutput)
                if not model_supports_structured_output(llm_config_global.get_model("fast", self.model_setting.model_level))
                else None
            ),
            hooks=LoggingAndJsonHook(),
            input_guardrails=[],
            output_guardrails=[],
            # model_settings=ModelSettings(
            #     extra_args={"enable_thinking": False}
            # )
        )
        await self.create_agent(
            name="ToolSelectorAgent",
            instructions=TOOL_SELECTOR_PROMPTS,
            model=llm_config_global.get_model("fast", self.model_setting.model_level),
            output_type=(
                AgentSelectionPlan
                if model_supports_structured_output(llm_config_global.get_model("fast", self.model_setting.model_level))
                else None
            ),
            output_parser=(
                create_type_parser(AgentSelectionPlan)
                if not model_supports_structured_output(llm_config_global.get_model("fast", self.model_setting.model_level))
                else None
            ),
            hooks=LoggingAndJsonHook(),
            input_guardrails=[],
            output_guardrails=[],
            # model_settings=ModelSettings(
            #     extra_args={"enable_thinking": False}
            # )
        )
        await self.create_agent(
            name="ThinkingAgent",
            instructions=THINKING_PROMPTS,
            model=llm_config_global.get_model("reasoning", self.model_setting.model_level),
            hooks=LoggingAndJsonHook(),
            input_guardrails=[],
            output_guardrails=[],
            # model_settings=ModelSettings(
            #     extra_args={"enable_thinking": True}
            # ),
        )
        await self.create_agent(
            name="WriterAgent",
            instructions=WRITER_PROMPTS,
            model=llm_config_global.get_model("reasoning", self.model_setting.model_level),
            hooks=LoggingAndJsonHook(),
            input_guardrails=[],
            output_guardrails=[],
            # model_settings=ModelSettings(
            #     extra_args={"enable_thinking": True}
            # ),
        )
        await self.create_agent(
            name="WebAgent",
            instructions=WEB_PROMPTS,
            model=llm_config_global.get_model("code", self.model_setting.model_level),
            hooks=LoggingAndJsonHook(),
            model_settings=ModelSettings(max_tokens=32768, temperature=0.0),
            input_guardrails=[],
            output_guardrails=[],
        )
        await self.create_agent(
            name="UnderstandingAgent",
            instructions=UNDERSTANDING_PROMPTS,
            model=llm_config_global.get_model("main", self.model_setting.model_level),
            output_type=(
                UnderstandingOutput
                if model_supports_structured_output(llm_config_global.get_model("main", self.model_setting.model_level))
                else None
            ),
            output_parser=(
                create_type_parser(UnderstandingOutput)
                if not model_supports_structured_output(llm_config_global.get_model("main", self.model_setting.model_level))
                else None
            ),
            hooks=LoggingAndJsonHook(),
            # model_settings=ModelSettings(
            #     extra_args={"enable_thinking": False}
            # ),
        )

    @override
    async def _do_process_message(self, message: str) -> Dict[str, Any]:
        """处理用户消息，执行完整的研究流程"""
        self._check_cancellation()

        context = self.context or {}
        mode = context.get("mode", "deep")
        if mode == "deep":
            researcher = DeepResearcher(
                self.all_agents,
                self.session_id,
                self.trace_id,
                agent_context=self.agent_context,
                max_iterations=context.get("max_iterations", 1),
                max_time_minutes=context.get("max_time_minutes", 5),
                cancellation_checker=self._check_cancellation,
            )
            result = await researcher.run(message)

            # 检查是否被取消
            self._check_cancellation()

            # 根据 needs_further_processing 字段决定后续处理
            if result["needs_further_processing"]:
                web_report = await WaiyRunner.run(
                    self.all_agents["WebAgent"], result["content"], disable_memory=True
                )

                report_title = researcher.get_report_title()
                html_report = web_report.final_output_as(str)
                # self._save_report(report_title, html_report, "html")
                # self._save_report(report_title, result["content"], "md")

                self.memory.add_event(
                    ArtifactEvent(
                        session_id=self.session_id,
                        run_id=self.trace_id,
                        artifact_type="content_string",
                        file_type="html",
                        file_name=f"{report_title}.html",
                        content=html_report,
                        description="待补充",
                        is_process_file=True,
                    ),
                    self.session_id,
                )
                self.memory.add_event(
                    ArtifactEvent(
                        session_id=self.session_id,
                        run_id=self.trace_id,
                        artifact_type="content_string",
                        file_type="md",
                        file_name=f"{report_title}.md",
                        content=result["content"],
                        description="待补充",
                        is_process_file=True,
                    ),
                    self.session_id,
                )
                return {
                    "response": json.dumps(
                        {
                            "report": result["content"],
                            "web_report": web_report.final_output_as(str),
                        },
                        ensure_ascii=False,
                    )
                }
            else:
                return {
                    "response": json.dumps(
                        {"report": result["content"], "web_report": ""}, ensure_ascii=False
                    )
                }

        else:
            researcher = IterativeResearcher(
                self.all_agents,
                self.session_id,
                config=self.config,
                max_iterations=context.get("max_iterations", 5),
                max_time_minutes=context.get("max_time_minutes", 10),
                cancellation_checker=self._check_cancellation,
            )
            result = await researcher.run(
                query=message,
                output_length=context.get("output_length", "5 pages"),
            )

            # 检查是否被取消
            self._check_cancellation()

            web_report = await WaiyRunner.run(
                self.all_agents["WebAgent"], result["content"], disable_memory=True
            )
            # self._save_report(result["content"][:10], web_report.final_output_as(str), "html")
            # self._save_report(result["content"][:10], result["content"], "md")

            return {
                "response": json.dumps(
                    {"report": result["content"], "web_report": web_report.final_output_as(str)},
                    ensure_ascii=False,
                )
            }

    def _save_report(self, report_title: str, report: str, report_type: str = "md") -> None:
        """保存报告到指定文件"""
        # 检查是否被取消
        self._check_cancellation()
        file_path = DATA_DIR / f"{report_title}.{report_type}"
        try:
            with open(file_path, "w", encoding="utf-8") as file:
                file.write(report)
            logger.info(f"Report saved successfully to {file_path}")
        except Exception as e:
            # 检查是否被取消
            if hasattr(self, "_check_cancellation"):
                self._check_cancellation()

            logger.error(f"Failed to save report: {e}")

    async def cleanup(self) -> None:
        """清理资源"""
        logger.info(f"清理deeper research应用资源: session_id={self.session_id}")
        await super().cleanup()
