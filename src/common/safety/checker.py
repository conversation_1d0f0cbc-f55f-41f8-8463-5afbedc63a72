"""
安全检查工具
"""

from ..config import settings
from ..logging import logger


class SafetyChecker:
    """安全检查工具类"""

    @staticmethod
    def is_command_safe(command: str) -> bool:
        """
        检查命令是否安全

        Args:
            command: 要检查的命令字符串

        Returns:
            bool: 如果命令安全返回True，否则返回False
        """
        if not command:
            return True

        command = command.lower()
        for dangerous in settings.security.dangerous_commands:
            if dangerous.lower() in command:
                logger.warning(f"检测到危险命令模式: '{dangerous}' 在命令 '{command}' 中")
                return False
        return True

    @staticmethod
    def sanitize_path(path: str) -> str:
        """
        清理路径字符串，防止路径遍历攻击

        Args:
            path: 原始路径字符串

        Returns:
            str: 清理后的路径
        """
        # 移除可能导致路径遍历的模式
        sanitized = path.replace("..", "").replace("//", "/")

        # 移除前导的 / 以防止访问根目录
        while sanitized.startswith("/"):
            sanitized = sanitized[1:]

        if path != sanitized:
            logger.warning(f"路径已被清理: '{path}' -> '{sanitized}'")

        return sanitized

    @staticmethod
    def is_url_safe(url: str) -> bool:
        """
        检查URL是否安全

        Args:
            url: 要检查的URL

        Returns:
            bool: 如果URL安全返回True，否则返回False
        """
        # 此处可以扩展更多安全检查
        # 例如检查URL是否指向已知的恶意网站
        # 或者检查URL是否试图使用file://协议访问本地文件等

        if not url:
            return False

        dangerous_protocols = ["file://", "data:", "javascript:"]
        for protocol in dangerous_protocols:
            if protocol in url.lower():
                logger.warning(f"检测到危险URL协议: '{protocol}' 在URL '{url}' 中")
                return False

        return True
