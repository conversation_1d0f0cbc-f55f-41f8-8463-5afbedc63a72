from enum import Enum
from typing import List

from pydantic import BaseModel


class Visibility(str, Enum):
    """应用可见性枚举"""

    PRIVATE = "private"  # 私有应用，不对外显示
    PUBLIC = "public"  # 公开应用，对所有用户显示
    DOMAIN_SPECIFIC = "domain_specific"  # 域限制应用，仅特定域用户可见


class AppMetadata(BaseModel):
    """应用元数据"""

    id: str
    name: str
    version: str
    description: str = ""
    tags: List[str] = []
    mcp_servers: List[str] = []  # MCP服务器类型列表
    visibility: Visibility = Visibility.PUBLIC  # 应用可见性，默认为公开
