import json
import os
from functools import lru_cache
from typing import List, Optional

import nacos

from src.common.logging import logger

# 弹内默认服务地址，用于本地测试
# 正式环境地址：从aone配置的环境变量中获取
DEFAULT_ENDPOINT = "http://jmenv.tbsite.net:8080/diamond-server/diamond"
DATA_ID = "waiy-infra:application"
DEFAULT_GROUP = "DEFAULT_GROUP"


class DynamicConfig:
    def __init__(self):
        self.endpoint = os.getenv("DIAMOND_ENDPOINT", DEFAULT_ENDPOINT)
        self.data_id = os.getenv("DIAMOND_DATA_ID", DATA_ID)
        logger.info("DIAMOND endpoint: " + self.endpoint)
        self.client = nacos.NacosClient(
            endpoint=self.endpoint,
            namespace="",
        )
        self.get_config()

    def get_config(self) -> Optional[str]:
        self.config = self.client.get_config(self.data_id, DEFAULT_GROUP)
        if self.config is None:
            return None
        logger.info("get dynamic config: " + self.config)
        return self.config

    def get_config_by_key(self, key) -> Optional[str]:
        if self.config is None:
            return None
        try:
            config_dict = json.loads(self.config)
            return config_dict.get(key)
        except Exception as e:
            # TODO: [ccc] diamond 配置不正确，要增加告警
            logger.error(e)
            return None

    def get_config_by_key_list(self, key_list) -> List[Optional[str]]:
        if self.config is None:
            return []
        try:
            config_dict = json.loads(self.config)
            return [config_dict.get(key) for key in key_list]
        except Exception as e:
            logger.error(e)
            return []

    def add_watchers(self, cb):
        self.client.add_config_watcher(self.data_id, DEFAULT_GROUP, cb)

    def remove_watchers(self, cb):
        self.client.remove_config_watcher(self.data_id, DEFAULT_GROUP, cb)


@lru_cache(maxsize=1)
def get_instance():
    return DynamicConfig()


dynamicConfig = get_instance()
