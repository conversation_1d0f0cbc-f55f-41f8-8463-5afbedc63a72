from typing import Union

from agents import OpenAIChatCompletionsModel, OpenAIResponsesModel
from openai import AsyncOpenAI
from agents.extensions.models.litellm_model import LitellmModel

from src.common.config import settings

supported_providers = [
    "openai",
    # "bailian",
    "dashscope",
    "deepseek",
    "openrouter",
    "gemini",
    "anthropic",
    "others",
]

provider_mapping = {
    "openai": {
        "model": OpenAIChatCompletionsModel,
        "base_url": None,
        "api_key": settings.llm.openai_api_key,
    },
    # "bailian": {
    "dashscope": {
        "model": OpenAIChatCompletionsModel,
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "api_key": settings.llm.bailian_api_key,
    },
    "deepseek": {
        "model": OpenAIChatCompletionsModel,
        "base_url": "https://api.deepseek.com/v1",
        "api_key": settings.llm.deepseek_api_key,
    },
    "openrouter": {
        "model": OpenAIChatCompletionsModel,
        "base_url": "https://openrouter.ai/api/v1",
        "api_key": settings.llm.openrouter_api_key,
    },
    "gemini": {
        "model": OpenAIChatCompletionsModel,
        "base_url": "https://generativelanguage.googleapis.com/v1beta/openai/",
        "api_key": settings.llm.gemini_api_key,
    },
    "anthropic": {
        "model": OpenAIChatCompletionsModel,
        "base_url": "https://api.anthropic.com/v1/",
        "api_key": settings.llm.anthropic_api_key,
    },
    "others": {
        "model": OpenAIChatCompletionsModel,
        "base_url": settings.llm.others_provider_url,
        "api_key": settings.llm.others_provider_api_key,
    },
}


class LLMConfig:
    def __init__(self, settings) -> None:
        self.settings = settings

    def get_model(self, model_type: str = None, model_level: str = None, *, model_provider: str = None, model_name: str = None):
        """
        根据模型名称和级别获取对应的模型配置，或根据指定的模型提供者和模型名称获取配置，必须且只能提供 (model_type, model_level) 或 (model_provider, model_name) 的一组"
        
        Args:
            model_type: 模型类型，可选值为 "main", "fast", "code", "reasoning"
            model_level: 模型级别，可选值为 "high", "medium", "low"
            model_provider: 模型提供者，如 "openai", "dashscope" 等
            model_name: 具体的模型名称
            
        Returns:
            LitellmModel: 对应的模型配置实例
            
        Raises:
            ValueError: 当参数不合法或参数组合不正确时
        """
        # 验证参数互斥性：必须提供且只能提供两组参数中的一组
        if (model_type is not None and model_level is not None) == (model_provider is not None and model_name is not None):
            raise ValueError("必须且只能提供 (model_type, model_level) 或 (model_provider, model_name) 参数组合")
            
        # 如果提供了 model_type 和 model_level
        if model_type is not None and model_level is not None:
            # 验证参数
            valid_model_names = ["main", "fast", "code", "reasoning"]
            valid_model_levels = ["high", "medium", "low"]
            
            if model_type not in valid_model_names:
                raise ValueError(f"Invalid model_type: {model_type}. Must be one of {valid_model_names}")
                
            if model_level not in valid_model_levels:
                raise ValueError(f"Invalid model_level: {model_level}. Must be one of {valid_model_levels}")
            
            # 获取提供者和模型名称
            provider_attr = f"{model_type}_model_provider_{model_level}"
            model_attr = f"{model_type}_model_{model_level}"
            
            provider = getattr(self.settings.llm, provider_attr)
            model = getattr(self.settings.llm, model_attr)
        # 如果提供了 model_provider 和 model_name
        elif model_provider is not None and model_name is not None:
            provider = model_provider
            model = model_name
        else:
            raise ValueError("参数不完整，必须提供完整的 (model_type, model_level) 或 (model_provider, model_name) 参数组合")
        
        if provider not in supported_providers:
            raise ValueError(f"Unsupported provider: {provider}. Must be one of {supported_providers}")
            
        if provider != "others":
            return LitellmModel(
                model=f"{provider}/{model}",
                api_key=provider_mapping[provider]["api_key"],
                base_url=provider_mapping[provider]["base_url"],
            )
        else:
            # 创建并返回模型实例
            return LitellmModel(
                model=f"openai/{model}",
                api_key=provider_mapping[provider]["api_key"],
                base_url=provider_mapping[provider]["base_url"],
            )


llm_config_global = LLMConfig(settings)


def get_model_name(model: Union[OpenAIChatCompletionsModel, OpenAIResponsesModel, LitellmModel]) -> str:
    """Utility function to get the model name for a given model"""
    return str(model.model)


def model_supports_structured_output(
    model: Union[OpenAIChatCompletionsModel, OpenAIResponsesModel, LitellmModel],
) -> bool:
    """Utility function to check if a model supports structured output"""
    structured_output_models = [
        "o3",
        "o3-pro",
        "gpt-4.1-nano",
        "gpt-4.1-2025-04-14",
        "o3-mini",
        "o4-mini",
        "o4-nano",
        "gpt-4o",
        "gpt-4o-mini",
        "gemini-2.5-pro-preview-05-06",
        "gemini-2.5-pro",
    ]
    return any(llm in get_model_name(model) for llm in structured_output_models)


def model_need_streaming(model: Union[OpenAIChatCompletionsModel, OpenAIResponsesModel]) -> bool:
    """Utility function to check if a model needs streaming"""
    need_stream_models = [
        "qwq-plus",
        "o3",
        "o4",
        "gpt-4.1",
        "deepseek-r1",
        "qwen3-235b-a22b",
        "qwen3-32b",
        "qwen-plus",
        "qwen-turbo",
        "qwen-max",
        "gemini-2.5-pro-preview-05-06",
        "claude-sonnet-4-0-thinking",
        "gpt-5"
    ]
    return any(llm in get_model_name(model) for llm in need_stream_models)
