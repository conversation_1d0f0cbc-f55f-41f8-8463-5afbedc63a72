import json
import logging
from datetime import date, datetime
from pathlib import Path
from typing import Any, Dict, Optional


class SettingsFormatter:
    """设置格式化工具"""

    logger = logging.getLogger(__name__)

    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

    SENSITIVE_FIELDS = {
        "api_key",
        "key_secret",
        "bailian_api_key",
        "openai_api_key",
        "anthropic_api_key",
    }

    @classmethod
    def mask_sensitive_value(cls, value: str) -> str:
        """掩码敏感值"""
        if not value:
            return value
        return value[:4] + "*" * (len(value) - 8) + value[-4:] if len(value) > 8 else "****"

    @classmethod
    def process_value(cls, value: Any, mask_sensitive: bool = True) -> Any:
        """处理单个值，处理特殊类型"""
        if isinstance(value, Path):
            return str(value)
        elif isinstance(value, (datetime, date)):
            return value.isoformat()
        return value

    @classmethod
    def process_dict(cls, d: Dict[str, Any], mask_sensitive: bool = True) -> Dict[str, Any]:
        """处理字典，可选择是否掩码敏感信息"""
        result = {}
        for k, v in d.items():
            # 处理嵌套字典
            if isinstance(v, dict):
                result[k] = cls.process_dict(v, mask_sensitive)
            # 处理列表或元组
            elif isinstance(v, (list, tuple)):
                result[k] = [
                    (
                        cls.process_dict(i, mask_sensitive)
                        if isinstance(i, dict)
                        else cls.process_value(i, mask_sensitive)
                    )
                    for i in v
                ]
            # 处理敏感字段
            elif mask_sensitive and any(
                sensitive in k.lower() for sensitive in cls.SENSITIVE_FIELDS
            ):
                result[k] = cls.mask_sensitive_value(str(v))
            # 处理其他值
            else:
                result[k] = cls.process_value(v, mask_sensitive)
        return result

    @classmethod
    def format_settings(
        cls, settings: Any, mask_sensitive: bool = True, include_timestamp: bool = True
    ) -> str:
        """格式化设置为 JSON 字符串"""
        try:
            settings_dict = settings.model_dump()

            # 处理敏感信息和特殊类型
            processed_dict = cls.process_dict(settings_dict, mask_sensitive)

            # 构建输出
            output = (
                {"timestamp": datetime.now().isoformat(), "settings": processed_dict}
                if include_timestamp
                else processed_dict
            )

            # 格式化 JSON
            return json.dumps(output, indent=2, sort_keys=True, ensure_ascii=False)
        except Exception as e:
            return f"Error formatting settings: {str(e)}"

    @classmethod
    def log_settings(
        cls, settings: Any, mask_sensitive: bool = True, separator: str = "=" * 50
    ) -> None:
        """记录设置到日志"""
        formatted_output = cls.format_settings(settings, mask_sensitive=mask_sensitive)

        cls.logger.info(f"\n{separator}")
        cls.logger.info("Global Settings Initialized:")
        for line in formatted_output.split("\n"):
            cls.logger.info(line)
        cls.logger.info(separator)
