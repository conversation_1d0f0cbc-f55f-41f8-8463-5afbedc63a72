"""
配置管理模块
使用Pydantic BaseSettings实现配置管理与环境变量加载
"""

import json
from abc import ABC
from functools import lru_cache
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, TypeVar

from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings, PydanticBaseSettingsSource, SettingsConfigDict

from src.common.config.diamond_config import dynamicConfig
from src.common.config.key_center_config import kc
from src.common.config.settings_format import SettingsFormatter
from src.common.logging.logger import logger

# 基本路径配置
BASE_DIR = Path(__file__).parent.parent.parent.parent
DATA_DIR = BASE_DIR / "data"

# 确保数据目录存在
if not DATA_DIR.exists():
    DATA_DIR.mkdir(parents=True, exist_ok=True)


# 密钥解密辅助函数
def _decrypt_if_needed(value: str) -> str:
    """对字符串进行解密（如果需要）

    Args:
        value: 需要解密的字符串

    Returns:
        解密后的字符串，如果解密失败则返回原值
    """
    if not value:
        logger.warning("收到空值，跳过解密")
        return value
    try:
        decrypted = kc.kc_decrypt(value)
        return decrypted
    except Exception as e:
        logger.error(f"{str(e)}")
        # 对于必需的密钥，如果解密失败可能需要抛出异常
        # 这里简单返回原值，实际应用中可能需要更严格的处理
        return value


class DynamicConfigSettingsSource(PydanticBaseSettingsSource):
    """自定义配置源，从动态配置中心获取值"""

    def __init__(self, settings_cls):
        super().__init__(settings_cls)
        conf = getattr(settings_cls, "model_config", {})
        self.env_prefix = conf.get("env_prefix", "")

    def get_field_value(self, field_name: str, field_info: Any) -> tuple[Any, str, bool]:
        """从动态配置中心获取值

        Returns:
            tuple: (值, 来源, 需要更多的源)
        """
        env_key = f"{self.env_prefix}{field_name.upper()}"
        config_value = dynamicConfig.get_config_by_key(env_key)
        logger.info(f"从动态配置中心获取值: {field_name}={config_value}")

        if config_value is not None:
            return config_value, f"dynamic_config:{env_key}", False

        # 返回None表示需要尝试其他配置源
        return None, f"dynamic_config:{env_key}", True

    def prepare_field_value(
        self, field_name: str, field_source: str, value: Any, value_is_complex: bool
    ) -> Any:
        """预处理字段值"""
        return value

    def __call__(self) -> Dict[str, Any]:
        """返回从动态配置中心获取的所有配置值

        这是一个必须实现的抽象方法，按字段遍历并获取值，
        仅返回能够从动态配置中心找到的值。
        """
        d: Dict[str, Any] = {}
        for field_name, field in self.settings_cls.model_fields.items():
            value, field_source, use_other_sources = self.get_field_value(field_name, field)
            if not use_other_sources and value is not None:
                d[field_name] = self.prepare_field_value(
                    field_name, field_source, value, value_is_complex=False
                )
        return d


T = TypeVar("T", bound=BaseSettings)


class BaseConfigSettings(BaseSettings, ABC):
    """配置基类，统一处理配置获取逻辑"""

    model_config = SettingsConfigDict(
        extra="ignore",
        case_sensitive=False,
        env_file=".env",
        env_file_encoding="utf-8",
    )

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        """自定义设置源的顺序

        注意: Pydantic中，元组中靠后的源具有更高的覆盖优先级。

        期望的实际优先级 (从高到低):
        1. 初始化参数 (init_settings) - 最后处理，覆盖所有
        2. 动态配置源 (DynamicConfigSettingsSource) - 中间处理，覆盖.env
        3. .env文件 (dotenv_settings) - 最先处理，优先级最低

        因此，返回的元组顺序应为 (低优先级在前，高优先级在后):
        """
        return (
            init_settings,  # 最高优先级
            dotenv_settings,  # 本地env文件，调试时添加env文件可以覆盖服务端配置
            DynamicConfigSettingsSource(settings_cls),
        )

    @classmethod
    @lru_cache(maxsize=1)
    def get_instance(cls: Type[T]) -> T:
        """获取单例实例"""
        return cls()

    @classmethod
    def refresh(cls: Type[T]) -> T:
        """当动态配置变化时，刷新实例"""
        logger.info(f"刷新配置: {cls.__name__}")
        cls.get_instance.cache_clear()
        return cls.get_instance()


# 弃用，后续推荐使用llm_config.py中的LLMConfig
class OpenAISettings(BaseConfigSettings):
    model_config = SettingsConfigDict(
        env_prefix="OPENAI_",
    )

    """OpenAI相关配置"""
    api_key: str = Field("")
    model: str = Field("qwen-max")
    api_base_url: str = Field("")
    max_tokens: int = Field(8192)
    temperature: float = Field(0.0)


class LlmSettings(BaseConfigSettings):
    model_config = SettingsConfigDict(
        env_prefix="LLM_",
    )

    """模型相关配置"""
    openai_api_key: str = ""
    bailian_api_key: str = ""
    deepseek_api_key: str = ""
    openrouter_api_key: str = ""
    gemini_api_key: str = ""
    anthropic_api_key: str = ""
    others_provider_url: str = ""
    others_provider_api_key: str = ""
    reasoning_model_provider_high: str = ""
    reasoning_model_high: str = ""
    reasoning_model_provider_medium: str = ""
    reasoning_model_medium: str = ""
    reasoning_model_provider_low: str = ""
    reasoning_model_low: str = ""

    main_model_provider_high: str = ""
    main_model_high: str = ""
    main_model_provider_medium: str = ""
    main_model_medium: str = ""
    main_model_provider_low: str = ""
    main_model_low: str = ""
    
    fast_model_provider_high: str = ""
    fast_model_high: str = ""
    fast_model_provider_medium: str = ""
    fast_model_medium: str = ""
    fast_model_provider_low: str = ""
    fast_model_low: str = ""
    
    code_model_provider_high: str = ""
    code_model_high: str = ""
    code_model_provider_medium: str = ""
    code_model_medium: str = ""
    code_model_provider_low: str = ""
    code_model_low: str = ""

    @field_validator(
        "bailian_api_key",
        "others_provider_api_key",
        "openai_api_key",
        "deepseek_api_key",
        "openrouter_api_key",
        "gemini_api_key",
        "anthropic_api_key",
        mode="before",
    )
    def decrypt_secrets(cls, v: str) -> str:
        return _decrypt_if_needed(v)


class GuardrailSettings(BaseConfigSettings):
    model_config = SettingsConfigDict(
        env_prefix="GUARDRAIL_",
    )

    """护栏相关配置"""
    enabled: bool = True
    strict_mode: bool = False
    content_check_app_key: str = "34686242"
    content_check_app_secret: str = ""
    content_check_endpoint: str = "http://gw.api.taobao.com/router/rest"
    content_check_timeout: int = 10

    @field_validator("content_check_app_secret", mode="before")
    def decrypt_app_secret(cls, v: str) -> str:
        return _decrypt_if_needed(v)


class MCPSettings(BaseConfigSettings):
    model_config = SettingsConfigDict(
        env_prefix="MCP_",
    )

    """MCP服务器相关配置"""
    wuying_server_url: str = Field("")
    wuying_server_api_key: str = Field("")
    default_workspace_path: str = Field(".")
    enable_file_system: bool = Field(True)
    enable_git: bool = Field(True)
    enable_terminal: bool = Field(True)
    enable_browser: bool = Field(True)
    enable_thinking: bool = Field(True)
    enable_python: bool = Field(True)


class SecuritySettings(BaseConfigSettings):
    """安全相关配置"""

    model_config = SettingsConfigDict(
        env_prefix="SECURITY_", extra="ignore", case_sensitive=False, env_file=".env"
    )

    dangerous_commands: List[str] = Field(
        [
            "rm -rf",
            "format",
            "mkfs",
            "dd",
            ":(){ :|:& };:",  # fork bomb
            "shutdown",
            "reboot",
            "> /dev/sda",
            "chmod -R 777 /",
        ]
    )


class SearchSettings(BaseConfigSettings):
    """搜索相关配置"""

    model_config = SettingsConfigDict(
        env_prefix="SEARCH_",
    )

    iqs_key_id: str = Field("")
    iqs_key_secret: str = Field("")
    gg_key_id: str = Field("")
    gg_key_secret: str = Field("")
    tavily_api_key: str = Field("")

    @field_validator("iqs_key_secret", "gg_key_secret", "tavily_api_key", mode="before")
    def decrypt_secrets(cls, v: str) -> str:
        return _decrypt_if_needed(v)


class HistorySettings(BaseConfigSettings):
    """历史记录相关配置"""

    model_config = SettingsConfigDict(
        env_prefix="HISTORY_",
    )

    max_history_entries: int = Field(100)
    max_result_length: int = Field(500)
    history_file: Path = Field(DATA_DIR / "task_history.json")


class APISettings(BaseConfigSettings):
    model_config = SettingsConfigDict(
        env_prefix="API_",
    )

    """API服务相关配置"""
    host: str = Field("0.0.0.0")
    port: int = Field(8000)
    debug: bool = Field(False)
    cors_origins: List[str] = Field(["*"])
    request_timeout: int = Field(300)  # 网关请求超时时间默认300秒

    @field_validator("cors_origins", mode="before")
    def parse_cors_origins(cls, v):
        if v is None:
            return ["*"]
        if isinstance(v, str):
            # 处理字符串输入
            if v.startswith("[") and v.endswith("]"):
                # 处理 JSON 格式的字符串
                try:
                    return json.loads(v)
                except json.JSONDecodeError as e:
                    logger.error(f"parse json error: {e}")
                    return ["*"]
            # 处理逗号分隔的字符串
            return [origin.strip() for origin in v.split(",")]
        if isinstance(v, list):
            return v
        return ["*"]


class DatabaseSettings(BaseConfigSettings):
    model_config = SettingsConfigDict(
        env_prefix="DB_",
    )

    """数据库相关配置"""
    mysql_host: str = Field("")
    mysql_user: str = Field("")
    mysql_password: str = Field("")
    mysql_name: str = Field("")
    mysql_port: int = Field(3306)

    @property
    def url(self) -> str:
        """获取数据库连接URL"""
        return f"mysql+pymysql://{self.mysql_user}:{self.mysql_password}@{self.mysql_host}:{self.mysql_port}/{self.mysql_name}"

    @field_validator("mysql_password", mode="before")
    def decrypt_password(cls, v: str) -> str:
        return _decrypt_if_needed(v)


class MemorySettings(BaseConfigSettings):
    """Memory相关配置"""

    model_config = SettingsConfigDict(
        env_prefix="MEMORY_",
    )

    # 全局配置
    storage_preference: List[str] = Field(["database"])

    # Mem0存储配置
    mem0_dashscope_api_key: str = Field("")
    mem0_es_host: str = Field("")
    mem0_es_port: str = Field("")
    mem0_es_user: str = Field("")
    mem0_es_password: str = Field("")
    mem0_embedding_model: str = Field("text-embedding-v3")

    # 数据库存储配置
    db_host: str = Field("")
    db_port: int = Field(3306)
    db_user: str = Field("")
    db_password: str = Field("")
    db_database: str = Field("")

    # 内存存储配置（暂时没有特殊配置）
    memory_enabled: bool = Field(True)

    # MQ配置
    mq_enabled: bool = Field(False)
    mq_type: str = Field("rocketmq")
    mq_endpoint: str = Field("")
    mq_access_key: str = Field("")
    mq_secret_key: str = Field("")
    mq_instance_id: str = Field("")
    mq_topic: str = Field("")
    mq_group_id: str = Field("")

    @field_validator("mem0_dashscope_api_key", "db_password", "mq_secret_key", mode="before")
    def decrypt_secrets(cls, v: str) -> str:
        return _decrypt_if_needed(v)


class ResourceSettings(BaseConfigSettings):
    """资源处理配置"""

    model_config = SettingsConfigDict(env_prefix="RESOURCE_")

    # RAG服务配置
    rag_access_key_id: str = Field("", description="RAG服务AccessKey ID")
    rag_access_key_secret: str = Field("", description="RAG服务AccessKey Secret")
    rag_endpoint: str = Field("wuyingaiinner-pre.aliyuncs.com", description="RAG服务端点")
    rag_service_timeout: int = Field(30, description="服务超时时间(秒)")

    # 文件内容限制配置
    max_file_content_size: int = Field(9_437_184)  # 9MB in bytes

    @field_validator("rag_access_key_secret", mode="before")
    def decrypt_secrets(cls, v: str) -> str:
        return _decrypt_if_needed(v)


class ServiceSettings(BaseConfigSettings):
    """服务相关配置"""

    model_config = SettingsConfigDict(env_prefix="SERVICE_")

    ram_role: str = Field("acs:ram::1550203943326350:role/waiy-infra-pre")
    region_id: str = Field("cn-shanghai")
    app_name: str = Field("waiy-infra")
    app_env: str = Field("production")


class PCAgentSettings(BaseConfigSettings):
    """PC Agent相关配置"""

    model_config = SettingsConfigDict(env_prefix="PCAGENT_")

    agent_task_timeout: int = Field(300, description="pc agent 执行操作的超时时间(秒)")
    region_id: str = Field("cn-shanghai", description="Agentbay 区域ID")
    endpoint: str = Field("wuyingai.cn-shanghai.aliyuncs.com", description="Agentbay 服务端点")
    timeout_ms: int = Field(60000, description="Agentbay 请求超时时间(毫秒)")


class ExecutorSettings(BaseConfigSettings):
    """异步执行器配置"""

    model_config = SettingsConfigDict(env_prefix="EXECUTOR_")

    max_workers: int = Field(
        default=10,
        description="异步执行器的最大线程数，用于处理同步SDK调用",
        ge=1,
        le=100
    )
    
    timeout: int = Field(
        default=300,
        description="异步执行器的默认超时时间（秒）",
        ge=10,
        le=3600
    )


class Settings(BaseSettings):
    """全局配置类，集成所有子配置"""

    openai: OpenAISettings = Field(default_factory=OpenAISettings)
    llm: LlmSettings = Field(default_factory=LlmSettings)
    guardrail: GuardrailSettings = Field(default_factory=GuardrailSettings)
    mcp: MCPSettings = Field(default_factory=MCPSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    search: SearchSettings = Field(default_factory=SearchSettings)
    pcagent: PCAgentSettings = Field(default_factory=PCAgentSettings)
    executor: ExecutorSettings = Field(default_factory=ExecutorSettings)
    history: HistorySettings = Field(default_factory=HistorySettings)
    api: APISettings = Field(default_factory=APISettings)
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    memory: MemorySettings = Field(default_factory=MemorySettings)
    resource: ResourceSettings = Field(default_factory=ResourceSettings)
    service: ServiceSettings = Field(default_factory=ServiceSettings)

    model_config = SettingsConfigDict(extra="ignore", case_sensitive=False, env_file=".env")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._post_init()

    def _post_init(self):
        try:
            SettingsFormatter.log_settings(self)
            self.start_watch()
        except Exception as e:
            logger.error(f"Error logging settings: {e}")

    @model_validator(mode="after")
    def initialize_components(self) -> "Settings":
        """全局配置加载后的初始化操作"""
        return self

    def start_watch(self):
        """启动配置监听"""
        logger.info(f"启动配置监听")
        try:
            logger.info("开始监听配置变化")
            dynamicConfig.add_watchers(refresh_config)
            logger.info("配置监听器添加成功")
        except Exception as e:
            logger.error("配置监听发生错误: {}", e)

    def cleanup(self):
        """清理资源"""
        logger.info("开始清理配置资源")
        try:
            dynamicConfig.remove_watchers(refresh_config)
            logger.info("配置监听器移除成功")
        except Exception as e:
            logger.error(f"配置监听器移除失败: {e}")

    def __del__(self):
        """析构函数"""
        self.cleanup()

    @classmethod
    @lru_cache(maxsize=1)
    def get_instance(cls) -> "Settings":
        return cls()


def refresh_config(data: str):
    """刷新配置"""
    try:
        dynamicConfig.get_config()
        settings.openai = OpenAISettings.refresh()
        settings.llm = LlmSettings.refresh()
        settings.guardrail = GuardrailSettings.refresh()
        settings.mcp = MCPSettings.refresh()
        settings.security = SecuritySettings.refresh()
        settings.search = SearchSettings.refresh()
        settings.history = HistorySettings.refresh()
        settings.api = APISettings.refresh()
        settings.database = DatabaseSettings.refresh()
        settings.memory = MemorySettings.refresh()
        settings.resource = ResourceSettings.refresh()
        settings.service = ServiceSettings.refresh()
        settings.executor = ExecutorSettings.refresh()
        SettingsFormatter.log_settings(settings)

    except Exception as e:
        logger.error(f"刷新配置失败: {e}")
    pass


# 创建全局配置实例
settings = Settings.get_instance()
