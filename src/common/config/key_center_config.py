import os
import traceback

from keycenter import kc_crypto_service as kcs
from keycenter import kc_key_sync as kks

from src.common.logging import logger

# 使用keycenter进行密钥管理
# KC环境地址一览：https://yuque.antfin.com/mkf08w/cvdlr6/environment
# 日常使用域名:
DAILY_HOST = "https://daily-keycenter.alibaba.net/keycenter"
# 预发使用域名:
PRE_HOST = "http://pre-keycenter-service-internal.alibaba-inc.com/keycenter"
# 线上使用域名:
ONLINE_HOST = "http://keycenter-service.alibaba-inc.com/keycenter"
# 应用发放码：https://keycenter.alibaba.net/#/app/my
KC_APP_CODE = "8c82a4f4831a4e03aa85727487744ecf"  # daily
# 密钥名称：https://keycenter.alibaba.net/#/key/my
KEY_NAME = "waiy-infra_aone_key"


class DecryptError(Exception):
    pass


class KeyCenterConfig:
    server_host = os.getenv("KC_SERVER", DAILY_HOST)
    publish_num = os.getenv("KC_APP_CODE", KC_APP_CODE)
    key_name = os.getenv("KC_KEY_NAME", KEY_NAME)


class KC:
    def __init__(self):
        self.cfg = KeyCenterConfig()
        self.kc_init(self.cfg.server_host, self.cfg.publish_num)  # KC初始化

    def kc_init(self, server_host, publish_num):
        """
        KC初始化, 仅需执行一次即可
        server_host: KC环境地址
        publish_num: KC应用发放码
        """
        kks.initialize(server_host, publish_num)
        logger.info(
            "KC初始化已完成, keyname:"
            + self.cfg.key_name
            + " , server_host:"
            + server_host
            + " , publish_num:"
            + publish_num
        )

    def kc_decrypt(self, encrypted) -> str:
        """
        KC解密方法

        Args:
            encrypted (str): 需要解密的密文

        Returns:
            str: 解密后的明文

        Raises:
            DecryptError: 解密失败时抛出
        """
        if not encrypted:
            logger.warning("收到空密文")
            return encrypted

        try:
            logger.info(f"开始解密密文: {encrypted}")
            decrypted = kcs.decrypt(self.cfg.key_name, encrypted)
            if not decrypted:
                raise DecryptError("解密结果为空")

            logger.success("解密成功")
            return decrypted

        except Exception as e:
            error_msg = f"解密失败,返回原值: encrypted={encrypted}, error={str(e)}"
            # print(error_msg)
            # print(f"异常堆栈: {traceback.format_exc()}")
            raise DecryptError(error_msg) from e

    def kc_encrypt(self, plaintext):
        """
        KC加密
        plaintext: 待加密明文
        """
        return kcs.encrypt(self.cfg.key_name, plaintext)


kc = KC()

# if __name__ == "__main__":
#     #测试代码，在办公网只能访问daily环境
#     kc_test = KC()
#     plaintext = 'Hello World!'
#     # 加密字符串
#     encrypted = kc_test.kc_encrypt(plaintext)
#     print ("encrypted: " + encrypted)
#     # 解密字符串
#     decrypted = kc_test.kc_decrypt(encrypted)
#     print ("decrypted: " + decrypted)

#     #sk密文
#     qwen_api_key_daily = "ELyBJyxc8x645FLq6HOUxJ8QxjEEckJ7fvU4m34UTGAKTcp8f6+pdtht0miQoRM8"
#     api_key = kc_test.kc_decrypt(qwen_api_key_daily)
#     print ("api_key: " + api_key)

#     #测试iqs_key_secret
# iqs_key_secret = "******************************"
# encrypted = kc_test.kc_encrypt(iqs_key_secret)
# print ("encrypted: " + encrypted)
# decrypted = kc_test.kc_decrypt(encrypted)
# print ("decrypted: " + decrypted)

#     #测试空值
#     decrypted = kc_test.kc_decrypt(None)


#     #测试无法解密场景
#     try:
#         decrypted = kc_test.kc_decrypt("LTAI5tJehaVS79rYrcv7LrHQ")
#     except Exception as e:
#          print(e)
