"""
日志系统
整合终端显示和跟踪逻辑

loguru提供了七层日志层级，或者说七种日志类型。

生产环境中，常常在不同场景下使用不用的日志类型，用于处理各种问题。

每种类型的日志有一个整数值，表示日志层级，我们成为log level no。

TRACE (5): 用于记录程序执行路径的细节信息，以进行诊断。
DEBUG (10): 开发人员使用该工具记录调试信息。
INFO (20): 用于记录描述程序正常操作的信息消息。
SUCCESS (25): 类似于INFO，用于指示操作成功的情况。
WARNING (30): 警告类型，用于指示可能需要进一步调查的不寻常事件。
ERROR (40): 错误类型，用于记录影响特定操作的错误条件。
CRITICAL (50): 严重类型，用于记录阻止核心功能正常工作的错误条件。
"""

import os
import sys
from contextvars import ContextVar
from typing import Optional

import logfire
from loguru import logger as loguru_logger
from agents.tracing.scope import Scope

# 读取环境变量中的LOGLEVEL配置，默认INFO
log_level = os.environ.get("LOGLEVEL") or "INFO"

# 全局 trace_id 上下文变量
trace_id_context: ContextVar[str] = ContextVar('trace_id', default='unknown')

# 自定义埋点级别
loguru_logger.level("ANALYTICS", no=26, color="<yellow>")


def get_current_trace_id() -> str:
    """获取当前 trace_id"""
    # 优先从上下文变量中获取
    context_trace_id = trace_id_context.get()
    if context_trace_id != 'unknown':
        return context_trace_id
    
    # 如果上下文中没有，尝试从 agent scope 中获取
    try:
        current_trace = Scope.get_current_trace()
        if current_trace:
            return current_trace.export().get("id", "unknown")
    except Exception:
        pass
    
    return "unknown"


def format_record(record):
    """自定义记录格式化函数，添加 trace_id"""
    # 获取 trace_id
    trace_id = record["extra"].get("trace_id", get_current_trace_id())
    record["extra"]["trace_id"] = trace_id
    return record


def set_trace_id(trace_id: str) -> None:
    """设置当前 trace_id 到上下文中"""
    trace_id_context.set(trace_id)


def clear_trace_id() -> None:
    """清除当前 trace_id"""
    trace_id_context.set('unknown')


# 不再使用包装器，而是在 format_record 中自动处理


# 从环境变量获取IS_SERVER_ENV，转换为布尔值
# 如果.env中没有配置IS_SERVER_ENV变量，则默认为True
is_server_env_str = os.getenv("IS_SERVER_ENV")
if is_server_env_str is None:
    is_server_env = True
else:
    is_server_env = is_server_env_str.lower() in ("true", "1", "yes", "on")

if is_server_env:
    # 服务器环境：无颜色格式，添加 trace_id 字段
    format_string = "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {extra[trace_id]: <20} | {process}:{thread} | {name}:{function}:{line} | {message}" 
else:
    # 开发环境：带颜色格式，添加 trace_id 字段
    format_string = "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | " + \
        "<level>{level: <8}</level> | " + \
        "<blue>{extra[trace_id]: <20}</blue> | " + \
        "<magenta>{process}:{thread}</magenta> | " + \
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | " + \
        "<level>{message}</level>"
    

loguru_logger.remove(0)

# 日志输出到控制台，添加格式化函数
loguru_logger.add(sys.stdout, format=format_string, colorize=True, level=log_level, filter=format_record)

# 使用原始 loguru logger，trace_id 在 format_record 中自动处理
logger = loguru_logger

# --- 专用规划日志（PLAN）配置 ---
# 将"任务规划/步骤输入输出"等关键节点日志单独输出到 logs/agent_plan.log
try:
    import os as _os
    _os.makedirs("logs", exist_ok=True)

    def _plan_filter(record):
        return record["extra"].get("channel") == "PLAN"

    # 更简洁的格式，便于人类阅读
    _plan_format = "{time:YYYY-MM-DD HH:mm:ss} | {extra[trace_id]} | {message}"
    loguru_logger.add(
        "logs/agent_plan.log",
        format=_plan_format,
        filter=_plan_filter,
        level="INFO",
        rotation="10 MB",
        retention="7 days",
        enqueue=True,
    )
    # 导出专用 plan_logger
    plan_logger = loguru_logger.bind(channel="PLAN")
except Exception as _e:
    # 若文件日志初始化失败，回退到控制台
    plan_logger = loguru_logger.bind(channel="PLAN")

logger.info(f"log inited! 日志级别: {log_level}")
