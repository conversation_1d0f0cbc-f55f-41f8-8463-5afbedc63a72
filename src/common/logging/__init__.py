"""
日志系统模块
"""

from .logger import (  # print_header,; print_info,; print_success,; print_warning,; print_error,; print_divider,; print_trace,
    log_level,
    logfire,
    logger,
)
from .waiy_trace import WaiyTracingProcessor, setup_waiy_tracing

__all__ = [
    "logger",
    "logfire",
    "log_level",
    # "console",
    # "print_header",
    # "print_info",
    # "print_success",
    # "print_warning",
    # "print_error",
    # "print_divider",
    # "print_trace",
    "WaiyTracingProcessor",
    "setup_waiy_tracing",
]
