import base64
import json
import os
from datetime import datetime
from typing import Any, Dict, Optional

import logfire
from agents import Span, Trace, TracingProcessor, set_trace_processors
from dotenv import load_dotenv
from memory import Function, Memory, Message, Role, ToolCall

from src.common.logging import logfire, logger, plan_logger
from src.core.memory.handler import MemoryHandler
from src.core.memory.initialization import get_memory


class WaiyTracingProcessor(TracingProcessor):
    """
    自定义跟踪处理器，用于打印和记录LLM、MCP等交互链路信息
    """

    memory: Memory

    def __init__(self, detailed_mode: bool = True):
        """
        初始化自定义跟踪处理器

        Args:
            detailed_mode: 是否打印详细信息
            memory: Memory实例，如果不提供则使用全局实例
        """
        from src.common.config import settings

        self.detailed_mode = detailed_mode

        self.memory = get_memory()
        self.memory_handler = MemoryHandler(self.memory)
        # 跟踪状态
        self.active_traces: Dict[str, Dict[str, Any]] = {}
        self.active_spans: Dict[str, Dict[str, Any]] = {}

        # 缩进级别
        self.indent_level = 0

    def _log_message(self, message: str, span: Optional[Span] = None) -> None:
        """
        记录消息到控制台和/或文件

        Args:
            message: 要记录的消息
            span: 相关的span对象
        """
        # 添加缩进
        indented_message = "  " * self.indent_level + message

        logger.log("ANALYTICS", f"{indented_message}")

    def _store_memory(self, message: dict) -> None:
        try:
            self.memory_handler.store_message(message)
        except Exception as e:
            logger.error(f"Failed to store memory: {str(e)}")

    def on_trace_start(self, trace: Trace) -> None:
        """当Trace开始时调用"""
        trace_info = {
            "object": "trace",
            "trace_id": trace.trace_id,
            "name": trace.name,
            "started_at": datetime.now().isoformat(),
            "spans": [],
            "data": trace.export(),
            "session_id": trace.export().get("group_id"),
        }
        self._store_memory(trace_info)
        self.active_traces[trace.trace_id] = trace_info

        self._log_message(f"🔍 TRACE START: {trace.name} (ID: {trace.trace_id})")
        self.indent_level += 1

    def on_trace_end(self, trace: Trace) -> None:
        """当Trace结束时调用"""
        if trace.trace_id in self.active_traces:
            trace_info = self.active_traces[trace.trace_id]
            trace_info["ended_at"] = datetime.now().isoformat()

            # 计算持续时间
            start_time = datetime.fromisoformat(trace_info["started_at"])
            end_time = datetime.fromisoformat(trace_info["ended_at"])
            duration = (end_time - start_time).total_seconds()

            self.indent_level -= 1
            self._log_message(f"TRACE END: {trace.name} (Duration: {duration:.2f}s)")

            trace_export = trace.export()
            self._log_message(f"FULL TRACE: {json.dumps(trace_export)}")

            self._store_memory(trace_info)  
            # 清理
            del self.active_traces[trace.trace_id]

    def on_span_start(self, span: Span[Any]) -> None:
        """当Span开始时调用"""
        span_data = span.span_data
        export_data =span_data.export()
        span_type = span_data.type
                
        ext_info = {}
        if span.parent_id in self.active_spans:
            ext_info = self.active_spans[span.parent_id].get("ext_info") or {}

        span_info = {
            "span_id": span.span_id,
            "trace_id": span.trace_id,
            "parent_id": span.parent_id,
            "type": span_type,
            "started_at": datetime.now().isoformat(),
            "data": span_data
        }

        # 添加到trace的spans列表
        if span.trace_id in self.active_traces:
            self.active_traces[span.trace_id]["spans"].append(span.span_id)

        # 根据span类型打印不同的信息
        if span_type == "Generation".lower():
            model = getattr(span_data, "model", "unknown")
            input_msgs = getattr(span_data, "input", [])
            input_summary = f"{len(input_msgs)} messages" if input_msgs else "No input"
            self._log_message(f"💬 LLM CALL: Model={model}, Input={input_summary}", span)
            # 同步写入规划日志，标注使用的模型
            try:
                trace_id = span.trace_id or 'unknown'
                plan_logger.bind(trace_id=trace_id).info(f"[LLM_CALL] model={model} agent={ext_info.get('agent_name', 'unknown')}")
            except Exception:
                pass
            export_data = span.export()
            export_data["span_data"]["ext_info"] = ext_info
            self._store_memory(export_data)

        elif span_type == "Function".lower():
            function_name = getattr(span_data, "name", "unknown")
            function_input = getattr(span_data, "input", None)
            input_summary = (
                str(function_input)[:50] + "..." if not self.detailed_mode else str(function_input)
            )
            self._log_message(f"🔧 FUNCTION: {function_name}({function_input})", span)

        elif span_type == "MCPListTools".lower():
            server = getattr(span_data, "server", "unknown")
            self._log_message(f"🛠️ MCP LIST TOOLS: Server={server}", span)

        elif span_type == "mcp_tools".lower():
            server = getattr(span_data, "server", "unknown")
            self._log_message(f"🛠️ MCP TOOLS: Server={server}", span)

        elif span_type == "Agent".lower():
            agent_name = getattr(span_data, "name", "unknown")
            tools = getattr(span_data, "tools", [])
            tools_summary = f"{len(tools)} tools" if tools else "No tools"
            ext_info.update(agent_name=agent_name)
            self._log_message(f"🤖 AGENT: {agent_name}, Tools={tools_summary}", span)

        elif span_type == "Handoff".lower():
            from_agent = getattr(span_data, "from_agent", "unknown")
            to_agent = getattr(span_data, "to_agent", "unknown")
            self._log_message(f"🔄 HANDOFF: {from_agent} -> {to_agent}", span)

        elif span_type == "Response".lower():
            self._log_message(f"📤 RESPONSE", span)

        elif span_type == "Custom".lower():
            data = getattr(span_data, "data", {})
            name = getattr(span_data, "name", "unknown")
            
            if isinstance(data, dict):
                ext_info.update(data)
            else:
                logger.error(f"Expected dict for data, got {type(data)}: {data}")
            
            self._log_message(f"🔶 CUSTOM: {name}", span)

        else:
            self._log_message(f"📊 SPAN START: {span_type}", span)
        
        span_info["ext_info"] = ext_info or {}
        self.active_spans[span.span_id] = span_info

        self.indent_level += 1

        # 详细模式: 打印完整的span数据
        if self.detailed_mode:
            self._log_message(
                f"Details: {json.dumps(span_data.export(), ensure_ascii=False)}", span
            )

    def on_span_end(self, span: Span[Any]) -> None:
        """当Span结束时调用"""
        if span.span_id in self.active_spans:
            span_info = self.active_spans[span.span_id]
            span_info["ended_at"] = datetime.now().isoformat()

            # 计算持续时间
            start_time = datetime.fromisoformat(span_info["started_at"])
            end_time = datetime.fromisoformat(span_info["ended_at"])
            duration = (end_time - start_time).total_seconds()

            span_type = span_info["type"]

            self.indent_level -= 1

            # 根据span类型打印不同的结束信息
            if span_type == "Function".lower():
                function_name = getattr(span.span_data, "name", "unknown")
                function_output = getattr(span.span_data, "output", None)
                output_summary = (
                    str(function_output)[:50] + "..."
                    if function_output and len(str(function_output)) > 50
                    else str(function_output)
                )
                self._log_message(
                    f"🔧 FUNCTION END: {function_name} -> {function_output} ({duration:.2f}s)", span
                )
            elif span_type == "Generation".lower():
                model = getattr(span.span_data, "model", "unknown")
                output = getattr(span.span_data, "output", None)
                output_summary = (
                    str(output)[:50] + "..." if output and len(str(output)) > 50 else "(no output)"
                )
                self._log_message(f"💬 LLM END: {model} -> {output} ({duration:.2f}s)", span)
                # 同步写入规划日志，标注使用的模型
                try:
                    trace_id = span.trace_id or 'unknown'
                    plan_logger.bind(trace_id=trace_id).info(f"[LLM_END] model={model} agent={self.active_spans.get(span.span_id, {}).get('ext_info', {}).get('agent_name', 'unknown')} duration={duration:.2f}s")
                except Exception:
                    pass
                # Handle list output (e.g., multiple messages)
            else:
                self._log_message(f"📊 SPAN END: {span_type} ({duration:.2f}s)", span)
            
            export_data = span.export()
            export_data["span_data"]["ext_info"] = span_info.get("ext_info", {}) or {}
            self._store_memory(export_data)
            # 详细模式：打印更新后的 span 数据
            # if self.detailed_mode:
            #     self._log_message(f"Final Details: {json.dumps(span.span_data.export(), indent=2, ensure_ascii=False)}", span)

            # 清理
            del self.active_spans[span.span_id]

    def shutdown(self) -> None:
        """关闭处理器"""
        logger.info("Shutting down CustomTracingProcessor")

    def force_flush(self) -> None:
        """强制刷新缓冲区"""
        from src.common.config import settings

        if settings.logging.log_to_file:
            settings.logging.log_to_file.flush()

    def cleanup(self):
        """清理资源"""
        pass


def setup_waiy_tracing(
    detailed_mode: bool = True,
) -> WaiyTracingProcessor:
    """
    设置WAIY跟踪处理器

    Args:
        detailed_mode: 是否打印详细信息

    Returns:
        WaiyTracingProcessor: 跟踪处理器实例
    """
    # 创建跟踪处理器
    tracer = WaiyTracingProcessor(detailed_mode=detailed_mode)

    # 设置为全局跟踪处理器
    set_trace_processors([tracer])

    # Configure OpenTelemetry endpoint & headers
    # load_dotenv()
    # LANGFUSE_AUTH = base64.b64encode(
    #     f"{os.environ.get('LANGFUSE_PUBLIC_KEY')}:{os.environ.get('LANGFUSE_SECRET_KEY')}".encode()
    # ).decode()

    # langfuse_host = os.environ.get("LANGFUSE_HOST")
    # if not langfuse_host:
    #     raise ValueError("LANGFUSE_HOST environment variable is not set.")
    # os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = langfuse_host + "/api/public/otel"
    # os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = f"Authorization=Basic {LANGFUSE_AUTH}"

    # 自动追踪OpenAI agents SDK调用
    logfire.instrument_openai_agents()

    return tracer
