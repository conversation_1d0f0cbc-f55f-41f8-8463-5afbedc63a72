# WAIY智能体框架 Web UI

Web UI模块提供了一个基于Gradio构建的交互式用户界面，用于与WAIY智能体框架中的各种智能体应用进行对话交互。

## 功能特点

- **多智能体应用支持**：可以连接并切换多个智能体应用
- **实时对话**：与智能体进行实时对话交互，支持富文本格式显示
- **Markdown渲染**：支持富文本格式的消息显示，包括代码块、表格等
- **进度展示**：长时间运行的请求会显示进度条和等待时间
- **超时设置**：可自定义消息处理超时时间（30-1200秒，最长20分钟）
- **响应式界面**：适应不同屏幕尺寸的界面设计

## 使用方法

### 通过API服务器访问

Web UI已自动集成到API服务器中。启动API服务器时，Web UI将自动挂载到`/web-ui`路径：

```bash
python apy.py
```

启动后，访问：http://localhost:8000/web-ui

### 界面指南

#### 主界面元素

Web UI界面主要分为两部分：

1. **左侧控制面板**：
   - 智能体应用选择下拉菜单
   - 刷新应用列表按钮
   - 应用信息显示区域
   - 服务器信息折叠面板

2. **右侧聊天区域**：
   - 聊天消息显示区域
   - 消息输入框
   - 发送按钮
   - 清空对话按钮

#### 使用流程

1. 从下拉菜单中选择一个智能体应用
2. 查看应用描述了解其功能
3. 在输入框中输入问题或指令
4. 点击"发送"按钮或按Enter键发送消息
5. 查看智能体的回复
6. 继续对话或选择其他智能体

## 集成到其他应用

可以使用以下代码将Web UI集成到其他应用中：

```python
from src.web_ui.app import create_gradio_app

# 创建Gradio应用实例
app = create_gradio_app(api_url="http://your-api-server:8000")

# 启动应用
app.launch()
```

## 超时处理

Web UI和API服务器都有超时设置，它们协同工作以确保请求得到适当处理：

- **客户端超时**：Web UI中的`message_timeout`设置（默认240秒，最大1200秒）
- **服务器超时**：API服务器中的`request_timeout`设置（默认300秒）

对于需要较长处理时间的请求，可以在"服务器信息"面板中调整超时设置：

1. 点击"服务器信息"展开面板
2. 使用滑块调整消息超时时间（30-1200秒，最长20分钟）
3. 设置将立即生效

为了获得最佳体验，客户端超时不应超过服务器超时。您可以在"服务器信息"面板中调整超时设置。

## 技术架构

Web UI使用以下技术构建：

- **Gradio**：用于构建交互式Web界面
- **HTTPX**：用于异步HTTP请求
- **FastAPI**：用于API集成（当作为API服务器的一部分运行时）
- **asyncio**：用于异步操作和任务管理

## 开发者信息

### 扩展Web UI

要添加新功能到Web UI，可以修改以下文件：

- `src/web_ui/app.py`：主要应用逻辑 