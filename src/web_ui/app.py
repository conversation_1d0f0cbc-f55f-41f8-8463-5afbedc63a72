"""
WAIY智能体框架 Web UI应用
使用Gradio构建的交互式UI，用于与智能体进行对话
"""

import asyncio
import json
import os
import time
import traceback
import uuid
from typing import Any, Dict, List, Optional, Tuple

import gradio as gr
import httpx
import markdown

from ..common.logging import logger
from ..common.models import AppMetadata


class WaiyWebUI:
    """WAIY Web UI应用类"""

    def __init__(self, api_base_url: str = None, static_base_url: str = None):
        """初始化Web UI应用"""
        # 确保API_BASE_URL不以斜杠结尾
        if api_base_url and api_base_url.endswith("/"):
            api_base_url = api_base_url[:-1]
        self.api_base_url = api_base_url or "http://localhost:8000"

        # 静态文件基础URL配置
        if static_base_url:
            self.static_base_url = static_base_url.rstrip("/")
        else:
            # 默认使用相对路径（适用于集成到FastAPI的情况）
            self.static_base_url = ""

        self.app_list = []

        # websocket 连接
        self.ws_task = None

        # 配置 - 确保在interface构建前初始化
        self.message_timeout = 240.0  # 默认设为更大的240秒
        self.fetch_timeout = 30.0  # 增加获取应用列表和信息的超时时间(秒)

        # 活跃任务跟踪
        self.active_tasks = []

        # 主题和界面初始化
        self.theme = self._create_theme()
        self.interface = self._build_interface()

    def _create_theme(self) -> gr.Theme:
        """创建自定义主题"""
        # 使用简化的主题配置，兼容新版Gradio
        return gr.Theme(
            primary_hue="indigo",
            secondary_hue="blue",
            neutral_hue="slate",
            font=[
                gr.themes.GoogleFont("Source Sans Pro"),
                "ui-sans-serif",
                "system-ui",
                "sans-serif",
            ],
        )

    async def _fetch_all_apps(self) -> List[Tuple[str, str]]:
        """获取所有可用的应用"""
        try:
            url = f"{self.api_base_url}/apps"
            logger.info(f"获取应用列表: {url}")

            # 使用更长的超时时间
            timeout = httpx.Timeout(self.fetch_timeout)
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(url)
                if response.status_code != 200:
                    logger.error(f"获取应用列表失败: {response.text}")
                    return []

                data = response.json()
                self.app_list = data.get("applications", [])

                # 转换为Gradio下拉菜单的格式 [(显示名, 值), ...]
                return [(app["name"], app["id"]) for app in self.app_list]
        except httpx.TimeoutException as e:
            logger.error(f"获取应用列表时超时: {e}")
            logger.error(traceback.format_exc())
            return []
        except Exception as e:
            logger.error(f"获取应用列表时发生错误: {e}")
            logger.error(traceback.format_exc())
            return []

    async def _send_message(
        self, message: str, app_id: str, history: List[Dict[str, Any]], session_id: str = None
    ) -> Tuple[List[Dict[str, Any]], str]:
        """发送消息到选定的智能体应用"""
        if not message or not app_id:
            return history, session_id or ""

        new_history = history + [
            {"role": "user", "content": message},
            {"role": "assistant", "content": None},
        ]
        progress = gr.Progress(track_tqdm=False)
        progress(0, desc="初始化请求...")
        self.active_tasks = [t for t in self.active_tasks if not t.done()]

        try:
            url = f"{self.api_base_url}/apps/{app_id}/message"
            timeout_seconds = float(self.message_timeout)
            logger.info(
                f"发送消息到: {url}, 超时设置: {timeout_seconds}秒, session_id: {session_id}"
            )
            start_time = time.time()
            progress(0.05, desc="发送消息...")
            timeout = httpx.Timeout(timeout_seconds)

            # 构建请求上下文，包含session_id（如果有的话）
            context = {}
            if session_id:
                context["session_id"] = session_id
            # else:
            #     context["session_id"] = self.temp_session_id

            async with httpx.AsyncClient(timeout=timeout) as client:
                progress(0.1, desc="正在处理请求...")
                progress_task = asyncio.create_task(self._update_progress(progress, start_time))
                self.active_tasks.append(progress_task)

                try:
                    response = await client.post(url, json={"message": message, "requestId": "xxxx", "context": context})
                    if not progress_task.done():
                        progress_task.cancel()
                        await asyncio.sleep(0.1)

                    progress(0.9, desc="处理响应...")
                    if response.status_code != 200:
                        error_msg = f"请求失败 (状态码: {response.status_code}): {response.text}"
                        logger.error(error_msg)
                        error_content = f"""❌ **请求错误**

**状态码**: {response.status_code}
**详情**: {response.text}

请检查服务器状态或稍后重试。
"""
                        new_history[-1]["content"] = error_content
                        return new_history, session_id

                    data = response.json()
                    ai_response = data.get("response", "")
                    returned_session_id = data.get("session_id", session_id)
                    progress(1.0, desc="完成")

                    try:
                        response_obj = (
                            json.loads(ai_response)
                            if isinstance(ai_response, str) and ai_response.strip().startswith("{")
                            else None
                        )
                        if response_obj and "web_report" in response_obj:
                            logger.info("检测到web report响应")
                            web_report_html = response_obj.get("web_report", "")
                            report_summary = response_obj.get("report", "详细报告请查看网页版本。")

                            if web_report_html:
                                # 定义保存报告的目录和文件名
                                base_dir = os.path.dirname(os.path.abspath(__file__))
                                reports_dir = os.path.join(base_dir, "static", "temp_reports")
                                os.makedirs(reports_dir, exist_ok=True)

                                filename = f"report_{uuid.uuid4().hex}.html"
                                file_path = os.path.join(reports_dir, filename)

                                # 保存HTML内容到文件
                                try:
                                    with open(file_path, "w", encoding="utf-8") as f:
                                        f.write(web_report_html)
                                    logger.info(f"Web report已保存到: {file_path}")

                                    # 构建在新标签页打开的链接
                                    # 根据static_base_url配置生成正确的URL
                                    if self.static_base_url:
                                        report_url = f"{self.static_base_url}/web-ui-custom-static/temp_reports/{filename}"
                                    else:
                                        # 默认情况（集成到FastAPI）
                                        report_url = (
                                            f"/web-ui-custom-static/temp_reports/{filename}"
                                        )

                                    link_html = f"""<div class="web-report-notification">
                                        <p>已生成网页报告，请点击下方链接在新标签页中查看：</p>
                                        <a href="{report_url}" target="_blank" class="view-web-report-btn">查看网页报告</a>
                                    </div>"""
                                    final_response = f"{report_summary}\n\n{link_html}"
                                    new_history[-1]["content"] = final_response
                                    return new_history, returned_session_id
                                except Exception as e_save:
                                    logger.error(f"保存web report文件失败: {e_save}")
                                    # 保存失败，回退到仅显示摘要
                                    new_history[-1]["content"] = (
                                        report_summary + "\n(网页报告生成失败，无法提供链接)"
                                    )
                                    return new_history, returned_session_id
                            else:
                                # web_report为空，只显示report内容
                                logger.info("web_report为空，只显示report内容")
                                new_history[-1]["content"] = report_summary
                                return new_history, returned_session_id
                        else:
                            # 不包含web report字段，直接显示原始响应
                            logger.debug("响应不包含web report字段，直接显示原始内容")
                            new_history[-1]["content"] = ai_response
                            return new_history, returned_session_id
                    except (json.JSONDecodeError, ValueError, AttributeError) as e:
                        logger.debug(f"响应不是JSON格式或不包含web report: {e}")
                        # 解析失败，直接显示原始响应
                        new_history[-1]["content"] = ai_response
                        return new_history, returned_session_id
                except asyncio.CancelledError:
                    if not progress_task.done():
                        progress_task.cancel()
                        await asyncio.sleep(0.1)
                    raise
                finally:
                    if not progress_task.done():
                        progress_task.cancel()
                        await asyncio.sleep(0.1)
        except httpx.TimeoutException as e:
            elapsed = time.time() - start_time
            logger.error(f"请求超时 ({elapsed:.1f}秒): {e}")
            logger.error(traceback.format_exc())
            error_message = f"""❌ **请求超时**

发送到智能体 `{app_id}` 的请求在 {elapsed:.1f} 秒后超时。

当前超时设置: {self.message_timeout} 秒

这可能是因为:
- 服务器处理时间过长
- 网络连接问题
- 服务器负载过高

请稍后重试或选择其他智能体。您也可以在"服务器信息"中调整超时设置。
"""
            new_history[-1]["content"] = error_message
            return new_history, session_id or ""

        except Exception as e:
            logger.error("处理应用 '%s' 消息时发生未知错误: %s", app_id, str(e), exc_info=True)
            error_message = f"""❌ **系统错误**

与服务器通信时出现问题: {str(e)}

错误类型: {type(e).__name__}

请检查网络连接或联系管理员。
"""
            new_history[-1]["content"] = error_message
            return new_history, session_id or ""

    async def _update_progress(self, progress, start_time):
        """异步更新进度条，显示等待时间"""
        try:
            prog_value = 0.1
            while True:
                elapsed = time.time() - start_time
                # 进度最多到达85%，剩余部分留给结果处理
                # 使用实际的超时设置来计算进度
                timeout_seconds = float(self.message_timeout)
                prog_value = min(0.1 + (elapsed / timeout_seconds) * 0.75, 0.85)
                progress(prog_value, desc=f"智能体处理中... {elapsed:.1f}秒 / {timeout_seconds}秒")
                await asyncio.sleep(0.5)  # 每0.5秒更新一次
        except asyncio.CancelledError:
            # 正常结束
            pass

    async def _terminate_async_task(
        self, session_id: str, app_id: str = None
    ) -> List[Dict[str, Any]]:
        """终止异步任务"""
        # 使用传入的session_id（来自session_id_state）
        # 如果没有有效的session_id，提示用户先创建任务
        if not session_id or session_id == "":
            content = "❌ 无法终止异步任务\n\n当前没有活跃的异步任务会话。请先发送一个消息来创建异步任务，然后再尝试终止。"
            return [{"role": "assistant", "content": content}]

        # 如果没有提供app_id，提示用户先选择应用
        if not app_id:
            content = "❌ 无法终止异步任务\n\n请先选择一个智能体应用，然后再尝试终止任务。"
            return [{"role": "assistant", "content": content}]

        try:
            # 构建正确的应用子路由URL
            url = f"{self.api_base_url}/apps/{app_id}/terminate/async"
            logger.info(f"发送终止异步任务请求: {url}, session_id: {session_id}, app_id: {app_id}")

            timeout = httpx.Timeout(30.0)  # 30秒超时
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(url, json={"session_id": session_id})

                if response.status_code == 200:
                    data = response.json()
                    success = data.get("success", False)
                    message = data.get("message", "未知响应")

                    if success:
                        logger.info(f"成功终止异步任务: {session_id}")
                        content = f"✅ 成功终止异步任务\n\n**应用ID**: {app_id}\n**会话ID**: {session_id}\n**状态**: {message}"
                    else:
                        logger.warning(f"终止异步任务失败: {message}")
                        content = f"⚠️ 终止异步任务失败\n\n**应用ID**: {app_id}\n**会话ID**: {session_id}\n**错误**: {message}"
                else:
                    error_msg = f"请求失败 (状态码: {response.status_code}): {response.text}"
                    logger.error(error_msg)
                    content = f"❌ 终止异步任务请求失败\n\n**应用ID**: {app_id}\n**状态码**: {response.status_code}\n**详情**: {response.text}"

                return [{"role": "assistant", "content": content}]

        except httpx.TimeoutException as e:
            logger.error(f"终止异步任务请求超时: {e}")
            content = f"❌ 请求超时\n\n终止异步任务请求在30秒后超时，请稍后重试。"
            return [{"role": "assistant", "content": content}]
        except Exception as e:
            logger.error(f"终止异步任务时发生错误: {e}")
            content = f"❌ 系统错误\n\n终止异步任务时发生未知错误: {str(e)}"
            return [{"role": "assistant", "content": content}]

    async def _get_task_status(self, session_id: str, app_id: str = None) -> List[Dict[str, Any]]:
        """获取任务状态"""
        # 使用传入的session_id（来自session_id_state）
        # 如果没有有效的session_id，提示用户先创建任务
        if not session_id or session_id == "":
            content = "❌ 无法获取任务状态\n\n当前没有活跃的异步任务会话。请先发送一个消息来创建异步任务，然后再尝试获取状态。"
            return [{"role": "assistant", "content": content}]

        # 如果没有提供app_id，提示用户先选择应用
        if not app_id:
            content = "❌ 无法获取任务状态\n\n请先选择一个智能体应用，然后再尝试获取任务状态。"
            return [{"role": "assistant", "content": content}]

        try:
            # 构建正确的应用子路由URL
            url = f"{self.api_base_url}/apps/{app_id}/task/status?session_id={session_id}"
            logger.info(f"发送获取任务状态请求: {url}, session_id: {session_id}, app_id: {app_id}")

            timeout = httpx.Timeout(30.0)  # 30秒超时
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(url)

                if response.status_code == 200:
                    data = response.json()
                    status = data.get("status", "unknown")
                    message = data.get("message", "未知状态")

                    logger.info(f"成功获取任务状态: {session_id}, 状态: {status}")
                    content = f"📊 任务状态信息\n\n**应用ID**: {app_id}\n**会话ID**: {session_id}\n**状态**: {status}\n**详情**: {message}"
                else:
                    error_msg = f"请求失败 (状态码: {response.status_code}): {response.text}"
                    logger.error(error_msg)
                    content = f"❌ 获取任务状态失败\n\n**应用ID**: {app_id}\n**状态码**: {response.status_code}\n**详情**: {response.text}"

                return [{"role": "assistant", "content": content}]

        except httpx.TimeoutException as e:
            logger.error(f"获取任务状态请求超时: {e}")
            content = f"❌ 请求超时\n\n获取任务状态请求在30秒后超时，请稍后重试。"
            return [{"role": "assistant", "content": content}]
        except Exception as e:
            logger.error(f"获取任务状态时发生错误: {e}")
            content = f"❌ 系统错误\n\n获取任务状态时发生未知错误: {str(e)}"
            return [{"role": "assistant", "content": content}]

    def update_timeout_setting(self, value):
        """更新超时设置"""
        try:
            # 确保值是浮点数
            value = float(value)
            # 设置有效范围
            if value < 30.0:
                value = 30.0
            if value > 1200.0:  # 将最大值调整为1200秒（20分钟）
                value = 1200.0

            # 更新超时设置
            self.message_timeout = value
            logger.info(f"更新超时设置为: {value}秒")
            return f"已更新超时设置: {value} 秒"
        except Exception as e:
            logger.error(f"更新超时设置出错: {e}")
            return f"更新超时设置出错: {e}"

    async def _get_app_info(self, app_id: str) -> Optional[Dict[str, Any]]:
        """获取应用的详细信息"""
        if not app_id:
            return None

        try:
            url = f"{self.api_base_url}/apps/{app_id}/info"
            logger.info(f"获取应用信息: {url}")

            # 使用更长的超时时间
            timeout = httpx.Timeout(self.fetch_timeout)
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(url)
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"获取应用信息失败: {response.text}")
                    return None
        except httpx.TimeoutException as e:
            logger.error(f"获取应用信息时超时: {e}")
            logger.error(traceback.format_exc())
            return None
        except Exception as e:
            logger.error(f"获取应用信息时发生错误: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _refresh_app_dropdown(self):
        """刷新应用下拉列表"""
        choices = await self._fetch_all_apps()
        if not choices:
            # 如果没有获取到应用，显示错误消息
            return gr.update(
                choices=[("无法连接到服务器", "error")], value="error", interactive=False
            )
        return gr.update(
            choices=choices, value=choices[0][1] if choices else None, interactive=True
        )

    async def _on_app_selected(self, app_id: str):
        """处理应用选择事件"""
        if not app_id:
            return (gr.update(value="请选择一个智能体应用"), gr.update(value=[]))

        if app_id == "error":
            return (
                gr.update(
                    value=f"""### ❌ 无法连接到服务器

无法连接到API服务器 `{self.api_base_url}`

请检查:
1. API服务器是否正在运行
2. 网络连接是否正常
3. 服务器地址是否正确

点击"刷新应用列表"重试。
"""
                ),
                gr.update(value=[]),
            )

        app_info = await self._get_app_info(app_id)
        if not app_info:
            return (
                gr.update(
                    value=f"""### ❌ 无法获取应用信息

无法获取应用 `{app_id}` 的详细信息。

请检查:
1. API服务器是否正在运行
2. 该应用是否可用
3. 网络连接是否正常

点击"刷新应用列表"重试。
"""
                ),
                gr.update(value=[]),
            )

        # 构建应用信息展示
        app_desc = f"""
## {app_info['name']} v{app_info['version']}

{app_info['description']}

**功能**: {app_info.get('features', '智能对话')}
"""

        # 添加欢迎消息 - 使用新的messages格式
        welcome_message = [
            {
                "role": "assistant",
                "content": f"👋 欢迎使用 **{app_info['name']}**\n\n{app_info['description']}\n\n请输入您的问题开始对话。",
            }
        ]

        return (gr.update(value=app_desc), gr.update(value=welcome_message))

    # 清除消息输入框
    async def _send_and_clear(self, message, app_id, history):
        """发送消息并清除输入框"""
        if not app_id or app_id == "error":
            return history, gr.update(value="", placeholder="请先选择一个可用的智能体应用")
        if not message or message.strip() == "":
            return history, gr.update(value="", placeholder="请输入消息")

        # 调用发送消息方法
        result, session_id = await self._send_message(message, app_id, history)
        return result, ""  # 返回对话历史和空字符串（用于清除输入框）

    def _build_interface(self) -> gr.Blocks:
        """构建Gradio界面"""
        with gr.Blocks(
            title="WAIY智能体框架",
            theme=self.theme,
            css="""
            #header-area {
                text-align: center;
                margin-bottom: 30px;
            }
            #header-area h1 {
                margin-bottom: 0;
            }
            #header-area p {
                margin-top: 0;
                opacity: 0.7;
            }
            footer {
                margin-top: 50px;
                text-align: center;
                opacity: 0.7;
            }
            .chatbot-container {
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .error-message {
                color: #e11d48;
                font-weight: bold;
            }
            .warning-message {
                color: #ea580c;
                font-weight: bold;
            }
            /* 使用CSS隐藏多余的进度条 */
            .progress-container:nth-of-type(n+2) {
                display: none !important;
            }
            .gradio-container .progress {
                z-index: 100 !important;
            }
            /* Web Report样式 */
            .web-report-notification {
                background-color: #f0f4ff;
                border-left: 4px solid #4f46e5;
                padding: 16px;
                margin: 10px 0;
                border-radius: 4px;
            }
            .web-report-notification p {
                margin: 0 0 10px 0;
                font-weight: 500;
            }
            .view-web-report-btn {
                background-color: #4f46e5;
                color: white !important; /* 确保链接颜色也是白色 */
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-weight: 500;
                text-decoration: none; /* 移除链接下划线 */
                display: inline-block; /* 使其表现像按钮 */
                transition: background-color 0.2s;
            }
            .view-web-report-btn:hover {
                background-color: #4338ca;
                text-decoration: none; /* 悬停时也移除下划线 */
            }
            /* 允许Markdown渲染HTML */
            .prose * {
                max-width: 100% !important;
            }
            .prose iframe, .prose img {
                max-width: 100% !important;
                margin: 0 auto;
                display: block;
            }
            """,
        ) as interface:
            with gr.Column(elem_id="header-area"):
                gr.Markdown(
                    """# WAIY智能体框架
                Web UI 交互界面 - 探索智能体的强大功能
                """
                )

            # 隐藏的session_id状态组件
            session_id_state = gr.State(value="")

            with gr.Row():
                with gr.Column(scale=1):
                    # 侧边栏 - 应用选择和信息
                    with gr.Group():
                        app_dropdown = gr.Dropdown(
                            label="选择智能体应用",
                            choices=[],
                            interactive=True,
                            container=True,
                            allow_custom_value=False,
                        )
                        refresh_btn = gr.Button("刷新应用列表", variant="secondary")

                    app_info = gr.Markdown("请选择一个智能体应用")

                    # 显示会话信息
                    with gr.Accordion("会话信息", open=False):
                        session_info = gr.Markdown("**会话ID**: 未开始")

                    # 显示服务器信息
                    with gr.Accordion("服务器信息", open=False):
                        gr.Markdown(f"API服务器: {self.api_base_url}")
                        timeout_info = gr.Markdown(f"消息超时: {self.message_timeout} 秒")

                        # 超时设置滑块
                        timeout_slider = gr.Slider(
                            minimum=30,
                            maximum=1200,  # 将最大值调整为1200秒（20分钟）
                            value=self.message_timeout,
                            step=30,  # 调整步长为30秒
                            label="消息超时时间(秒)",
                        )

                        timeout_status = gr.Markdown(value="")

                        # 更新超时设置的事件
                        timeout_slider.change(
                            self.update_timeout_setting,
                            inputs=[timeout_slider],
                            outputs=[timeout_status],
                        )

                with gr.Column(scale=3, elem_classes="chatbot-container"):
                    # 主区域 - 聊天界面
                    chatbot = gr.Chatbot(
                        height=500,
                        render_markdown=True,
                        show_copy_button=True,
                        bubble_full_width=False,
                        type="messages",  # 使用新的messages格式，避免弃用警告
                        elem_classes="chatbot",
                        sanitize_html=False,  # 允许HTML内容渲染，用于web report
                    )

                    with gr.Row():
                        message_box = gr.Textbox(
                            placeholder="在此输入您的问题或指令...",
                            label="消息",
                            lines=2,
                            scale=10,
                            container=False,
                        )
                        send_btn = gr.Button("发送", variant="primary", scale=1)

                    with gr.Row():
                        clear_btn = gr.Button("清空对话", variant="secondary")
                        terminate_btn = gr.Button("终止异步任务", variant="stop", visible=True)
                        status_btn = gr.Button("获取任务状态", variant="secondary", visible=True)
                        cancel_btn = gr.Button("取消请求", variant="stop", visible=False)

            with gr.Row(elem_id="footer"):
                gr.Markdown(
                    """
                &copy; WAIY智能体框架 - 基于多智能体协同的自动化助手
                """
                )

            # 定义更新会话信息的函数
            def update_session_info(session_id):
                if session_id:
                    return f"**会话ID**: `{session_id}`"
                else:
                    return "**会话ID**: 未开始"

            # 事件处理
            refresh_btn.click(self._refresh_app_dropdown, outputs=[app_dropdown])

            app_dropdown.change(
                self._on_app_selected, inputs=[app_dropdown], outputs=[app_info, chatbot]
            )

            # 修改后的发送和清除函数，支持session_id
            async def send_and_clear_with_session(message, app_id, history, session_id):
                if not app_id or app_id == "error":
                    return history, "", session_id, update_session_info(session_id)
                if not message or message.strip() == "":
                    return history, "", session_id, update_session_info(session_id)

                # 调用发送消息方法
                new_history, new_session_id = await self._send_message(
                    message, app_id, history, session_id
                )
                return new_history, "", new_session_id, update_session_info(new_session_id)

            # 清空对话时也清空session_id
            def clear_conversation():
                return [], "", update_session_info("")

            # 终止异步任务的事件处理
            terminate_btn.click(
                self._terminate_async_task,
                inputs=[session_id_state, app_dropdown],
                outputs=[chatbot],
            )

            # 获取任务状态的事件处理
            status_btn.click(
                self._get_task_status, inputs=[session_id_state, app_dropdown], outputs=[chatbot]
            )

            # 使用修改过的事件处理方法
            msg_events = [message_box.submit, send_btn.click]

            for event in msg_events:
                event(
                    send_and_clear_with_session,
                    inputs=[message_box, app_dropdown, chatbot, session_id_state],
                    outputs=[chatbot, message_box, session_id_state, session_info],
                )

            clear_btn.click(clear_conversation, outputs=[chatbot, session_id_state, session_info])

            # 页面加载时刷新应用列表
            interface.load(self._refresh_app_dropdown, outputs=[app_dropdown])

        return interface

    def launch(self, **kwargs):
        """启动Gradio应用"""
        return self.interface.launch(**kwargs)

    def queue(self):
        """启用队列"""
        return self.interface.queue()

    @property
    def app(self):
        """获取Gradio应用实例"""
        return self.interface


def create_gradio_app(api_base_url: str = None, static_base_url: str = None) -> gr.Blocks:
    """创建Gradio应用实例"""
    web_ui = WaiyWebUI(api_base_url, static_base_url)
    web_ui.queue()
    return web_ui.app


# 导出应用实例，用于挂载到FastAPI
gradio_app = create_gradio_app()
