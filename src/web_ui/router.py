"""
Web UI路由器
用于将Gradio应用集成到FastAPI应用中
"""

import os

import gradio as gr
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles

from ..common.logging import logger
from .app import create_gradio_app


def mount_web_ui(app: FastAPI, path: str = "/web-ui") -> FastAPI:
    """
    将Gradio Web UI挂载到FastAPI应用

    Args:
        app: FastAPI应用实例
        path: 挂载路径，默认为/web-ui

    Returns:
        挂载后的FastAPI应用
    """
    try:
        # 确定web_ui模块的基础目录
        web_ui_base_dir = os.path.dirname(os.path.abspath(__file__))
        # 定义并创建用于存放自定义静态文件的目录 (例如报告)
        custom_static_dir = os.path.join(web_ui_base_dir, "static")
        os.makedirs(custom_static_dir, exist_ok=True)

        # 挂载自定义静态文件目录到FastAPI应用
        # URL路径为 /web-ui-static, 文件系统路径为 custom_static_dir
        app.mount(
            "/web-ui-custom-static",
            StaticFiles(directory=custom_static_dir),
            name="web_ui_custom_static",
        )
        logger.info(f"自定义静态文件目录已挂载到 /web-ui-custom-static")

        # 从环境变量或配置中获取API URL
        api_base_url = os.environ.get("WAIY_API_URL")
        if api_base_url:
            logger.info(f"使用自定义API URL: {api_base_url}")
        else:
            # 使用服务器自身的URL
            server_host = os.environ.get("WAIY_SERVER_HOST", "localhost")
            server_port = os.environ.get("WAIY_SERVER_PORT", "8000")
            api_base_url = f"http://{server_host}:{server_port}"
            logger.info(f"使用默认API URL: {api_base_url}")

        # 创建Gradio应用
        gradio_app = create_gradio_app(api_base_url)

        logger.info(f"正在挂载Web UI到路径: {path}")
        # 将Gradio应用挂载到FastAPI，确保在自定义静态目录挂载之后
        return gr.mount_gradio_app(app, gradio_app, path=path)
    except Exception as e:
        logger.error(f"挂载Web UI失败: {e}")
        raise
