"""
RAG服务客户端
使用无影AI知识库服务进行检索
"""

import time
from typing import Any, Dict, List, Optional

from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_wuyingaiinner20250709 import client
from aliyunaklesscredprovider.core import AklessCredproviderFactory
from alibabacloud_wuyingaiinner20250709.models import (
    GenerateRagRequest,
    GenerateRagRequestQueryParaments,
    GenerateRagRequestQueryParamentsDocMemory,
    GenerateRagRequestQueryParamentsSnippetMemory,
    GenerateRagResponse,
)
from aliyunaklesscredprovider.core.config import CredentialProviderConfig

from src.api.models.resource_models import KnowledgeBaseResult
from src.common.config.settings import ResourceSettings
from src.common.logging import logger
from src.common.config import settings as global_settings

class RAGService:
    """RAG服务客户端 - 基于无影AI"""

    def __init__(self, settings: Optional[ResourceSettings] = None):
        if settings is None:
            self.settings = global_settings.resource
        else:
            self.settings = settings

        # 初始化RAG客户端
        self.client = None
        self._init_client()

    def _init_client(self):
        """初始化RAG客户端"""
        try:
            # 使用显式配置初始化AKless凭证，避免因环境变量缺失导致的空app_name等错误
            cred_client = AklessCredproviderFactory.get_opensdk_v2_credential_with_config(
                CredentialProviderConfig(
                    ram_role_arn=global_settings.service.ram_role,
                    region_id=global_settings.service.region_id,
                    app_name=global_settings.service.app_name,
                    app_env=global_settings.service.app_env,
                )
            )

            config = open_api_models.Config(
                credential=cred_client,
                connect_timeout=self.settings.rag_service_timeout * 1000,  # 转换为毫秒
            )
            config.endpoint = self.settings.rag_endpoint
            self.client = client.Client(config)
            logger.info("RAG客户端初始化成功")
        except Exception as e:
            logger.error(f"RAG客户端初始化失败: {e}")
            raise Exception(f"RAG服务初始化失败: {str(e)}")

    async def search_knowledge_base(
        self, kb_id: str, query: str, query_parameters: Dict[str, Any] = None, top_k: int = 5
    ) -> KnowledgeBaseResult:
        """
        检索单个知识库

        Args:
            kb_id: 知识库ID
            query: 检索查询语句
            query_parameters: 查询参数
            top_k: 返回结果数量

        Returns:
            KnowledgeBaseResult: 检索结果
        """
        return await self.search_knowledge_bases(query, [kb_id], query_parameters)

    async def search_knowledge_bases(
        self, query: str, knowledge_base_ids: List[str], query_parameters: Dict[str, Any] = None
    ) -> KnowledgeBaseResult:
        """
        检索知识库

        Args:
            query: 检索查询语句
            knowledge_base_ids: 知识库ID列表
            query_parameters: 查询参数

        Returns:
            KnowledgeBaseResult: 检索结果
        """
        start_time = time.time()

        try:
            # 构建请求参数

            rag_query_parameters = GenerateRagRequestQueryParaments(
                doc_memory=GenerateRagRequestQueryParamentsDocMemory(enable=True),
                snippet_memory=GenerateRagRequestQueryParamentsSnippetMemory(enable=True),
            )

            # 创建请求对象
            request = GenerateRagRequest(
                kb_ids=knowledge_base_ids, q=query, query_paraments=rag_query_parameters
            )

            logger.info(f"调用RAG服务: query='{query}', kb_ids={knowledge_base_ids}")

            # 调用API
            response = self.client.generate_rag(request)
            search_time = time.time() - start_time

            # 处理响应
            if isinstance(response, GenerateRagResponse):
                result = self._parse_response(response, query, knowledge_base_ids, search_time)
                logger.info(
                    f"RAG服务调用成功: 用时{search_time:.2f}s，返回{result.total_results}个结果"
                )
                return result
            else:
                raise Exception("未能正确收到响应")

        except Exception as e:
            search_time = time.time() - start_time
            logger.error(f"RAG服务调用失败: 用时{search_time:.2f}s，错误: {e}")

            return KnowledgeBaseResult(
                success=False,
                kb_ids=knowledge_base_ids,
                query=query,
                docs=[],
                search_time=search_time,
                error_message=str(e),
            )

    def _parse_response(
        self, response: GenerateRagResponse, query: str, kb_ids: List[str], search_time: float
    ) -> KnowledgeBaseResult:
        """解析RAG服务响应"""
        try:
            # 获取响应体
            response_body = response.body

            # 检查响应状态
            if not response_body or response_body.status != 200:
                raise Exception(
                    f"服务返回错误状态: {response_body.status if response_body else 'None'}"
                )

            if response_body.success != "true":
                error_msg = response_body.message or "未知错误"
                raise Exception(f"服务返回失败: {error_msg}")

            # 解析数据
            data = response_body.data
            if not data:
                return KnowledgeBaseResult(
                    success=True,
                    kb_ids=kb_ids,
                    query=query,
                    docs=[],
                    total_results=0,
                    search_time=search_time,
                )

            # 检查是否有任何可用的记忆数据
            has_doc_memory = hasattr(data, "doc_memory") and data.doc_memory
            has_snippet_memory = hasattr(data, "snippet_memory") and data.snippet_memory
            
            if not has_doc_memory and not has_snippet_memory:
                return KnowledgeBaseResult(
                    success=True,
                    kb_ids=kb_ids,
                    query=query,
                    docs=[],
                    total_results=0,
                    search_time=search_time,
                )

            # 合并所有内容
            all_contents = []
            doc_count = 0
            snippet_count = 0

            # 处理doc_memory数据
            if has_doc_memory:
                doc_memory = data.doc_memory
                docs = doc_memory.docs if hasattr(doc_memory, "docs") else []
                for doc in docs:
                    if hasattr(doc, "merge_content") and doc.merge_content:
                        all_contents.append(doc.merge_content)
                        doc_count += 1

            # 处理snippet_memory数据
            if has_snippet_memory:
                snippet_memory = data.snippet_memory
                snippets = snippet_memory.snippets if hasattr(snippet_memory, "snippets") else []
                for snippet in snippets:
                    if hasattr(snippet, "merge_content") and snippet.merge_content:
                        all_contents.append(snippet.merge_content)
                        snippet_count += 1

            logger.info(f"RAG检索结果: doc_memory={doc_count}条, snippet_memory={snippet_count}条, 总计={len(all_contents)}条")

            return KnowledgeBaseResult(
                success=True,
                kb_ids=kb_ids,
                query=query,
                docs=all_contents,
                total_results=len(all_contents),
                search_time=search_time,
            )

        except Exception as e:
            logger.error(f"解析RAG响应失败: {e}")
            return KnowledgeBaseResult(
                success=False,
                kb_ids=kb_ids,
                query=query,
                docs=[],
                search_time=search_time,
                error_message=f"响应解析失败: {str(e)}",
            )

    async def close(self):
        """关闭客户端连接"""
        # RAG SDK客户端无需显式关闭
        logger.info("RAG服务客户端已关闭")
