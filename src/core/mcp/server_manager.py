"""
MCP服务器管理
"""

import asyncio
import shutil
import uuid
from typing import List, Optional

from agents.mcp import MCPServer, MCPServerSse

from ...common.config import settings
from ...common.logging import logger


class MCPServerManager:
    """MCP服务器管理器"""

    def __init__(self, agentbay_session_id: Optional[str] = None):
        """初始化MCP服务器管理器

        Args:
            session_id: 会话ID，用于wuying服务器连接
        """
        self.servers = []
        self.agentbay_session_id = agentbay_session_id or f"session-{uuid.uuid4()}"
        # 服务器能力映射
        self._server_capabilities = {
            "wuying": self._create_wuying_server,
        }

    async def setup_servers(self) -> List[MCPServer]:
        """
        设置并返回所有MCP服务器

        Returns:
            List[MCPServer]: MCP服务器列表
        """
        logger.info("正在初始化MCP服务器...")

        # 根据配置创建所有已启用的服务器
        server_configs = [{"name": "Wuying服务器", "enabled": True, "cap": "wuying"}]

        capabilities_to_create = [cfg["cap"] for cfg in server_configs if cfg["enabled"]]

        # 创建服务器
        for cap in capabilities_to_create:
            if cap in self._server_capabilities:
                max_retries = 2  # 最大重试次数
                retry_count = 0

                while retry_count <= max_retries:
                    try:
                        # 添加超时控制
                        connect_timeout = 30  # 连接超时时间（秒）
                        server = await asyncio.wait_for(
                            self._server_capabilities[cap](), timeout=connect_timeout
                        )

                        if server:
                            self.servers.append(server)
                            break  # 成功创建服务器，退出重试循环
                    except asyncio.TimeoutError:
                        retry_count += 1
                        if retry_count <= max_retries:
                            logger.error(
                                f"启动{cap}服务器超时，正在重试 ({retry_count}/{max_retries})..."
                            )
                            await asyncio.sleep(2)  # 等待2秒后重试
                        else:
                            # logger.error(f"启动{cap}服务器超时，已达到最大重试次数")
                            logger.error(f"启动{cap}服务器超时，已达到最大重试次数")
                    except Exception as e:
                        # logger.error(f"启动{cap}服务器失败: {e}")
                        logger.error(f"启动{cap}服务器失败", exc_info=True)
                        break  # 其他异常直接退出循环

        logger.info(f"已成功初始化 {len(self.servers)} 个MCP服务器")
        return self.servers

    async def _create_wuying_server(self) -> Optional[MCPServer]:
        """创建Wuying服务器"""
        logger.info("  - 启动Wuying服务器...")
        logger.info(f"  - Wuying服务器地址: {settings.mcp.wuying_server_url}")
        logger.info(f"  - 使用Session ID: {self.agentbay_session_id}")
        wuying_server = MCPServerSse(
            name="WuyingServer",
            cache_tools_list=False,
            params={
                "url": f"{settings.mcp.wuying_server_url}?APIKEY={settings.mcp.wuying_server_api_key}&IMAGEID=linux_latest&SESSIONID={self.agentbay_session_id}",
                "timeout": 20,
            },
            client_session_timeout_seconds=60,
        )
        await wuying_server.connect()

        return wuying_server

    async def cleanup(self):
        """清理所有MCP服务器"""
        logger.info("正在清理MCP服务器...")

        for server in self.servers:
            try:
                await server.cleanup()
            except Exception as e:
                # print_error(f"清理服务器 '{server.name}' 时出错: {e}")
                logger.error(f"清理服务器 '{server.name}' 时出错", exc_info=True)

        self.servers = []
        logger.info("MCP服务器清理完成")
