"""
MCP服务器装饰器
用于修改MCP服务器的行为，例如重命名工具
"""

from typing import Any, Awaitable, Callable, Dict, List, Optional, Union

from agents import AgentBase, RunContextWrapper
from agents.mcp import MCPServer

from ...common.logging import logger


class NameOverridingMCPServer(MCPServer):
    """
    MCP服务器装饰器，用于重写工具名称
    解决不同MCP服务器工具命名冲突问题
    """

    def __init__(self, actual_server: MCPServer):
        """
        初始化一个工具名称重写的 MCP 服务器

        Args:
            actual_server: 实际的 MCP 服务器实例
            prefix: 要添加到所有工具名称前的前缀
        """
        self.actual_server = actual_server
        self.prefix = actual_server.name

    async def connect(self):
        """连接到实际的服务器"""
        return await self.actual_server.connect()

    @property
    def name(self) -> str:
        """返回服务器名称"""
        return self.actual_server.name

    async def cleanup(self):
        """清理实际的服务器"""
        return await self.actual_server.cleanup()

    async def list_tools(
        self,
        run_context: RunContextWrapper[Any],
        agent: AgentBase[Any],
    ) -> list:
        """列出工具，并为每个工具名称添加前缀"""
        tools = await self.actual_server.list_tools(run_context, agent)
        prefixed_tools = []

        for tool in tools:
            # 创建一个修改过名称的工具副本
            # 由于我们不能直接导入 MCPTool，所以我们需要复制工具的所有属性
            tool_dict = tool.__dict__.copy()
            tool_dict["name"] = f"{self.prefix}.{tool.name}"

            # 创建一个新的工具对象
            prefixed_tool = type(tool)(**tool_dict)
            prefixed_tools.append(prefixed_tool)

        return prefixed_tools

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] | None):
        """调用工具，去掉工具名称中的前缀"""
        # 如果工具名称以前缀开头，则去掉前缀
        if tool_name.startswith(f"{self.prefix}."):
            actual_tool_name = tool_name[len(f"{self.prefix}.") :]
            return await self.actual_server.call_tool(actual_tool_name, arguments)

        # 否则直接调用原始工具
        return await self.actual_server.call_tool(tool_name, arguments)

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.actual_server.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        """异步上下文管理器退出"""
        return await self.actual_server.__aexit__(exc_type, exc_value, traceback)
