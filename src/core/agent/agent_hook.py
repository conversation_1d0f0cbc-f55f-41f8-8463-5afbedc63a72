import json
import re
from typing import Any, Optional

from agents import AgentBase
from agents.lifecycle import <PERSON><PERSON><PERSON>s
from agents.run_context import RunContextWrapper
from agents.tool import Tool

from src.common.logging import logger, plan_logger
from src.common.config.llm_config import get_model_name


class LoggingAndJsonHook(AgentHooks):
    """记录agent活动并处理JSON格式的钩子"""

    def __init__(self, truncate_length: int = 100):
        """初始化钩子"""
        self.truncate_length = truncate_length

    @staticmethod
    def _clean_json(text: str) -> Any:
        """清理JSON输出，移除markdown标记"""
        if not isinstance(text, str):
            return text

        # 检查markdown代码块
        if match := re.search(r"^```(?:json)?(.*?)```$", text, re.DOTALL):
            try:
                return json.loads(match.group(1).strip())
            except json.JSONDecodeError:
                pass

        # 尝试直接解析
        if text.strip().startswith("{") and text.strip().endswith("}"):
            try:
                return json.loads(text)
            except json.JSONDecodeError:
                pass

        return text

    def _truncate(self, text: str) -> str:
        """截断文本"""
        return f"{text[:self.truncate_length]}..." if len(text) > self.truncate_length else text

    async def on_start(self, context: RunContextWrapper, agent: AgentBase) -> None:
        """agent开始运行时调用"""
        # 获取 trace_id
        trace_id = getattr(context, 'trace_id', 'unknown')

        # 规划日志：Agent开始
        plan_logger.bind(trace_id=trace_id).info(f"[PLAN] AgentStart name={agent.name}")

        logger.info(f"Agent启动: {agent.name}")
        logger.debug(f"指令: {agent.instructions}")

        # 打印本次模型调用的最终提示词（instructions + 历史 + 本轮输入）
        try:
            prompt = []
            if getattr(agent, "instructions", None):
                prompt.append({"role": "system", "content": agent.instructions})
                # 规划日志：记录系统指令
                plan_logger.bind(trace_id=trace_id).info(f"[STEP_DETAIL] agent={agent.name} system_instructions={agent.instructions[:500]}...")

            # 会话历史
            items = []
            if hasattr(context, "session") and context.session is not None:
                try:
                    items = await context.session.get_items()
                except Exception:
                    items = []
            if items:
                prompt.extend(items)
                # 规划日志：记录会话历史数量
                plan_logger.bind(trace_id=trace_id).info(f"[STEP_DETAIL] agent={agent.name} session_history_count={len(items)}")

            # 当前输入
            if getattr(context, "input", None):
                prompt.append({"role": "user", "content": context.input})
                # 规划日志：本轮输入
                trace_id = getattr(context, 'trace_id', 'unknown')
                plan_logger.bind(trace_id=trace_id).info(f"[STEP_INPUT] agent={agent.name} input={context.input}")
            # 组装并打印完整日志（不截断）
            prompt_json = json.dumps(prompt, ensure_ascii=False)
            logger.info(f"[PROMPT] {agent.name}: {prompt_json}")
        except Exception as e:
            logger.warning(f"打印prompt失败: {e}")
            # 规划日志：记录错误
            plan_logger.bind(trace_id=trace_id).info(f"[STEP_ERROR] agent={agent.name} prompt_error={str(e)}")

        # 记录工具信息
        if agent.tools:
            logger.info(f"工具数量: {len(agent.tools)}")
            tool_names = [tool.name for tool in agent.tools]
            # 规划日志：记录可用工具
            for i, tool in enumerate(agent.tools):
                logger.info(f"工具{i+1}: {tool.name} - {getattr(tool, 'description', '无描述')}")

        # 记录MCP服务器
        if servers := getattr(agent, "mcp_servers", None):
            logger.info(f"MCP服务器数量: {len(servers)}")
            # 规划日志：记录MCP服务器数量
            for i, server in enumerate(servers):
                logger.debug(
                    f"服务器{i+1}: {getattr(server, 'name', f'server-{i}')} - "
                    f"状态: {'已连接' if getattr(server, 'is_connected', False) else '未连接'}"
                )

                # 获取服务器工具
                if hasattr(server, "list_tools") and callable(server.list_tools):
                    try:
                        tools = server.list_tools(context, agent)
                        names = [getattr(t, "name", f"tool-{i}") for i, t in enumerate(tools)]
                        logger.debug(f"服务器{i+1}工具: {names}")
                        # 规划日志：记录MCP工具
                    except Exception as e:
                        logger.warning(f"获取服务器{i+1}工具失败: {e}")
                        # 规划日志：记录MCP错误
                        plan_logger.bind(trace_id=trace_id).info(f"[STEP_ERROR] agent={agent.name} mcp_server_{i+1}_error={str(e)}")

        # 记录输出类型和模型信息
        logger.debug(f"输出类型: {agent.output_type.__name__ if agent.output_type else 'str'}")
        # 规范化模型名
        try:
            model_obj = getattr(agent, 'model', None)
            model_name = get_model_name(model_obj) if model_obj else '默认'
        except Exception:
            model_name = str(getattr(agent, 'model', '默认'))
        logger.debug(f"模型: {model_name}")
        # 规划日志：记录模型信息（可读）
        plan_logger.bind(trace_id=trace_id).info(f"[STEP_DETAIL] agent={agent.name} model={model_name}")

        # 记录模型设置
        if settings := getattr(agent, "model_settings", None):
            logger.debug(
                f"模型设置: 温度={getattr(settings, 'temperature', '默认')}, "
                f"最大令牌={getattr(settings, 'max_tokens', '默认')}"
            )
            # 规划日志：记录模型设置

    async def on_end(self, context: RunContextWrapper, agent: AgentBase, output: Any) -> None:
        """agent产生最终输出时调用"""
        # 获取 trace_id
        trace_id = getattr(context, 'trace_id', 'unknown')

        # 规划日志：Agent输出
        output_str = str(output)
        plan_logger.bind(trace_id=trace_id).info(f"[STEP_OUTPUT] agent={agent.name} output={output_str[:2000]}")

        # 如果输出内容较长，记录完整的计划内容
        if len(output_str) > 2000:
            plan_logger.bind(trace_id=trace_id).info(f"[PLAN_DETAIL] agent={agent.name} full_output={output_str}")

        logger.info(f"Agent完成: {agent.name}")

        # # 处理JSON输出
        # if agent.output_type and agent.output_type is not str:
        #     cleaned = self._clean_json(output)
        #     if cleaned != output and hasattr(context.context, 'cleaned_output'):
        #         context.context.cleaned_output = cleaned
        #         logger.info("已清理JSON格式")

        # 记录输出和使用情况
        logger.info(f"输出: {output}")
        # 安全地访问usage信息，避免Pydantic验证错误
        try:
            if hasattr(context, "usage") and context.usage is not None:
                input_tokens = getattr(context.usage, "input_tokens", None) or 0
                output_tokens = getattr(context.usage, "output_tokens", None) or 0
                total_tokens = getattr(context.usage, "total_tokens", None) or 0
                logger.info(
                    f"使用情况: 输入={input_tokens}, "
                    f"输出={output_tokens}, "
                    f"总计={total_tokens}"
                )
                # 规划日志：记录token使用情况
                plan_logger.bind(trace_id=trace_id).info(f"[STEP_DETAIL] agent={agent.name} token_usage=input:{input_tokens},output:{output_tokens},total:{total_tokens}")
            else:
                logger.warning("Usage信息不可用")
                # 规划日志：记录usage不可用
                plan_logger.bind(trace_id=trace_id).info(f"[STEP_WARNING] agent={agent.name} usage_unavailable=true")
        except Exception as e:
            logger.warning(f"获取usage信息失败: {e}")
            # 规划日志：记录usage错误
            plan_logger.bind(trace_id=trace_id).info(f"[STEP_ERROR] agent={agent.name} usage_error={str(e)}")

    async def on_handoff(self, context: RunContextWrapper, agent: AgentBase, source: AgentBase) -> None:
        """控制权交接时调用"""
        # 获取 trace_id
        trace_id = getattr(context, 'trace_id', 'unknown')

        # 规划日志：记录控制权交接
        plan_logger.bind(trace_id=trace_id).info(f"[PLAN] AgentHandoff from={source.name} to={agent.name}")

        logger.info(f"交接: {source.name} -> {agent.name}")
        logger.debug(f"工具数量: {len(agent.tools)}")
        logger.debug(f"工具列表: {[t.name for t in agent.tools]}")

        # 规划日志：记录交接后的工具信息
        tool_names = [t.name for t in agent.tools]
        plan_logger.bind(trace_id=trace_id).info(f"[STEP_DETAIL] agent={agent.name} handoff_tools={tool_names}")

        if servers := getattr(agent, "mcp_servers", None):
            logger.debug(f"MCP服务器数量: {len(servers)}")
            # 规划日志：记录交接后的MCP服务器
            plan_logger.bind(trace_id=trace_id).info(f"[STEP_DETAIL] agent={agent.name} handoff_mcp_servers={len(servers)}")

    async def on_tool_start(self, context: RunContextWrapper, agent: AgentBase, tool: Tool) -> None:
        """工具调用开始时"""
        # 获取 trace_id
        trace_id = getattr(context, 'trace_id', 'unknown')

        # 规划日志：工具调用开始
        plan_logger.bind(trace_id=trace_id).info(f"[PLAN] ToolStart agent={agent.name} tool={tool.name}")

        # 规划日志：记录工具调用的详细信息
        tool_description = getattr(tool, 'description', '无描述')
        plan_logger.bind(trace_id=trace_id).info(f"[STEP_DETAIL] agent={agent.name} tool={tool.name} description={tool_description}")

        logger.info(f"工具调用: {agent.name} 使用 {tool.name}")

    async def on_tool_end(
        self, context: RunContextWrapper, agent: AgentBase, tool: Tool, result: str
    ) -> None:
        """工具调用完成时"""
        # 获取 trace_id
        trace_id = getattr(context, 'trace_id', 'unknown')

        # 规划日志：工具调用结束
        result_str = str(result)
        plan_logger.bind(trace_id=trace_id).info(f"[STEP_OUTPUT] agent={agent.name} tool={tool.name} result={result_str[:1000]}")

        # 如果结果较长，记录完整结果
        if len(result_str) > 1000:
            plan_logger.bind(trace_id=trace_id).info(f"[STEP_DETAIL] agent={agent.name} tool={tool.name} full_result={result_str}")

        logger.info(f"工具完成: {agent.name} 的 {tool.name}")
        logger.debug(f"结果: {result}")
