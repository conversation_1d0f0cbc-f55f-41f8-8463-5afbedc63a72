import json
import re
from typing import Any, Optional

from agents import AgentBase
from agents.lifecycle import Agent<PERSON>ooks
from agents.run_context import RunContextWrapper
from agents.tool import Tool

from src.common.logging import logger


class LoggingAndJsonHook(AgentHooks):
    """记录agent活动并处理JSON格式的钩子"""

    def __init__(self, truncate_length: int = 100):
        """初始化钩子"""
        self.truncate_length = truncate_length

    @staticmethod
    def _clean_json(text: str) -> Any:
        """清理JSON输出，移除markdown标记"""
        if not isinstance(text, str):
            return text

        # 检查markdown代码块
        if match := re.search(r"^```(?:json)?(.*?)```$", text, re.DOTALL):
            try:
                return json.loads(match.group(1).strip())
            except json.JSONDecodeError:
                pass

        # 尝试直接解析
        if text.strip().startswith("{") and text.strip().endswith("}"):
            try:
                return json.loads(text)
            except json.JSONDecodeError:
                pass

        return text

    def _truncate(self, text: str) -> str:
        """截断文本"""
        return f"{text[:self.truncate_length]}..." if len(text) > self.truncate_length else text

    async def on_start(self, context: RunContextWrapper, agent: AgentBase) -> None:
        """agent开始运行时调用"""
        logger.info(f"Agent启动: {agent.name}")
        logger.debug(f"指令: {agent.instructions}")

        # 打印本次模型调用的最终提示词（instructions + 历史 + 本轮输入）
        try:
            prompt = []
            if getattr(agent, "instructions", None):
                prompt.append({"role": "system", "content": agent.instructions})
            # 会话历史
            items = []
            if hasattr(context, "session") and context.session is not None:
                try:
                    items = await context.session.get_items()
                except Exception:
                    items = []
            if items:
                prompt.extend(items)
            # 当前输入
            if getattr(context, "input", None):
                prompt.append({"role": "user", "content": context.input})
            # 组装并打印完整日志（不截断）
            prompt_json = json.dumps(prompt, ensure_ascii=False)
            logger.info(f"[PROMPT] {agent.name}: {prompt_json}")
        except Exception as e:
            logger.warning(f"打印prompt失败: {e}")

        # 记录工具信息
        if agent.tools:
            logger.info(f"工具数量: {len(agent.tools)}")
            for i, tool in enumerate(agent.tools):
                logger.info(f"工具{i+1}: {tool.name} - {getattr(tool, 'description', '无描述')}")

        # 记录MCP服务器
        if servers := getattr(agent, "mcp_servers", None):
            logger.info(f"MCP服务器数量: {len(servers)}")
            for i, server in enumerate(servers):
                logger.debug(
                    f"服务器{i+1}: {getattr(server, 'name', f'server-{i}')} - "
                    f"状态: {'已连接' if getattr(server, 'is_connected', False) else '未连接'}"
                )

                # 获取服务器工具
                if hasattr(server, "list_tools") and callable(server.list_tools):
                    try:
                        tools = server.list_tools(context, agent)
                        names = [getattr(t, "name", f"tool-{i}") for i, t in enumerate(tools)]
                        logger.debug(f"服务器{i+1}工具: {names}")
                    except Exception as e:
                        logger.warning(f"获取服务器{i+1}工具失败: {e}")

        # 记录输出类型和模型信息
        logger.debug(f"输出类型: {agent.output_type.__name__ if agent.output_type else 'str'}")
        logger.debug(f"模型: {getattr(agent, 'model', '默认')}")

        # 记录模型设置
        if settings := getattr(agent, "model_settings", None):
            logger.debug(
                f"模型设置: 温度={getattr(settings, 'temperature', '默认')}, "
                f"最大令牌={getattr(settings, 'max_tokens', '默认')}"
            )

    async def on_end(self, context: RunContextWrapper, agent: AgentBase, output: Any) -> None:
        """agent产生最终输出时调用"""
        logger.info(f"Agent完成: {agent.name}")

        # # 处理JSON输出
        # if agent.output_type and agent.output_type is not str:
        #     cleaned = self._clean_json(output)
        #     if cleaned != output and hasattr(context.context, 'cleaned_output'):
        #         context.context.cleaned_output = cleaned
        #         logger.info("已清理JSON格式")

        # 记录输出和使用情况
        logger.info(f"输出: {output}")
        # 安全地访问usage信息，避免Pydantic验证错误
        try:
            if hasattr(context, "usage") and context.usage is not None:
                input_tokens = getattr(context.usage, "input_tokens", None) or 0
                output_tokens = getattr(context.usage, "output_tokens", None) or 0
                total_tokens = getattr(context.usage, "total_tokens", None) or 0
                logger.info(
                    f"使用情况: 输入={input_tokens}, "
                    f"输出={output_tokens}, "
                    f"总计={total_tokens}"
                )
            else:
                logger.warning("Usage信息不可用")
        except Exception as e:
            logger.warning(f"获取usage信息失败: {e}")

    async def on_handoff(self, context: RunContextWrapper, agent: AgentBase, source: AgentBase) -> None:
        """控制权交接时调用"""
        logger.info(f"交接: {source.name} -> {agent.name}")
        logger.debug(f"工具数量: {len(agent.tools)}")
        logger.debug(f"工具列表: {[t.name for t in agent.tools]}")

        if servers := getattr(agent, "mcp_servers", None):
            logger.debug(f"MCP服务器数量: {len(servers)}")

    async def on_tool_start(self, context: RunContextWrapper, agent: AgentBase, tool: Tool) -> None:
        """工具调用开始时"""
        logger.info(f"工具调用: {agent.name} 使用 {tool.name}")

    async def on_tool_end(
        self, context: RunContextWrapper, agent: AgentBase, tool: Tool, result: str
    ) -> None:
        """工具调用完成时"""
        logger.info(f"工具完成: {agent.name} 的 {tool.name}")
        logger.debug(f"结果: {result}")
