import json
import time
from typing import Any, Dict, List, Optional, Set

from agents import AgentBase
from agents.lifecycle import RunHooks
from agents.run_context import RunContextWrapper
from agents.tool import Tool

from src.common.logging import logger


class RunTrackingHook(RunHooks):
    """记录完整运行链路的钩子，跟踪所有事件和调用"""

    def __init__(self, truncate_length: int = 100):
        """初始化运行链路跟踪钩子

        Args:
            truncate_length: 日志中文本截断长度
        """
        self.truncate_length = truncate_length
        self.start_time = time.time()
        self.agents_executed: List[str] = []
        self.tools_executed: List[str] = []
        self.execution_path: List[str] = []
        self.handoff_count = 0
        self.tool_call_count = 0
        self.active_agent: Optional[str] = None

    def _truncate(self, text: str) -> str:
        """截断文本"""
        return f"{text[:self.truncate_length]}..." if len(text) > self.truncate_length else text
    
    def _log_execution_summary(self) -> None:
        """记录执行摘要"""
        elapsed = time.time() - self.start_time
        logger.info(f"执行摘要 - 耗时: {elapsed:.2f}秒")
        logger.info(f"执行摘要 - Agent数量: {len(self.agents_executed)}")
        logger.info(f"执行摘要 - 工具调用: {self.tool_call_count}次")
        logger.info(f"执行摘要 - 交接次数: {self.handoff_count}次")

        if self.agents_executed:
            logger.info(f"执行摘要 - Agent链路: {' -> '.join(self.agents_executed)}")

        if self.tools_executed:
            logger.info(f"执行摘要 - 工具链路: {', '.join(self.tools_executed)}")

        # 记录完整执行路径
        if self.execution_path:
            logger.debug("执行摘要 - 完整路径:")
            for i, step in enumerate(self.execution_path):
                logger.debug(f"  步骤{i+1}: {step}")

    async def on_agent_start(self, context: RunContextWrapper, agent: AgentBase) -> None:
        """Agent开始执行时触发"""
        self.active_agent = agent.name

        if agent.name not in self.agents_executed:
            self.agents_executed.append(agent.name)

        logger.info(f"Agent开始: {agent.name}")
        self.execution_path.append(f"Agent: {agent.name}")

        # 记录Agent配置
        logger.debug(
            f"Agent配置: 工具数量={len(agent.tools)}, "
            f"输出类型={agent.output_type.__name__ if agent.output_type else 'str'}"
        )

    async def on_agent_end(self, context: RunContextWrapper, agent: AgentBase, output: Any) -> None:
        """Agent执行结束时触发"""
        logger.info(f"Agent结束: {agent.name}")
        self.execution_path.append(f"Agent完成: {agent.name}")

        # 记录输出摘要
        output_summary = self._truncate(str(output))
        logger.info(f"Agent输出: {output_summary}")

        # 安全地记录使用情况，避免Pydantic验证错误
        try:
            if hasattr(context, "usage") and context.usage is not None:
                input_tokens = getattr(context.usage, "input_tokens", None) or 0
                output_tokens = getattr(context.usage, "output_tokens", None) or 0
                logger.info(f"Agent使用情况: 输入={input_tokens}, " f"输出={output_tokens}")
            else:
                logger.warning("Agent使用情况: Usage信息不可用")
        except Exception as e:
            logger.warning(f"获取Agent使用情况失败: {e}")

    async def on_tool_start(self, context: RunContextWrapper, agent: AgentBase, tool: Tool) -> None:
        """工具即将被调用时触发"""
        # 记录工具调用
        tool_identifier = f"{tool.name}@{agent.name}"
        if tool.name not in self.tools_executed:
            self.tools_executed.append(tool.name)

        self.execution_path.append(f"工具: {tool.name}")
        self.tool_call_count += 1

        logger.info(f"工具调用: {agent.name} 正在调用 {tool.name}")

        # 尝试记录工具参数
        if hasattr(context, "messages") and context.messages:
            last_message = context.messages[-1] if context.messages else None
            if last_message and hasattr(last_message, "content"):
                logger.debug(f"最近的消息内容: {self._truncate(str(last_message.content))}")

    async def on_tool_end(
        self, context: RunContextWrapper, agent: AgentBase, tool: Tool, result: Any
    ) -> None:
        """工具调用完成时触发"""
        logger.info(f"工具完成: {agent.name} 的 {tool.name}")
        self.execution_path.append(f"工具完成: {tool.name}")

        # 记录结果摘要
        result_summary = self._truncate(str(result))
        logger.debug(f"工具结果: {result_summary}")

    async def on_handoff(self, context: RunContextWrapper, source: AgentBase, target: AgentBase) -> None:
        """Agent交接时触发"""
        self.handoff_count += 1
        logger.info(f"交接: {source.name} -> {target.name}")
        self.execution_path.append(f"交接: {source.name} -> {target.name}")

        # 记录交接详情
        logger.debug(f"交接详情: 源工具数量={len(source.tools)}, 目标工具数量={len(target.tools)}")

        # 如果目标agent是新的，添加到执行列表
        if target.name not in self.agents_executed:
            self.agents_executed.append(target.name)

        self.active_agent = target.name
