"""
日期时间工具函数
提供统一的日期时间格式化功能
"""
from datetime import datetime, timezone, timedelta


def get_formatted_datetime():
    """
    获取格式化的日期时间字符串
    
    格式：10:45 AM 星期四，2025 年 8 月 21 日 [Asia/Shanghai;-480]
    
    Returns:
        str: 格式化的日期时间字符串
    """
    # 创建上海时区 (UTC+8)
    shanghai_tz = timezone(timedelta(hours=8))
    # 获取当前上海时间
    now_shanghai = datetime.now(shanghai_tz)
    
    # 星期映射表
    weekdays = {
        0: "星期一", 1: "星期二", 2: "星期三", 3: "星期四",
        4: "星期五", 5: "星期六", 6: "星期日"
    }
    
    # 格式化时间部分 (12小时制)
    time_str = now_shanghai.strftime("%I:%M %p")
    # 获取星期
    weekday_str = weekdays[now_shanghai.weekday()]
    # 格式化日期部分
    date_str = f"{now_shanghai.year} 年 {now_shanghai.month} 月 {now_shanghai.day} 日"
    # 时区信息
    timezone_str = "[Asia/Shanghai;-480]"
    
    return f"{time_str} {weekday_str}，{date_str} {timezone_str}"
