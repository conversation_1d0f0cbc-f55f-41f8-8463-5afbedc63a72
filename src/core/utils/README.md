# 异步执行器使用指南

## 概述

为了解决同步SDK调用阻塞事件循环影响并发性能的问题，我们实现了异步执行器工具，将同步操作包装为异步调用。

## 核心组件

### 1. AsyncExecutor 类

位置：`src/core/utils/async_executor.py`

主要功能：
- 使用 ThreadPoolExecutor 在独立线程中执行同步操作
- 支持超时控制
- 单例模式，全局共享线程池
- 自动配置管理

### 2. 异步包装器

- `AsyncAgentBayWrapper`: AgentBay SDK 的异步包装器
- `AsyncSessionWrapper`: Session 对象的异步包装器

### 3. 配置管理

在 `settings.py` 中新增 `ExecutorSettings`：
- `max_workers`: 线程池大小（默认10，范围1-100）
- `timeout`: 默认超时时间（默认300秒，范围10-3600）

## 使用方法

### 基本用法

```python
from src.core.utils.async_executor import async_executor

# 异步执行同步函数
result = await async_executor.run(sync_function, arg1, arg2, keyword_arg=value)

# 带超时的执行
result = await async_executor.run(sync_function, arg1, timeout=60)
```

### AgentBay SDK 异步包装

```python
from src.core.utils.async_executor import (
    create_async_agentbay_wrapper,
    create_async_session_wrapper
)

# 包装 AgentBay 实例
agent_bay = AgentBay(api_key, config)
async_agent_bay = create_async_agentbay_wrapper(agent_bay)

# 异步调用
context_result = await async_agent_bay.get_context(session_id, True)
session_result = await async_agent_bay.create_session(session_params)

# 包装 Session 实例
session = Session(agent_bay, session_id)
async_session = create_async_session_wrapper(session)

# 异步调用
info_result = await async_session.get_info()
task_result = await async_session.execute_task(message, timeout_s)
sync_result = await async_session.sync_context()
```

## 配置选项

### 环境变量配置

```bash
# 设置线程池大小
EXECUTOR_MAX_WORKERS=20

# 设置超时时间（秒）
EXECUTOR_TIMEOUT=600
```

### 代码配置

```python
from src.common.config import settings

# 查看当前配置
print(f"线程池大小: {settings.executor.max_workers}")
print(f"超时时间: {settings.executor.timeout}秒")
```

## 性能优化建议

1. **线程池大小**：
   - CPU密集型任务：设置为CPU核心数
   - I/O密集型任务：可以设置为CPU核心数的2-4倍
   - 对于AgentBay SDK调用，建议设置为10-20

2. **超时设置**：
   - 根据实际业务需求调整
   - AgentBay任务通常需要较长时间，建议300秒以上

3. **并发控制**：
   - 避免创建过多并发任务导致线程池饱和
   - 使用 `asyncio.Semaphore` 限制并发数量

## 测试和监控

运行性能测试：
```python
# 运行演示和性能测试
python -m src.core.utils.async_executor_demo
```

监控指标：
- 线程池使用率
- 任务执行时间
- 超时发生频率
- 并发任务数量

## 注意事项

1. **线程安全**：确保被包装的同步函数是线程安全的
2. **资源管理**：应用关闭时会自动清理线程池
3. **异常处理**：超时会抛出 `asyncio.TimeoutError`
4. **内存使用**：线程池会占用额外内存，合理设置线程数量

## 迁移指南

### 从同步调用迁移

**修改前**：
```python
# 直接调用同步函数（阻塞事件循环）
result = agent_bay.context.get(session_id, True)
task_result = session.agent.execute_task(message, timeout_s)
```

**修改后**：
```python
# 使用异步包装器（不阻塞事件循环）
async_agent_bay = create_async_agentbay_wrapper(agent_bay)
result = await async_agent_bay.get_context(session_id, True)

async_session = create_async_session_wrapper(session)
task_result = await async_session.execute_task(message, timeout_s)
```

这样的改进可以显著提升Web应用的并发性能，特别是在处理多个用户请求时。
