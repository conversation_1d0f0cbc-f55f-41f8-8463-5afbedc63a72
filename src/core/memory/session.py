from agents.memory import Session
from typing import List
from .initialization import get_memory
from memory import Memory, Message, Role
from ...common.logging import logger


class WaiySession:
    """Custom session implementation following the Session protocol."""
    memory: Memory
    session_id: str
    user_id: str = None
    user_input: str = None
    def __init__(self, session_id: str, user_id: str = None, user_input: str = None):
        self.session_id = session_id
        self.user_id = user_id
        self.user_input = user_input
        self.memory = get_memory()

    async def get_items(self, limit: int | None = None) -> List[dict]:
        """Retrieve conversation history for this session."""
        messages = self.memory.list_messages(session_id=self.session_id, page_size=10000)
        result = []
        
        for message in messages.get("messages", []):
            if limit is not None and len(result) >= limit:
                break
                
            msg: Message = message
            if msg.role == Role.USER and msg.content:
                result.append({
                    "role": "user",
                    "content": msg.content,
                })
            elif msg.role == Role.ASSISTANT and msg.content:
                result.append({
                    "role": "assistant",
                    "content": msg.content,
                })
        if self.user_id and self.user_input:
            memory_context = self.memory.search_memory(self.user_input, self.user_id, 5)
            if memory_context:
                result.append({
                    "role": "system",
                    "content": f"# 用户背景信息\n\n{memory_context}\n\n请基于用户的历史偏好和对话记录，"
                                "提供符合其需求的个性化回答。如果背景信息与当前问题相关，请适当引用；如果不相"
                                "关，则正常回答即可。",
                })
        # Log the number of items retrieved and resulting content
        logger.info(f"Retrieved {len(result)} items from session {self.session_id}. Content: {result}")
        return result
    async def add_items(self, items: List[dict]) -> None:
        """Store new items for this session."""
        # Filter messages - only allow user and assistant roles
        if self.user_id:
            filtered_items = []
            for item in items:
                if not isinstance(item, dict):
                    continue
                    
                role = item.get("role", "").lower()
                content = item.get("content", "")
                
                # Extract text from assistant content if it's a list with type 'output_text'
                if role == "assistant" and isinstance(content, list):
                    text_content = ""
                    for content_item in content:
                        if isinstance(content_item, dict) and content_item.get("type") == "output_text":
                            text_content = content_item.get("text", "")
                            break
                    content = text_content
                
                # Only keep user and assistant messages with content
                if role in ["user", "assistant"] and content:
                    filtered_items.append({
                        "role": role,
                        "content": content
                    })
            self.memory.add_memory(
                user_id=self.user_id,
                messages=filtered_items
            )
    async def pop_item(self) -> dict | None:
        """Remove and return the most recent item from this session."""
        # Your implementation here
        pass

    async def clear_session(self) -> None:
        """Clear all items for this session."""
        # Your implementation here
        pass