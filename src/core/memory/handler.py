from datetime import datetime
from typing import Any, Dict, List, Optional

from memory import Function, Memory, Message, Role, ToolCall
from memory.events import (
    TextMessageStartEvent,
    TextMessageContentEvent,
    TextMessageEndEvent,
    Event,
    EventType
)
from src.common.logging import logger


class MemoryHandler:
    """处理消息并存储到Memory中"""
    memory : Memory
    def __init__(self, memory: Memory, use_adk: bool = False, db_url: Optional[str] = None):
        """
        初始化内存处理器

        Args:
            memory: 内存对象
            use_adk: 是否使用ADK会话管理器保存消息
            db_url: 数据库URL，如果use_adk为True但不提供此参数，将使用默认配置
        """
        self.memory = memory
        self.active_traces: Dict[str, Dict[str, Any]] = {}
        self.active_generations: Dict[str, str] = {}

    def store_message(self, info: Optional[Dict[str, Any]] = None) -> None:
        """创建消息并存储到Memory"""
        if not info:
            logger.warning("No information provided to store_message.")
            return

        try:
            if info.get("object") == "trace.span":
                self.process_trace_span(info)
            elif info.get("object") == "trace":
                self.process_trace(info)
            else:
                logger.warning(f"Unsupported object type: {info.get('object')}")
        except Exception as e:
            logger.error(f"Error processing the information: {info}")
            logger.error(f"Error processing the information: {e}")

    def process_trace_span(self, info: Dict[str, Any]) -> None:
        """处理 'trace.span' 类型的信息"""
        span_id = info.get("id", "")
        trace_id = info.get("trace_id", "")
        trace_info = self.active_traces.get(trace_id, {})
        session_id = trace_info.get("session_id", "")
        msg = Message(run_id=trace_id, session_id=session_id)
        logger.info(f"Processing trace span with trace_id: {msg.run_id}")

        if msg.run_id in self.active_traces:
            msg.app_id = self.active_traces[msg.run_id].get("name", "")

        span_data = info.get("span_data", {})
        span_type = span_data.get("type", "").lower()

        if span_type == "function":
            self.handle_function_span(span_data, msg)
        elif span_type == "generation":
            if info.get("ended_at"):
                self.handle_generation_end_span(span_id, span_data, msg)
            else:
                self.handle_generation_start_span(span_id, span_data, msg)
        else:
            logger.warning(f"other span type, just skip: {span_type}")

    def set_tool_call_id(self, msg, span_data: dict) -> None:
        """设置消息的tool_call_id"""
        try:
            tool_call_events = self.memory.list_events(
                session_id=msg.session_id,
                content_filter=span_data.get("input", ""),
                tool_name_filter=msg.tool_name,
                page_size=1,
                order_by='desc'
            )
            events = tool_call_events.get("events", [])
            msg.tool_call_id = (
                events[0].tool_call_id 
                if events and hasattr(events[0], 'tool_call_id') 
                else "unknown"
        )
        except Exception as e:
            logger.error(f"Error setting tool_call_id: {e}")
            msg.tool_call_id = "unknown"
    def handle_function_span(self, span_data: Dict[str, Any], msg: Message) -> None:
        """处理 'Function' 类型的 span"""
        msg.role = Role.TOOL
        msg.tool_name = span_data.get("name", "unknown")
        msg.content = span_data.get("output", "")
        self.set_tool_call_id(msg, span_data)
        # 添加到内存
        if not span_data.get("ext_info", {}).get('disable_memory'):
            self.memory.add_message(msg)

        logger.info(f"Stored function message: {msg}")

    def handle_generation_start_span(self, span_id: str, span_data: Dict[str, Any], msg: Message) -> None:
        """处理 'Generation' 类型的 span"""
        event = TextMessageStartEvent(role="assistant", session_id=msg.session_id, run_id=msg.run_id)
        self.active_generations[span_id] = event.message_id
        msg.ext_info = span_data.get("ext_info", {})
        if not span_data.get("ext_info", {}).get('disable_memory'):
            self.memory.add_event(event)
    def handle_generation_end_span(self, span_id: str, span_data: Dict[str, Any], msg: Message) -> None:
        """处理 'Generation' 类型的 span"""
        msg.role = Role.ASSISTANT
        msg.message_id = self.active_generations.get(span_id)
        self.parse_output(span_data.get("output", []), msg)
        if not span_data.get("ext_info", {}).get('disable_memory'):
            self.memory.add_message(msg)
        if span_id in self.active_generations:
            del self.active_generations[span_id]

    def process_trace(self, info: Dict[str, Any]) -> None:
        """处理 'trace' 类型的信息"""
        ended_at = info.get("ended_at", "")
        session_id = info.get("session_id", None)
        trace_id = info.get("trace_id", "")
        app_name = info.get("name", "")

        if not ended_at and session_id:
            # print_info(f"Storing active trace with trace_id: {trace_id}")
            self.active_traces[trace_id] = info
        else:
            if trace_id in self.active_traces:
                del self.active_traces[trace_id]

    def calculate_duration_in_seconds(self, started_at: str, ended_at: str) -> float:
        """
        计算两个ISO格式时间字符串之间的秒数

        Args:
            started_at: 开始时间的ISO格式字符串
            ended_at: 结束时间的ISO格式字符串

        Returns:
            时间差，以秒为单位
        """
        # 将时间字符串解析为 datetime 对象
        start_time = datetime.fromisoformat(started_at)
        end_time = datetime.fromisoformat(ended_at)

        # 计算时间差
        duration = end_time - start_time

        # 返回时间差的总秒数
        return duration.total_seconds()

    def parse_output(self, data: List[dict], message: Message) -> None:
        tool_calls = []

        # 检查data是否为有效列表
        if not data or not isinstance(data, list):
            return

        # 遍历 output 列表
        for item in data:
            if not item or not isinstance(item, dict):
                continue
            output = item.get("output", None)
            if isinstance(output, list):
                return self.parse_output(output, message)
            if item.get("role") == "assistant" and not item.get("tool_calls"):
                # 找到助手消息，并更新传入的 message 对象
                content = item.get("content", "")
                if isinstance(content, list) and len(content) > 0:
                    message.content = content[0].get("text", "")
                else:
                    message.content = content
            elif (
                item.get("role") == "assistant"
                and item.get("tool_calls")
                and len(item["tool_calls"]) > 0
            ):
                # 将工具调用转换为 ToolCall，支持多个工具调用
                for tool_call in item["tool_calls"]:
                    id = tool_call.get("id", "unknown")
                    name = tool_call.get("function", {}).get("name", "unknown")
                    arguments = tool_call.get("function", {}).get("arguments", "{}")
                    tool_calls.append(
                        ToolCall(
                            id=id,
                            function=Function(name=name, arguments=arguments),
                        )
                    )
            elif item.get("arguments"):
                id = item.get("call_id", "unknown")
                name = item.get("name", "unknown")
                arguments = item.get("arguments", "{}")
                tool_calls.append(
                    ToolCall(
                        id=id,
                        function=Function(name=name, arguments=arguments),
                    )
                )


        # 更新传入的消息的 tool_calls
        message.tool_calls = tool_calls
