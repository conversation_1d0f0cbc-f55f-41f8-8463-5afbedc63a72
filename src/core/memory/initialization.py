"""
Memory初始化模块
"""

import logging
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)

# 全局Memory配置缓存
_memory_config: Optional[Dict[str, Any]] = None

# 全局Memory实例（单例）
_global_memory: Optional["Memory"] = None


def _load_memory_config_from_settings() -> None:
    """
    从settings中加载Memory配置
    """
    global _memory_config

    try:
        from src.common.config import settings

        # 从settings获取配置并转换为storage_configs格式
        memory_settings = settings.memory

        storage_configs = []

        # 根据配置优先级生成存储配置
        for storage_type in memory_settings.storage_preference:
            if storage_type == "mem0":
                config = {
                    "type": "mem0",
                    "config": {
                        "dashscope_api_key": memory_settings.mem0_dashscope_api_key,
                        "es_host": memory_settings.mem0_es_host,
                        "es_port": memory_settings.mem0_es_port,
                        "es_user": memory_settings.mem0_es_user,
                        "es_password": memory_settings.mem0_es_password,
                        "embedding_model": memory_settings.mem0_embedding_model,
                    },
                }
            elif storage_type == "database":
                # 检查数据库配置是否完整
                required_db_fields = ["db_host", "db_user", "db_password", "db_database"]
                missing_db_fields = []
                for field in required_db_fields:
                    if not getattr(memory_settings, field, ""):
                        missing_db_fields.append(field)

                if missing_db_fields:
                    logger.warning(f"数据库配置缺失必要字段: {missing_db_fields}，跳过数据库存储")
                    continue

                config = {
                    "type": "database",
                    "config": {
                        "host": memory_settings.db_host,
                        "port": memory_settings.db_port,
                        "user": memory_settings.db_user,
                        "password": memory_settings.db_password,
                        "database": memory_settings.db_database,
                    },
                }
            elif storage_type == "memory":
                config = {"type": "memory", "config": {}}
            else:
                continue

            storage_configs.append(config)
            logger.info(f"添加存储配置: {storage_type}")

        # 如果没有找到任何可用存储，默认使用内存存储
        if not storage_configs:
            storage_configs = [{"type": "memory", "config": {}}]
            logger.info("使用默认内存存储")

        # 从settings获取MQ配置
        mq_config = None
        if hasattr(memory_settings, "mq_enabled") and memory_settings.mq_enabled:
            try:
                mq_config = {
                    "enabled": True,
                    "type": getattr(memory_settings, "mq_type", "rocketmq"),
                    "config": {
                        "endpoint": memory_settings.mq_endpoint,
                        "access_key": memory_settings.mq_access_key,
                        "secret_key": memory_settings.mq_secret_key,
                        "instance_id": memory_settings.mq_instance_id,
                        "topic": getattr(memory_settings, "mq_topic", "waiy_memory_messages"),
                        "group_id": getattr(memory_settings, "mq_group_id", "GID_WAIY_MEMORY"),
                        "enable_listening": False
                    },
                }

                # 检查必要配置是否存在
                required_fields = ["endpoint", "instance_id"]
                missing_fields = []
                for field in required_fields:
                    if not mq_config["config"].get(field):
                        missing_fields.append(f"mq_{field}")

                if missing_fields:
                    logger.warning(f"MQ配置缺失必要字段: {missing_fields}，禁用消息队列")
                    mq_config = None
                else:
                    logger.info("MQ配置加载成功")

            except AttributeError as e:
                logger.warning(f"MQ配置字段缺失: {e}，禁用消息队列")
                mq_config = None
        else:
            logger.debug("MQ功能未启用或未配置")

        _memory_config = {"storage_configs": storage_configs, "mq_config": mq_config}

        logger.info("Memory配置加载成功")

    except ImportError as e:
        logger.warning(f"无法导入settings，使用默认配置: {e}")
        _memory_config = {"storage_configs": [{"type": "memory", "config": {}}], "mq_config": None}
    except Exception as e:
        logger.error(f"加载Memory配置失败: {e}")
        _memory_config = {"storage_configs": [{"type": "memory", "config": {}}], "mq_config": None}


def get_memory() -> "Memory":
    """
    获取全局配置的Memory实例（单例模式）
    这是应用中获取Memory的推荐方式

    Returns:
        Memory: 已配置的Memory实例
    """
    global _global_memory

    # 如果全局实例已存在，直接返回
    if _global_memory is not None:
        return _global_memory

    # 确保配置已加载
    if _memory_config is None:
        _load_memory_config_from_settings()

    from memory import create_memory

    # 创建全局Memory实例
    _global_memory = create_memory(
        storage_configs=_memory_config["storage_configs"], mq_config=_memory_config.get("mq_config")
    )

    logger.info(f"创建全局Memory实例 config: {_memory_config}")
    return _global_memory


def reset_memory() -> None:
    """
    重置全局Memory实例
    主要用于测试或重新初始化场景
    """
    global _global_memory, _memory_config

    if _global_memory is not None:
        try:
            _global_memory.close()
        except Exception as e:
            logger.error(f"关闭Memory实例失败: {e}")

    _global_memory = None
    _memory_config = None
    logger.info("全局Memory实例已重置")


def is_memory_initialized() -> bool:
    """
    检查Memory是否已初始化

    Returns:
        bool: True表示已初始化
    """
    return _global_memory is not None
