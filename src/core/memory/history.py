"""
历史记录管理
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# from src.common.config import settings
from src.common.logging import logger


class HistoryManager:
    """历史记录管理类"""

    def __init__(self, history_file: Optional[Path] = None):
        """
        初始化历史记录管理器

        Args:
            history_file: 历史记录文件路径，如果不提供则使用配置中的默认路径
        """
        from src.common.config import settings

        self.history_file = history_file or settings.history.history_file

        # 确保父目录存在
        self.history_file.parent.mkdir(parents=True, exist_ok=True)

    def load_history(self) -> List[Dict[str, Any]]:
        """
        加载任务历史记录

        Returns:
            List[Dict[str, Any]]: 历史记录列表
        """
        if Path(self.history_file).exists():
            try:
                with open(self.history_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except json.JSONDecodeError:
                logger.warning(f"历史记录文件 {self.history_file} 格式错误，返回空列表")
                # 如果文件损坏，返回空列表
                return []
        return []

    def save_history(self, history: List[Dict[str, Any]]):
        """
        保存任务历史记录

        Args:
            history: 要保存的历史记录列表
        """
        try:
            with open(self.history_file, "w", encoding="utf-8") as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存历史记录失败: {e}")

    def add_entry(self, task: str, result: str):
        """
        添加任务到历史记录

        Args:
            task: 任务描述
            result: 任务结果
        """
        history = self.load_history()

        from src.common.config import settings

        # 添加新条目
        history.append(
            {
                "timestamp": datetime.now().isoformat(),
                "task": task,
                "result": result[: settings.history.max_result_length]
                + ("..." if len(result) > settings.history.max_result_length else ""),
            }
        )

        # 限制历史记录大小
        if len(history) > settings.history.max_history_entries:
            history = history[-settings.history.max_history_entries :]

        self.save_history(history)

    def clear_history(self):
        """清除所有历史记录"""
        self.save_history([])

    def get_formatted_history(self, limit: int = 5) -> str:
        """
        获取格式化的历史记录

        Args:
            limit: 要返回的历史记录数量

        Returns:
            str: 格式化的历史记录字符串
        """
        history = self.load_history()
        recent = history[-limit:] if history else []

        if not recent:
            return "没有历史记录。"

        result = "最近的任务历史：\n\n"
        for i, entry in enumerate(recent, 1):
            time_str = datetime.fromisoformat(entry["timestamp"]).strftime("%Y-%m-%d %H:%M:%S")
            result += f"{i}. [{time_str}] 任务: {entry['task']}\n"
            result += f"   结果: {entry['result']}\n\n"

        return result

    def get_recent_tasks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的任务记录

        Args:
            limit: 要返回的记录数量

        Returns:
            List[Dict[str, Any]]: 最近的任务记录列表
        """
        history = self.load_history()
        return history[-limit:] if history else []
