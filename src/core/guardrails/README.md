# WAIY护栏系统

基于OpenAI Agents Python SDK的护栏机制实现的智能内容检测系统。

## 功能特性

- **自动集成**: WaiyBaseAgent自动集成输入和输出护栏，无需手动调用
- **内容检测**: 基于淘宝MTEE API的内容安全检测
- **多层检测**: 基础规则检测 + API智能检测
- **灵活配置**: 支持环境变量和动态配置
- **异常安全**: 检测异常时不阻断正常流程

## 核心组件

### 1. WaiyGuardrail
输入护栏检测类，提供输入内容的安全检测功能。

### 2. WaiyOutputGuardrail
输出护栏检测类，提供输出内容的安全检测功能。

### 3. waiy_input_guardrail & waiy_output_guardrail
基于OpenAI Agents SDK的护栏装饰器函数，集成到Agent的输入/输出检测流程中。

### 4. WaiyBaseAgent
扩展的Agent基类，自动集成WAIY输入和输出护栏功能。

## 使用方法

### 自动护栏模式（推荐）

```python
from src.core.agent.baseclass import WaiyBaseAgent

# 创建Agent时自动启用输入和输出护栏（默认行为）
agent = WaiyBaseAgent(
    name="MyAgent",
    instructions="You are a helpful assistant."
    # 自动添加 WAIY 输入和输出护栏
)
```

### 自定义护栏

```python
from src.core.guardrails import waiy_input_guardrail, waiy_output_guardrail

# 使用自定义护栏组合
agent = WaiyBaseAgent(
    name="MyAgent",
    instructions="You are a helpful assistant.",
    input_guardrails=[waiy_input_guardrail, my_custom_input_guardrail],
    output_guardrails=[waiy_output_guardrail, my_custom_output_guardrail]
)

# 仅使用自定义护栏
agent = WaiyBaseAgent(
    name="MyAgent",
    instructions="You are a helpful assistant.",
    input_guardrails=[my_custom_input_guardrail],
    output_guardrails=[my_custom_output_guardrail]
)
```

### 选择性禁用护栏

```python
# 仅禁用输入护栏（保留输出护栏）
agent = WaiyBaseAgent(
    name="MyAgent",
    instructions="You are a helpful assistant.",
    input_guardrails=[]  # 禁用输入护栏
    # output_guardrails 自动设置为 [waiy_output_guardrail]
)

# 仅禁用输出护栏（保留输入护栏）
agent = WaiyBaseAgent(
    name="MyAgent", 
    instructions="You are a helpful assistant.",
    output_guardrails=[]  # 禁用输出护栏
    # input_guardrails 自动设置为 [waiy_input_guardrail]
)
```

### 完全禁用护栏

```python
# 禁用所有护栏
agent = WaiyBaseAgent(
    name="MyAgent",
    instructions="You are a helpful assistant.",
    input_guardrails=[],   # 禁用输入护栏
    output_guardrails=[]   # 禁用输出护栏
)
```

### 在应用中创建Agent

```python
# 在继承BaseApplication的应用中
async def create_agent(self, use_custom_guardrails=False):
    """创建智能体实例"""
    if use_custom_guardrails:
        # 使用自定义护栏
        agent = WaiyBaseAgent(
            name="研究助手",
            instructions="你是一个专业的研究助手...",
            input_guardrails=[my_custom_input_guardrail],
            output_guardrails=[my_custom_output_guardrail],
            # 其他配置...
        )
    else:
        # 使用默认护栏（自动添加WAIY护栏）
        agent = WaiyBaseAgent(
            name="研究助手",
            instructions="你是一个专业的研究助手...",
            # 其他配置...
        )
    return agent
```

## 配置选项

### 环境变量配置

```bash
# 护栏开关
GUARDRAIL_ENABLED=true

# 严格模式（疑似违规也会拦截）
GUARDRAIL_STRICT_MODE=false

# 内容检测API配置
GUARDRAIL_CONTENT_CHECK_APP_KEY=34686242
GUARDRAIL_CONTENT_CHECK_APP_SECRET=your_secret_here
GUARDRAIL_CONTENT_CHECK_ENDPOINT=http://gw.api.taobao.com/router/rest
GUARDRAIL_CONTENT_CHECK_TIMEOUT=10
```

### 代码配置

```python
from src.common.config.settings import settings

# 访问护栏配置
config = settings.guardrail
print(f"护栏启用状态: {config.enabled}")
print(f"严格模式: {config.strict_mode}")
```

## 检测结果

### 检测码含义

- `-1`: 未命中检测规则
- `0`: 内容正常
- `1`: 内容违规
- `3`: 内容疑似违规

### 处理逻辑

#### 输入护栏
1. **正常内容**: 继续执行Agent逻辑
2. **违规内容**: 触发tripwire，抛出InputGuardrailTripwireTriggered异常
3. **疑似违规**: 
   - 严格模式: 触发tripwire
   - 普通模式: 继续执行
4. **检测异常**: 记录日志，继续执行（避免误拦截）

#### 输出护栏
1. **正常内容**: 正常输出结果
2. **违规内容**: 触发tripwire，抛出OutputGuardrailTripwireTriggered异常
3. **疑似违规**: 
   - 严格模式: 触发tripwire
   - 普通模式: 正常输出
4. **检测异常**: 记录日志，正常输出（避免误拦截）

## 日志示例

```
INFO - 已为WaiyBaseAgent自动添加WAIY输入护栏
INFO - 已为WaiyBaseAgent自动添加WAIY输出护栏
INFO - 全局WaiyGuardrail实例初始化成功
INFO - 全局WaiyOutputGuardrail实例初始化成功
INFO - WaiyGuardrail检测通过: 内容正常
INFO - WaiyOutputGuardrail检测通过: 内容正常
WARNING - WaiyGuardrail检测不通过: 内容违规
WARNING - WaiyOutputGuardrail检测不通过: 内容违规
ERROR - WaiyGuardrail护栏执行异常: API timeout
ERROR - WaiyOutputGuardrail护栏执行异常: API timeout
```

## 架构图

```
用户输入 → waiy_input_guardrail → WaiyGuardrail → 内容检测API
                                                ↓
                                        基础规则检测
                                                ↓
                                        检测结果处理
                                                ↓
                                           Agent执行
                                                ↓
                                       waiy_output_guardrail → WaiyOutputGuardrail → 内容检测API
                                                                        ↓
                                                                基础规则检测
                                                                        ↓
                                                                检测结果处理
                                                                        ↓
                                                            输出结果/拦截/异常处理
```

## 注意事项

1. **性能影响**: 每次输入都会进行检测，会增加响应时间
2. **API依赖**: 依赖外部内容检测API，需要网络连接
3. **异常处理**: 检测异常时不会阻断正常流程
4. **配置管理**: 建议通过环境变量管理敏感配置
5. **日志监控**: 建议监控护栏相关日志，及时发现问题