"""
输入护栏实现

提供输入内容的安全检测和过滤功能
"""

from typing import Optional, Dict, Any
from dataclasses import dataclass

from src.common.logging import logger
from .content_checker import ContentChecker, ContentCheckResult
from .exceptions import GuardrailViolationException


@dataclass
class GuardrailResult:
    """护栏检测结果"""
    is_safe: bool
    message: str
    confidence: float
    details: Optional[Dict[str, Any]] = None


class WaiyGuardrail:
    """
    WAIY输入护栏
    
    对用户输入进行安全检测，防止恶意或不当内容
    """
    
    def __init__(self, 
                 app_key: Optional[str] = None,
                 app_secret: Optional[str] = None,
                 enabled: bool = True,
                 strict_mode: bool = False):
        """
        初始化输入护栏
        
        Args:
            app_key: 内容检测API的app_key
            app_secret: 内容检测API的app_secret
            enabled: 是否启用护栏检测
            strict_mode: 严格模式，疑似违规也会被拦截
        """
        self.enabled = enabled
        self.strict_mode = strict_mode
        
        self.content_checker = None
        if app_key and app_secret:
            self.content_checker = ContentChecker(app_key, app_secret)
        else:
            logger.warning("Content checker not configured, input guardrail will use basic checks only")
    
    def check_input(self, content: str, user_id: str) -> GuardrailResult:
        """
        检查输入内容
        
        Args:
            content: 用户输入内容
            
        Returns:
            GuardrailResult: 检测结果
            
        Raises:
            GuardrailViolationException: 当检测到违规内容时
        """
        if not self.enabled:
            return GuardrailResult(
                is_safe=True,
                message="护栏已禁用",
                confidence=1.0
            )
        
        # 内容检测API检查
        if self.content_checker:
            try:
                check_result = self.content_checker.check_content(content, user_id,  "query")
                
                # 根据检测结果决定是否安全
                is_safe = check_result.is_safe
                
                # 严格模式下，疑似违规也会被拦截
                if self.strict_mode and check_result.code == 3:
                    is_safe = False
                
                result = GuardrailResult(
                    is_safe=is_safe,
                    message=check_result.message,
                    confidence=check_result.confidence,
                    details={
                        "check_code": check_result.code,
                        "api_result": check_result
                    }
                )
                
                if not is_safe:
                    raise GuardrailViolationException(
                        result.message + " " + str(result.details),
                        "CONTENT_VIOLATION",
                        result.confidence
                    )
                
                return result
                
            except GuardrailViolationException:
                raise
            except Exception:
                raise
        else:
            logger.error("Content checker not configured")
        return GuardrailResult(
            is_safe=True,
            message="Content checker not configured",
            confidence=1.0
        )
        