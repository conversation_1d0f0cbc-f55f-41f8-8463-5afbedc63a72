"""
护栏中间件

为主程序和应用集成护栏功能
"""

from typing import Dict, Any, Optional

from src.common.config.settings import settings
from src.common.logging import logger
from .input_guardrail import WaiyGuardrail, GuardrailResult
from .exceptions import GuardrailViolationException


class GuardrailMiddleware:
    """
    护栏中间件
    
    提供输入护栏检测的集成功能
    """
    
    def __init__(self):
        self._input_guardrail: Optional[WaiyGuardrail] = None
        self._initialized = False
    
    def initialize(self) -> bool:
        """
        初始化护栏中间件
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            guardrail_config = settings.guardrail
            
            if not guardrail_config.enabled:
                logger.info("护栏功能已禁用")
                self._initialized = True
                return True
            
            # 初始化输入护栏
            self._input_guardrail = WaiyGuardrail(
                app_key=guardrail_config.content_check_app_key,
                app_secret=guardrail_config.content_check_app_secret,
                enabled=guardrail_config.enabled,
                strict_mode=guardrail_config.strict_mode
            )
            
            self._initialized = True
            logger.info("护栏中间件初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"护栏中间件初始化失败: {e}", exc_info=True)
            return False
    
    def check_input(self, content: str) -> GuardrailResult:
        """
        检查输入内容
        
        Args:
            content: 用户输入内容
            
        Returns:
            GuardrailResult: 检测结果
            
        Raises:
            GuardrailViolationException: 当检测到违规内容时
        """
        if not self._initialized:
            if not self.initialize():
                # 初始化失败时返回安全结果，避免误拦截
                return GuardrailResult(
                    is_safe=True,
                    message="护栏未初始化，跳过检测",
                    confidence=0.0
                )
        
        if not self._input_guardrail:
            # 护栏被禁用
            return GuardrailResult(
                is_safe=True,
                message="护栏已禁用",
                confidence=1.0
            )
        
        try:
            return self._input_guardrail.check_input(content)
        except GuardrailViolationException:
            # 重新抛出违规异常
            raise
        except Exception as e:
            logger.error(f"护栏检测异常: {e}", exc_info=True)
            # 异常时返回安全结果，避免误拦截
            return GuardrailResult(
                is_safe=True,
                message=f"护栏检测异常: {str(e)}",
                confidence=0.0
            )
    
    def process_with_guardrail(self, content: str) -> Dict[str, Any]:
        """
        带护栏的内容处理
        
        Args:
            content: 用户输入内容
            
        Returns:
            Dict[str, Any]: 处理结果，包含是否通过和相关信息
        """
        try:
            result = self.check_input(content)
            
            return {
                "passed": result.is_safe,
                "message": result.message,
                "confidence": result.confidence,
                "details": result.details
            }
            
        except GuardrailViolationException as e:
            return {
                "passed": False,
                "message": str(e),
                "violation_type": e.violation_type,
                "confidence": e.confidence,
                "details": None
            }


# 全局护栏中间件实例
guardrail_middleware = GuardrailMiddleware()