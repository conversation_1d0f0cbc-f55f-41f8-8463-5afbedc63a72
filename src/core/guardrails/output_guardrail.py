"""
输出护栏实现

提供输出内容的安全检测和过滤功能
"""

from typing import Optional, Dict, Any
from dataclasses import dataclass

from src.common.logging import logger
from .content_checker import ContentChecker, ContentCheckResult
from .exceptions import GuardrailViolationException


@dataclass
class OutputGuardrailResult:
    """输出护栏检测结果"""
    is_safe: bool
    message: str
    confidence: float
    original_output: str
    details: Optional[Dict[str, Any]] = None


class WaiyOutputGuardrail:
    """
    WAIY输出护栏
    
    对智能体输出进行安全检测和过滤，防止不当内容输出
    """

    def __init__(self, 
                 app_key: Optional[str] = None,
                 app_secret: Optional[str] = None,
                 enabled: bool = True,
                 strict_mode: bool = False):
        """
        初始化输出护栏
        
        Args:
            app_key: 内容检测API的app_key
            app_secret: 内容检测API的app_secret
            enabled: 是否启用护栏检测
            strict_mode: 严格模式，疑似违规也会被拦截
        """
        self.enabled = enabled
        self.strict_mode = strict_mode

        self.content_checker = None
        if app_key and app_secret:
            self.content_checker = ContentChecker(app_key, app_secret)
        else:
            logger.warning("Content checker not configured, output guardrail will use basic checks only")

    def check_output(self, content: str, user_id: str) -> OutputGuardrailResult:
        """
        检查输出内容
        
        Args:
            content: 智能体输出内容
            
        Returns:
            OutputGuardrailResult: 检测结果
            
        Raises:
            GuardrailViolationException: 当检测到违规内容且非过滤模式时
        """
        if not self.enabled:
            return OutputGuardrailResult(
                is_safe=True,
                message="护栏已禁用",
                confidence=1.0,
                original_output=content
            )

        # 内容检测API检查
        if self.content_checker:
            try:
                check_result = self.content_checker.check_content(content, user_id, "response")

                # 根据检测结果决定是否安全
                is_safe = check_result.is_safe

                # 严格模式下，疑似违规也会被拦截
                if self.strict_mode and check_result.code == 3:
                    is_safe = False

                if not is_safe:
                    result = OutputGuardrailResult(
                        is_safe=False,
                        message=check_result.message,
                        confidence=check_result.confidence,
                        original_output=content,
                        details={
                            "check_code": check_result.code,
                            "api_result": check_result
                        }
                    )

                    raise GuardrailViolationException(
                        result.message,
                        "OUTPUT_CONTENT_VIOLATION",
                        result.confidence
                    )

                return OutputGuardrailResult(
                    is_safe=True,
                    message=check_result.message,
                    confidence=check_result.confidence,
                    original_output=content,
                    details={
                        "check_code": check_result.code,
                        "api_result": check_result
                    }
                )

            except GuardrailViolationException:
                raise
            except Exception as e:
                logger.error(f"Output content check failed: {e}", exc_info=True)

        return OutputGuardrailResult(
            is_safe=True, message=f"API检查失败", confidence=1.0, original_output=content
        )