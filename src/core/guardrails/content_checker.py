"""
内容检测器

基于green.py的检测逻辑实现内容安全检测
"""

import json
import hmac
import requests
from datetime import datetime
from hashlib import sha256
from typing import Dict, Any, Optional
from dataclasses import dataclass

from src.common.logging import logger


@dataclass
class ContentCheckResult:
    """内容检测结果"""
    code: int  # -1: 未命中, 0: 正常, 1: 违规, 3: 疑似
    message: str
    is_safe: bool
    confidence: float = 1.0


class ContentChecker:
    """
    内容检测器
    
    基于淘宝MTEE内容检测API实现
    """
    
    def __init__(self, app_key: str, app_secret: str, endpoint: str = "http://gw.api.taobao.com/router/rest"):
        self.app_key = app_key
        self.app_secret = app_secret
        self.endpoint = endpoint
        
    def _get_sign(self, param_dict: Dict[str, Any], request_dict: Dict[str, Any]) -> str:
        """
        生成HMAC-SHA256签名
        
        基于util.py的签名逻辑
        """
        all_params = param_dict.copy()
        all_params.update(request_dict)
        
        keys = list(all_params.keys())
        keys.sort()
        
        parameters = ''.join(f'{k}{all_params[k]}' for k in keys if not isinstance(all_params[k], bytes))
        sign = hmac.new(
            self.app_secret.encode("utf-8"), 
            parameters.encode("utf-8"), 
            digestmod=sha256
        ).hexdigest().upper()
        
        return sign
    
    def check_content(self, content: str, user_id: str, generate_stage: str = "query", timeout: int = 10) -> ContentCheckResult:
        """
        检测内容安全性
        
        Args:
            content: 要检测的内容
            timeout: 请求超时时间
            
        Returns:
            ContentCheckResult: 检测结果
        """
        try:
            # 构建公共参数
            public_param = {
                "method": "taobao.mtee.requestservice.content.aigc.request",
                "app_key": self.app_key,
                "format": "json",
                "v": "2.0",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "simplify": "true",
                "sign_method": "hmac-sha256"
            }
            
            # 构建业务参数
            ctx = {
                "r": {
                    "srcId": "aipc" + user_id,
                    "businessName": "ali.china.aliyun_cto",
                    "firstProductName": "wy_copilot_2c",
                    "csiAigc": {
                        "sceneType": "txt2txt",
                        "generateStage": generate_stage,
                        "userInputTexts": [
                            {
                                "content": content
                            }
                        ]
                    }
                }
            }
            
            method_param = {
                "code": "aliyun_wy_copilot_2c_aigc_mtee_sns_unify_check",
                "ctx": json.dumps(ctx)
            }
            
            # 生成签名
            sign_value = self._get_sign(public_param.copy(), method_param)
            public_param["sign"] = sign_value
            
            # 合并所有参数
            all_params = public_param.copy()
            all_params.update(method_param)
            
            # 发送请求
            response = requests.post(
                self.endpoint, 
                data=all_params, 
                timeout=timeout, 
                verify=True
            )
            
            if response.status_code != 200:
                logger.error(f"Content check API returned status {response.status_code}")
                return ContentCheckResult(
                    code=-1,
                    message="API请求失败",
                    is_safe=True,  # 默认认为安全，避免误拦截
                    confidence=0.0
                )
            
            response_body = json.loads(response.text)
            
            if "error_response" in response_body:
                logger.error(f"Content check API error: {response_body['error_response']}")
                return ContentCheckResult(
                    code=-1,
                    message="API返回错误",
                    is_safe=True,
                    confidence=0.0
                )
            
            if "result" not in response_body:
                logger.error("Content check API missing result field")
                return ContentCheckResult(
                    code=-1,
                    message="API响应格式错误",
                    is_safe=True,
                    confidence=0.0
                )
            
            result = json.loads(response_body["result"])
            
            if not (result.get("code") == "SUCCESS"):
                logger.error(f"Content check failed: {result}")
                return ContentCheckResult(
                    code=-1,
                    message="内容检测失败",
                    is_safe=True,
                    confidence=0.0
                )
            
            # 解析检测结果
            check_code = result.get("result", -1)
            
            # 根据检测码判断安全性
            # -1: 未命中, 0: 正常, 1: 违规, 3: 疑似
            if check_code == '0':
                return ContentCheckResult(
                    code=check_code,
                    message="内容正常",
                    is_safe=True,
                    confidence=1.0
                )
            elif check_code == '1':
                return ContentCheckResult(
                    code=check_code,
                    message="内容违规: " + str(result),
                    is_safe=False,
                    confidence=1.0
                )
            elif check_code == '3':
                return ContentCheckResult(
                    code=check_code,
                    message="内容疑似违规",
                    is_safe=False,
                    confidence=0.7
                )
            else:  # -1 或其他
                return ContentCheckResult(
                    code=check_code,
                    message="未命中检测规则",
                    is_safe=True,
                    confidence=0.5
                )
                
        except Exception as e:
            logger.error(f"Content check exception: {e}", exc_info=True)
            return ContentCheckResult(
                code=-1,
                message=f"检测异常: {str(e)}",
                is_safe=True,  # 异常时默认安全，避免误拦截
                confidence=0.0
            )