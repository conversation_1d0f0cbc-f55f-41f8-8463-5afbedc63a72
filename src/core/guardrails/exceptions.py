"""
护栏异常定义
"""

from typing import Optional


class GuardrailException(Exception):
    """护栏基础异常"""
    
    def __init__(self, message: str, code: Optional[str] = None):
        super().__init__(message)
        self.code = code


class GuardrailViolationException(GuardrailException):
    """护栏违规异常"""
    
    def __init__(self, message: str, violation_type: str, confidence: float = 1.0):
        super().__init__(message, "VIOLATION")
        self.violation_type = violation_type
        self.confidence = confidence