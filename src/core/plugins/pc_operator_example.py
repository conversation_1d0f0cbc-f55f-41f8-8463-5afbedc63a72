"""
测试AlphaApp中的pc_operator集成
"""

import asyncio
import os
from typing import Any, Dict

from src.applications.alpha.app import AlphaApp
from src.common.config.llm_config import LLMConfig


async def test_alpha_app_with_pc_operator():
    """测试AlphaApp中的pc_operator集成"""

    # 创建测试context
    test_context = {
        "session_id": "test_session_123",
        "agentbay_token": "test_api_key_here",
        "trace_id": "test_trace_456",
    }

    # 创建AlphaApp实例
    app = AlphaApp(context=test_context)

    print("=== 测试AlphaApp PC Operator集成 ===")
    print(f"Session ID: {app.session_id}")
    print(f"PC Context Session ID: {app.pc_context.session_id}")
    print(f"PC Context API Key: {app.pc_context.api_key}")

    # 初始化应用
    print("\n正在初始化应用...")
    success = await app.initialize()
    if not success:
        print("❌ 应用初始化失败")
        return

    print("✅ 应用初始化成功")

    # 测试消息处理
    test_messages = ["请帮我查看当前目录的文件列表", "请帮我创建一个测试文件", "请帮我查看系统信息"]

    for i, message in enumerate(test_messages, 1):
        print(f"\n--- 测试消息 {i} ---")
        print(f"用户输入: {message}")

        try:
            result = await app.process_message(message)
            print(f"AI回复: {result.get('response', '无回复')}")
            print(f"处理轮次: {result.get('turns', 0)}")
        except Exception as e:
            print(f"❌ 处理消息时出错: {str(e)}")

    # 清理资源
    await app.cleanup()
    print("\n✅ 测试完成")


async def test_pc_context_creation():
    """测试PC Context创建"""

    print("\n=== 测试PC Context创建 ===")

    # 测试不同的context配置
    test_cases = [
        {"name": "完整配置", "context": {"session_id": "session_1", "agentbay_token": "token_1"}},
        {"name": "缺少token", "context": {"session_id": "session_2"}},
        {"name": "缺少session_id", "context": {"agentbay_token": "token_3"}},
    ]

    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        try:
            app = AlphaApp(context=test_case["context"])
            print(f"Session ID: {app.session_id}")
            print(f"PC Context Session ID: {app.pc_context.session_id}")
            print(f"PC Context API Key: {app.pc_context.api_key}")
            print("✅ 创建成功")
        except Exception as e:
            print(f"❌ 创建失败: {str(e)}")


if __name__ == "__main__":
    print("开始测试AlphaApp PC Operator集成...")

    # 运行测试
    asyncio.run(test_pc_context_creation())
    asyncio.run(test_alpha_app_with_pc_operator())

    print("\n🎉 所有测试完成！")
