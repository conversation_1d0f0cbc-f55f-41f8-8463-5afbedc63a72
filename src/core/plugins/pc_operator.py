"""
PC操作插件
提供基于AgentBay的PC操作功能

使用示例:
    from src.core.plugins.pc_operator import pc_operator, PCAgentContext

    # 创建context
    context = PCAgentContext(
        session_id="my_session_123",
        api_key="your_api_key_here"
    )

    # 在智能体中使用
    agent = Agent[PCAgentContext](
        name="PC操作智能体",
        instructions="你可以使用pc_operator工具来执行PC操作",
        tools=[pc_operator]
    )

    result = await Runner.run(
        starting_agent=agent,
        input="请帮我查看当前目录",
        context=context,
    )
"""

import json
from typing import Any, Dict

from agentbay import AgentBay, Config, CreateSessionParams, Session
from agentbay.context_sync import ContextSync
from agents import RunContextWrapper, function_tool
from memory import Memory
from memory.events import CustomEvent
from pydantic import BaseModel

from src.api.models.request_models import RuntimeResource
from src.applications.base import DefaultAgentContext
from src.core.memory.initialization import get_memory

from ...common.config import settings
from ...common.logging import logger
from ..utils.async_executor import (
    create_async_agentbay_wrapper,
    create_async_session_wrapper,
    async_executor
)


@function_tool
async def pc_operator(wrapper: RunContextWrapper[DefaultAgentContext], msg: str) -> Any:
    """
    执行PC操作任务 - 专门负责通过精确格式的指令协助用户完成在Windows系统上的简单办公任务

    核心能力与重要约束：
    - 仅支持单应用操作（一次只能操作一个应用程序）
    - 仅支持简单任务（在3步以内就可以完成的任务）
    - 输入任务描述必须严格遵循下面格式："操作{应用程序名称}：执行任务内容"
    - 不支持复杂的多应用协同操作

    支持的应用程序：
    1. **Word操作**: 创建、编辑Word文档
    2. **Excel操作**: 创建、编辑Excel工作表
    3. **文件系统操作**: 文件/文件夹的创建、复制、移动等基本操作
    4. **Notepad操作**: 文本文件的创建和编辑
    5. **Chrome操作**: 网页浏览和搜索

    格式示例：
    - Word: "操作word: 创建文档C:\\aipc_workspace\\report.docx并输入标题'工作报告'"
    - Excel: "操作excel: 创建工作表C:\\aipc_workspace\\data.xlsx在A1输入'销售数据'"
    - 文件系统: "操作文件系统: 创建文件夹C:\\aipc_workspace\\project"
    - Notepad: "操作notepad: 创建文本文件C:\\aipc_workspace\\note.txt输入'会议纪要'"
    - Chrome: "操作chrome: 打开网址www.baidu.com"

    重要说明：
    - **默认路径**: 所有在任务执行过程中涉及的文件保存、创建、移动等操作，如果用户指定了目标目录，你应该以用户指定的目录为准，如果没有明确指定路径，保存时你应该显式指定保存在C:\\aipc_workspace目录
    - 确保操作的一致性和安全性

    安全约束：
    - 仅执行简单、安全的办公操作
    - 不访问或修改系统关键文件
    - 保护用户隐私和数据安全

    Args:
        msg: 输入的任务描述，必须严格遵循下面格式："操作{应用程序名称}：执行任务内容"。
            支持的应用程序：word, excel, 文件系统, notepad, chrome

    Returns:
        操作结果字典，包含：
        - success: 是否成功
        - message: 原始消息
        - session_id: 会话ID
        - context_id: 上下文ID
        - result: 任务执行结果
        - error: 错误信息（如果失败）
    """
    try:
        # 从context中获取session_id和api_key
        context = wrapper.context

        # 从Dict中获取session_id和api_key
        if not context.session_context:
            raise ValueError("Session context not configured")

        session_id = context.session_context.get("session_id")
        trace_id: str = str(context.session_context.get("trace_id") or "")

        # 添加配置检查
        logger.info(f"context: {json.dumps(context.model_dump(), ensure_ascii=False)}")
        runtime_resource = context.session_context.get("runtime_resource")
        runtime_type = runtime_resource.get("type") or "none"
        if not runtime_resource or runtime_type not in ["agentbay", "desktop"]:
            return {
                "success": False,
                "error": "PC操作需要配置AgentBay或Desktop运行时资源，请设置runtime_resource.type为'agentbay'或'desktop'并提供有效的token",
                "message": msg,
            }

        # 验证runtime_resource配置
        try:
            resource_config = RuntimeResource.model_construct(**runtime_resource)
            api_key = resource_config.token
        except Exception as e:
            logger.error(f"Runtime resource配置无效: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": f"Runtime resource配置无效: {str(e)}",
                "message": msg,
            }

        if not session_id:
            raise ValueError("Session ID not configured")

        if not api_key:
            raise ValueError("AgentBay API key not configured")

        logger.info(f"开始执行PC操作: {msg}, session_id: {session_id}")

        # 获取当前agentbay 环境配置
        custom_cfg = Config(
            region_id=settings.pcagent.region_id,
            endpoint=settings.pcagent.endpoint,
            timeout_ms=settings.pcagent.timeout_ms,
        )
        # 创建agentbay实例
        logger.info(
            f"AgentBay配置: region_id={custom_cfg.region_id}, endpoint={custom_cfg.endpoint}, timeout_ms={custom_cfg.timeout_ms}"
        )
        agent_bay = AgentBay(api_key, custom_cfg)
        # 创建异步包装器
        async_agent_bay = create_async_agentbay_wrapper(agent_bay)

        # 优先从memory中获取session对象的agentbay_context_id和agentbay_session_id
        memory: Memory = get_memory()
        session_obj = memory.get_session(session_id=session_id)
        context_id = None
        agentbay_session_id = None
        if session_obj:
            context_id = getattr(session_obj, "agentbay_context_id", None)
            agentbay_session_id = getattr(session_obj, "agentbay_session_id", None)
            logger.info(
                f"从memory获取到: context_id={context_id}, agentbay_session_id={agentbay_session_id}"
            )

        # 初始化变量
        session = None
        path = None

        # 如果已经存在agentbay_session_id，尝试直接使用它创建session
        # 只有agentbay类型才可能会走进这个分支。desktop类型不会走到这里。
        if agentbay_session_id:
            logger.info(f"尝试使用已存在的agentbay_session_id: {agentbay_session_id}")
            try:
                session = Session(agent_bay, agentbay_session_id)
                async_session = create_async_session_wrapper(session)
                logger.info(f"成功使用已存在的agentbay_session_id创建session")

                # 检查session是否有效, 如果无效，SDK会抛出异常
                await async_session.get_info()

            except Exception as e:
                logger.warning(f"使用已存在的agentbay_session_id创建session异常: {e}，将重新创建")
                session = None
                async_session = None

        # 如果没有session，走重新创建流程
        if not session:
            # 根据runtime_resource类型决定是否需要处理context_id
            if runtime_type == "desktop":
                # 个人版资源：不需要context_id，直接创建session
                logger.info("检测到desktop类型，使用个人版资源创建逻辑")
                context_id = ""
                path = "C:\\aipc_workspace"

                # 个人版资源不需要指定image_id
                session_params = CreateSessionParams()
                context_sync = ContextSync.new(context_id, path)
                session_params.context_syncs = [context_sync]
                # 注意：个人版不设置image_id

            else:
                # AgentBay类型：需要处理context_id和image_id
                logger.info("检测到agentbay类型，使用标准AgentBay创建逻辑")

                # 如果memory中没有context_id，则用原有方式获取
                if not context_id:
                    context_result = await async_agent_bay.get_context(session_id, True)
                    if not context_result or not context_result.context:
                        raise RuntimeError("Failed to get context")
                    context_obj = context_result.context
                    context_id = context_obj.id
                    logger.info(f"获取到context_id: {context_id}")

                # 默认使用agentbay的path
                path = "C:\\aipc_workspace"

                logger.info(f"使用runtime_type: {runtime_type}, path: {path}")

                session_params = CreateSessionParams()
                context_sync = ContextSync.new(context_id, path)
                session_params.context_syncs = [context_sync]
                session_params.image_id = "windows_latest"  # 标准AgentBay需要指定image_id

            logger.info(f"使用runtime_type: {runtime_type}, path: {path}, context_id: {context_id}")

            result = await async_agent_bay.create_session(session_params)
            if not result.success or not result.session:
                raise RuntimeError("Failed to create session")

            session = result.session
            if not session:
                raise RuntimeError("Failed to create session")
            
            # 创建异步session包装器
            async_session = create_async_session_wrapper(session)

        # 只有agentbay类型才需要获取session信息并更新memory
        if runtime_type == "agentbay":
            # 获取session信息，用于后续更新memory
            session_info_result = await async_session.get_info()
            if not session_info_result.success or not session_info_result.data:
                raise RuntimeError("Failed to get session info")
            session_info = session_info_result.data

            # 将 SessionInfo 对象转换为可序列化的字典
            try:
                session_info_dict = {
                    "session_id": getattr(session_info, "session_id", None),
                    "resource_url": getattr(session_info, "resource_url", None),
                    "app_id": getattr(session_info, "app_id", None),
                    "auth_code": getattr(session_info, "auth_code", None),
                    "connection_properties": getattr(session_info, "connection_properties", None),
                    "resource_id": getattr(session_info, "resource_id", None),
                    "resource_type": getattr(session_info, "resource_type", None),
                    "ticket": getattr(session_info, "ticket", None),
                }
                logger.info(f"session_info: {json.dumps(session_info_dict, ensure_ascii=False)}")
            except Exception as e:
                logger.warning(f"转换session_info为字典失败: {e}")
                raise RuntimeError("Failed to convert session_info to dict")

            # 创建自定义事件，使用可序列化的字典
            custom_event = CustomEvent(
                session_id=session_id,
                run_id=trace_id,
                name="pc_operator_info",
                content=json.dumps(session_info_dict, ensure_ascii=False),
            )
            memory.add_event(custom_event, session_id)

            memory.update_session(
                session_id=session_id,
                agentbay_session_id=session_info.session_id,
                agentbay_context_id=context_id,
                agentbay_context_dir=path
            )

            logger.info(f"成功更新Session信息: {json.dumps(session_info_dict, ensure_ascii=False)}")

        # 执行任务
        timeout_s = settings.pcagent.agent_task_timeout
        task_result = await async_session.execute_task(msg, timeout_s)

        # 打印 ExecutionResult的详细内容
        try:
            task_result_details = {
                "request_id": getattr(task_result, "request_id", None),
                "success": getattr(task_result, "success", None),
                "error_message": getattr(task_result, "error_message", None),
                "task_id": getattr(task_result, "task_id", None),
                "task_status": getattr(task_result, "task_status", None),
            }
            logger.info(
                f"PC操作: {msg}, 执行结果详情: {json.dumps(task_result_details, ensure_ascii=False, indent=2)}"
            )

            # 只有agentbay类型才需要上传制品
            if runtime_type == "agentbay":
                # 上传制品
                result = await async_session.sync_context()
                if result.success:
                    logger.info(f"PC操作执行制品上传成功")
                else:
                    logger.error(f"PC操作执行制品上传失败: {result.request_id}")

            if task_result.success:
                return {
                    "success": True,
                    "message": msg,
                    "session_id": session_id,
                    "context_id": context_id,
                    "result": task_result,
                }

            else:
                return {
                    "success": False,
                    "error": task_result.error_message,
                    "session_id": session_id,
                    "message": msg,
                }
        except Exception as e:
            logger.warning(f"PC操作: {msg}, 无法获取执行结果详情: {e}")
            return {"success": False, "error": str(e), "message": msg}

    except Exception as e:
        logger.error(f"PC操作执行失败: {str(e)}", exc_info=True)
        return {"success": False, "error": str(e), "message": msg}
