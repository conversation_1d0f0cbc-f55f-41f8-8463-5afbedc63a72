#!/usr/bin/env python3
"""
代码结构化分析工具
支持本地代码目录分析，产出格式化输出
"""

import fnmatch
import os
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Set, Tuple


@dataclass
class FileNode:
    """文件节点数据结构"""

    name: str
    path: Path
    is_file: bool
    size: int = 0
    content: str = ""
    children: List["FileNode"] = None

    def __post_init__(self):
        if self.children is None:
            self.children = []


@dataclass
class AnalysisConfig:
    """分析配置"""

    max_file_size: int = 10 * 1024 * 1024  # 10MB
    max_files: int = 1000
    include_patterns: Set[str] = None
    exclude_patterns: Set[str] = None

    def __post_init__(self):
        if self.include_patterns is None:
            self.include_patterns = set()
        if self.exclude_patterns is None:
            # 默认排除模式
            self.exclude_patterns = {
                "*.pyc",
                "__pycache__",
                ".git",
                ".svn",
                "node_modules",
                ".DS_Store",
                "*.log",
                ".idea",
                ".vscode",
                ".gitignore",
                ".gitmodules",
                ".gitattributes",
                ".cursor",
            }


class CodeAnalyzer:
    """代码分析器主类"""

    def __init__(self, config: AnalysisConfig = None):
        self.config = config or AnalysisConfig()
        self.stats = {"total_files": 0, "total_size": 0, "processed_files": 0}

    def should_include_file(self, file_path: Path) -> bool:
        """判断文件是否应该被包含"""
        file_name = file_path.name
        relative_path = str(file_path)

        # 检查排除模式
        for pattern in self.config.exclude_patterns:
            if fnmatch.fnmatch(file_name, pattern) or fnmatch.fnmatch(relative_path, pattern):
                return False

        # 如果有包含模式，检查是否匹配
        if self.config.include_patterns:
            for pattern in self.config.include_patterns:
                if fnmatch.fnmatch(file_name, pattern) or fnmatch.fnmatch(relative_path, pattern):
                    return True
            return False

        return True

    def read_file_content(self, file_path: Path) -> str:
        """读取文件内容"""
        try:
            # 检查文件大小
            if file_path.stat().st_size > self.config.max_file_size:
                return f"# 文件过大 (>{self.config.max_file_size} bytes)，跳过内容读取"

            # 尝试以文本方式读取
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                content = f.read()
                return content
        except Exception as e:
            return f"# 读取文件失败: {str(e)}"

    def process_directory(self, dir_path: Path, root_path: Path) -> FileNode:
        """处理目录，目录遍历逻辑"""
        node = FileNode(name=dir_path.name or dir_path.stem, path=dir_path, is_file=False)

        try:
            for item in sorted(dir_path.iterdir()):
                # 检查文件数量限制
                if self.stats["total_files"] >= self.config.max_files:
                    break

                if not self.should_include_file(item):
                    continue

                if item.is_file():
                    file_size = item.stat().st_size
                    content = self.read_file_content(item)

                    file_node = FileNode(
                        name=item.name, path=item, is_file=True, size=file_size, content=content
                    )

                    node.children.append(file_node)
                    node.size += file_size
                    self.stats["total_files"] += 1
                    self.stats["total_size"] += file_size
                    self.stats["processed_files"] += 1

                elif item.is_dir():
                    subdir_node = self.process_directory(item, root_path)
                    if subdir_node.children:  # 只添加非空目录
                        node.children.append(subdir_node)
                        node.size += subdir_node.size

        except PermissionError:
            pass  # 跳过无权限访问的目录

        return node

    def generate_tree_structure(
        self, node: FileNode, prefix: str = "", is_last: bool = True
    ) -> str:
        """生成目录树结构"""
        tree_str = ""
        current_prefix = "└── " if is_last else "├── "

        display_name = node.name
        if not node.is_file:
            display_name += "/"

        tree_str += f"{prefix}{current_prefix}{display_name}\n"

        if not node.is_file and node.children:
            next_prefix = prefix + ("    " if is_last else "│   ")
            for i, child in enumerate(node.children):
                is_child_last = i == len(node.children) - 1
                tree_str += self.generate_tree_structure(child, next_prefix, is_child_last)

        return tree_str

    def generate_file_contents(self, node: FileNode, root_path: Path) -> str:
        """生成文件内容部分"""
        content_str = ""

        if node.is_file and node.content:
            relative_path = node.path.relative_to(root_path)
            content_str += f"\n{'='*50}\n"
            content_str += f"文件: {relative_path}\n"
            content_str += f"大小: {node.size} bytes\n"
            content_str += f"{'='*50}\n\n"
            content_str += node.content + "\n"

        # 递归处理子节点
        for child in node.children:
            content_str += self.generate_file_contents(child, root_path)

        return content_str

    def analyze(self, source_path: str) -> Tuple[str, str, str]:
        """主分析函数"""
        path = Path(source_path).resolve()

        if not path.exists():
            raise ValueError(f"路径不存在: {path}")

        # 重置统计信息
        self.stats = {"total_files": 0, "total_size": 0, "processed_files": 0}

        if path.is_file():
            # 单文件处理
            content = self.read_file_content(path)
            file_node = FileNode(
                name=path.name, path=path, is_file=True, size=path.stat().st_size, content=content
            )
            root_node = file_node
            self.stats["total_files"] = 1
            self.stats["processed_files"] = 1
            self.stats["total_size"] = path.stat().st_size
        else:
            # 目录处理
            root_node = self.process_directory(path, path)

        # 生成输出
        summary = self.generate_summary(path)
        tree = self.generate_tree_structure(root_node)
        content = self.generate_file_contents(root_node, path)

        return summary, tree, content

    def generate_summary(self, path: Path) -> str:
        """生成分析摘要"""
        summary = f"代码分析报告\n"
        summary += f"{'='*30}\n"
        summary += f"分析路径: {path}\n"
        summary += f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        summary += f"处理文件数: {self.stats['processed_files']}\n"
        summary += f"总文件数: {self.stats['total_files']}\n"
        summary += f"总大小: {self.stats['total_size']:,} bytes\n"
        return summary


def main():
    """命令行入口函数"""
    import argparse

    parser = argparse.ArgumentParser(description="代码分析工具")
    parser.add_argument("source", help="要分析的源代码路径")
    parser.add_argument("--output", "-o", help="输出文件路径")
    parser.add_argument(
        "--max-size", type=int, default=10 * 1024 * 1024, help="最大文件大小(bytes)"
    )
    parser.add_argument("--max-files", type=int, default=1000, help="最大文件数量")
    parser.add_argument("--include", action="append", help="包含模式")
    parser.add_argument("--exclude", action="append", help="排除模式")

    args = parser.parse_args()

    # 创建配置
    config = AnalysisConfig(
        max_file_size=args.max_size,
        max_files=args.max_files,
        include_patterns=set(args.include) if args.include else set(),
        exclude_patterns=set(args.exclude) if args.exclude else None,
    )

    # 执行分析
    analyzer = CodeAnalyzer(config)
    try:
        summary, tree, content = analyzer.analyze(args.source)

        # 输出结果
        full_output = f"{summary}\n\n目录结构:\n{tree}\n\n文件内容:{content}"

        if args.output:
            with open(args.output, "w", encoding="utf-8") as f:
                f.write(full_output)
            print(f"分析完成！结果已保存到: {args.output}")
        else:
            print(full_output)

    except Exception as e:
        print(f"分析失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
