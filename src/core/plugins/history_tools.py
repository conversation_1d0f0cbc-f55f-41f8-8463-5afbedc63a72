"""
历史记录工具
提供获取任务历史记录的功能
"""

from typing import Any, Dict, List

from agents import function_tool

from ...common.logging import logger
from ..memory import HistoryManager

# 创建历史记录管理器实例
history_manager = HistoryManager()


@function_tool
async def get_task_history(limit: int = 5) -> str:
    """
    获取任务执行历史记录

    Args:
        limit: 要返回的历史记录数量

    Returns:
        格式化的历史记录字符串
    """
    logger.info(f"获取历史记录，限制数量: {limit}")
    return history_manager.get_formatted_history(limit)


@function_tool
async def clear_task_history() -> str:
    """
    清除所有任务历史记录

    Returns:
        操作结果消息
    """
    logger.info("清除历史记录")
    history_manager.clear_history()
    return "历史记录已清除"


@function_tool
async def get_recent_tasks(limit: int = 10) -> List[Dict[str, Any]]:
    """
    获取最近的任务记录

    Args:
        limit: 要返回的记录数量

    Returns:
        最近的任务记录列表
    """
    logger.info(f"获取最近任务，限制数量: {limit}")
    return history_manager.get_recent_tasks(limit)
