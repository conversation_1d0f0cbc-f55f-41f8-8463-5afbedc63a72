"""
网络搜索插件
提供基于阿里云IQS、Google、Tavily的网络搜索能力
"""

import asyncio
from typing import Any, Dict, List

import aiohttp
from agents import function_tool
from alibabacloud_iqs20241111 import models
from alibabacloud_iqs20241111.client import Client
from alibabacloud_tea_openapi import models as open_api_models
from Tea.exceptions import TeaException

from ...common.config import settings
from ...common.logging import logger

# 尝试导入Tavily，如果未安装则记录警告
try:
    from tavily import AsyncTavilyClient

    TAVILY_AVAILABLE = True
except ImportError:
    TAVILY_AVAILABLE = False
    logger.warning("Tavily客户端未安装，请运行 'pip install tavily-python' 来启用Tavily搜索功能")


# 返回结果数量 (默认: 10, 阿里云IQS总是返回10个结果)
@function_tool
async def aliyun_search(
    query: str, time_range: str = "NoLimit", category: str = None
) -> List[Dict[str, Any]]:
    """
    使用阿里云IQS搜索引擎执行搜索

    Args:
        query: 搜索查询字符串
        time_range: 搜索结果的时间范围 (默认: "NoLimit", 可选值: "OneDay/OneWeek/OneMonth/OneYear/NoLimit")
        category: 指定搜索的行业，指定后只返回行业站点的搜索结果，多个行业使用逗号分隔，默认不传。可选值（"finance/law/medical/internet/tax/news_province/news_center"）
            finance：金融
            law：法律
            medical：医疗
            internet：互联网（精选）
            tax：税务
            news_province：新闻省级
            news_center：新闻中央

    Returns:
        搜索结果列表，每个结果包含标题和链接
    """
    # 创建阿里云IQS客户端
    iqs_config = open_api_models.Config(
        access_key_id=settings.search.iqs_key_id,
        access_key_secret=settings.search.iqs_key_secret,
    )
    iqs_config.endpoint = "iqs.cn-zhangjiakou.aliyuncs.com"
    client = Client(iqs_config)

    # 创建并执行搜索请求
    run_instances_request = models.UnifiedSearchRequest(
        body=models.UnifiedSearchInput(
            query=query,
            time_range=time_range,
            category=category,
            engine_type="Generic",
            contents=models.RequestContents(
                summary=True,
                main_text=True,
            ),
        )
    )

    try:
        response = await client.unified_search_async(run_instances_request)
        logger.info(f"执行阿里云IQS搜索: {query}")

        # 处理和格式化搜索结果
        results = []
        if hasattr(response.body, "page_items") and response.body.page_items:
            for item in response.body.page_items:
                result_item = {
                    "title": getattr(item, "title", ""),
                    "url": getattr(item, "link", ""),
                    "summary": getattr(item, "summary", ""),
                    "main_text": getattr(item, "main_text", ""),
                }
                results.append(result_item)

            logger.info(f"搜索结果: 找到 {len(results)} 条结果")
            return results[:3]  # 限制结果数量
        else:
            logger.warning(f"搜索 '{query}' 未返回结果")
            return []

    except TeaException as e:
        code = e.code
        request_id = e.data.get("requestId") if hasattr(e, "data") else None
        message = e.data.get("message") if hasattr(e, "data") else str(e)
        logger.error(f"阿里云IQS API异常, requestId:{request_id}, code:{code}, message:{message}")
        return []

    except Exception as e:
        logger.error(f"IQS搜索意外错误: {str(e)}", exc_info=True)
        return []


@function_tool
async def google_search(query, num=10) -> List[Dict[str, Any]]:
    """
    使用Google进行搜索

    参数:
    query (str): 搜索关键词
    num (int): 返回结果数量，默认10条

    返回:
    list: 包含标题和URL的字典列表
    """
    base_url = "https://www.googleapis.com/customsearch/v1"

    params = {
        "q": query,
        "key": settings.search.gg_key_secret,
        "cx": settings.search.gg_key_id,
        "num": num,
    }

    try:
        # 使用aiohttp进行异步HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.get(base_url, params=params) as response:
                if response.status != 200:
                    logger.error(f"Google搜索错误: {response.status}")
                    error_text = await response.text()
                    logger.error(error_text)
                    return []

                search_results = await response.json()

                # 提取标题和URL
                title_url_array = []
                if "items" in search_results:
                    for item in search_results["items"]:
                        title_url_array.append({"title": item["title"], "url": item["link"]})

                return title_url_array
                
    except Exception as e:
        logger.error(f"Google搜索异常: {str(e)}", exc_info=True)
        return []


@function_tool
async def web_search(
    query: str,
    topic: str = "general",
    search_depth: str = "basic",
    max_results: int = 10,
    time_range: str = "none",
    search_images: bool = False,
    include_raw_content: str = "none",
) -> List[Dict[str, Any]]:
    """
    使用Tavily搜索引擎执行互联网搜索，可以进行文本或图片搜索

    Args:
        query: 搜索查询字符串
        topic: 搜索主题，默认"general"，如果为"general"，则不使用主题搜索，如果为"news"，则使用新闻主题搜索，如果为"finance"，则使用财经主题搜索
        search_depth: 搜索深度 ("basic" 或 "advanced")，默认为"basic"
        max_results: 最大返回结果数量，默认10条
        time_range: 搜索时间范围 ("none","day", "week", "month", "year")，默认为"none"
        search_images: 是否包含图片，默认False，如果为true，则本次搜索只会返回搜索到的图片列表，每个图片包含图片的url以及图片的描述
        include_raw_content: 是否包含原始内容，可以是布尔值或字符串格式("none","markdown", "text")，默认"none"

    Returns:
        搜索结果列表，每个结果包含标题、URL、摘要等信息
    """
    if not TAVILY_AVAILABLE:
        logger.error("Tavily客户端未安装，无法执行搜索")
        return []

    if not settings.search.tavily_api_key:
        logger.error("Tavily API密钥未配置，无法执行搜索")
        return []

    try:
        # 创建Tavily客户端
        client = AsyncTavilyClient(api_key=settings.search.tavily_api_key)

        logger.info(f"执行Tavily搜索: {query}")

        # 构建搜索参数
        search_params = {
            "query": query,
            "search_depth": search_depth,
            "max_results": max_results,
            "include_images": search_images,
            "include_image_descriptions": search_images,
        }

        if topic in ["news", "finance"]:
            search_params["topic"] = topic

        # 处理时间范围参数
        if time_range in ["day", "week", "month", "year"]:
            search_params["time_range"] = time_range

        # 处理原始内容参数
        if include_raw_content in ["markdown", "text"]:
            search_params["include_raw_content"] = include_raw_content

        # 执行搜索
        response = await client.search(**search_params)

        # 处理和格式化搜索结果
        results = []
        if response and search_images:
            results = response.get("images", [])
            logger.info(f"Tavily搜索结果: 找到 {len(results)} 条图片结果")
        elif response and "results" in response:
            for item in response["results"]:
                result_item = {
                    "title": item.get("title", ""),
                    "url": item.get("url", ""),
                    "summary": item.get("content", ""),
                    "score": item.get("score", 0),
                }

                if (
                    include_raw_content
                    and "raw_content" in item
                    and item["raw_content"] is not None
                ):
                    result_item["raw_content"] = item["raw_content"]

                results.append(result_item)

            logger.info(f"Tavily搜索结果: 找到 {len(results)} 条结果")
        else:
            logger.warning(f"Tavily搜索 '{query}' 未返回结果")

        return results

    except Exception as e:
        logger.error(f"Tavily搜索意外错误: {str(e)}", exc_info=True)
        return []
