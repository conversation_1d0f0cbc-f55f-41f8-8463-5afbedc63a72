"""
资源处理器 - 封装所有资源处理逻辑
统一处理知识库检索和文件内容处理
"""

import asyncio
import time
from typing import Any, Dict, List, Optional, Tuple

from src.api.models.resource_models import (
    FileParseResult,
    FileResource,
    FileResourceInfo,
    KnowledgeBaseInfo,
    KnowledgeBaseResource,
    KnowledgeBaseResult,
    ResourceInfo,
    ResourceRequest,
)
from src.common.config.settings import ResourceSettings
from src.common.logging import logger
from src.services.rag_service import RAGService

from .exceptions import InvalidResourceTypeError, ResourceError


class ResourceProcessor:
    """资源处理器 - 封装所有资源处理逻辑"""

    def __init__(self, settings: Optional[ResourceSettings] = None):
        if settings is None:
            from src.common.config import settings as global_settings

            self.settings = global_settings.resource
        else:
            self.settings = settings

        self.rag_service = RAGService(self.settings)

    async def process_resources(
        self, user_message: str, resources: List[ResourceRequest]
    ) -> <PERSON><PERSON>[str, ResourceInfo]:
        """
        处理资源请求，返回增强后的消息和资源信息

        Args:
            user_message: 用户的问题消息
            resources: 资源列表

        Returns:
            tuple: (增强后的消息, 资源信息)
        """
        if not resources:
            return "", ResourceInfo()

        logger.info(f"开始处理 {len(resources)} 个资源，用户问题: {user_message}")

        try:
            # 1. 验证和分组资源
            kb_resources, file_resources = self._validate_and_group_resources(resources)

            # 2. 处理知识库检索 - 使用用户消息作为查询
            rag_results = await self._process_knowledge_bases(user_message, kb_resources)

            # 3. 直接提取文件内容（无需服务调用）
            file_results = self._extract_file_contents(file_resources)

            # 4. 构建增强消息
            enhanced_message = self._build_enhanced_message(rag_results, file_results)

            # 5. 构建资源信息
            resource_info = self._build_resource_info(rag_results, file_results)

            logger.info(
                f"资源处理完成，知识库结果: {len(rag_results)}, 文件结果: {len(file_results)}"
            )

            return enhanced_message, resource_info

        except Exception as e:
            logger.error(f"资源处理失败: {e}", exc_info=True)
            # 优雅降级：返回空内容
            return "", ResourceInfo()

    def _validate_and_group_resources(
        self, resources: List[ResourceRequest]
    ) -> Tuple[List[KnowledgeBaseResource], List[FileResource]]:
        """验证和分组资源"""
        kb_resources = []
        file_resources = []

        for resource in resources:
            try:
                if resource.type == "knowledge_base":
                    if isinstance(resource, KnowledgeBaseResource):
                        kb_resources.append(resource)
                        logger.debug(f"添加知识库资源: {resource.kb_id}")
                    else:
                        logger.warning(f"知识库资源格式不正确: {resource}")

                elif resource.type == "file":
                    if isinstance(resource, FileResource):
                        file_resources.append(resource)
                        logger.debug(f"添加文件资源: {resource.file_name or 'Unknown'}")
                    else:
                        logger.warning(f"文件资源格式不正确: {resource}")

                else:
                    logger.warning(f"不支持的资源类型: {resource.type}")
                    continue

            except Exception as e:
                logger.warning(f"资源验证失败: {resource}, 错误: {e}")
                continue

        logger.info(f"资源分组完成: 知识库 {len(kb_resources)} 个, 文件 {len(file_resources)} 个")
        return kb_resources, file_resources

    async def _process_knowledge_bases(
        self, user_message: str, kb_resources: List[KnowledgeBaseResource]
    ) -> List[KnowledgeBaseResult]:
        """处理知识库资源"""
        if not kb_resources:
            return []

        # 按查询参数分组，相同参数的知识库可以批量处理
        query_groups = {}
        for kb_resource in kb_resources:
            # 创建分组的key（只基于参数，不再基于query）
            group_key = (str(sorted(kb_resource.query_parameters.items())), kb_resource.top_k)
            if group_key not in query_groups:
                query_groups[group_key] = {
                    "kb_ids": [],
                    "query_parameters": kb_resource.query_parameters,
                    "top_k": kb_resource.top_k,
                }
            query_groups[group_key]["kb_ids"].append(kb_resource.kb_id)

        results = []

        # 批量处理每个参数组
        for group_info in query_groups.values():
            logger.info(f"批量检索知识库: {group_info['kb_ids']}, 查询: {user_message}")

            start_time = time.time()
            try:
                # 批量调用RAG服务
                result = await self.rag_service.search_knowledge_bases(
                    query=user_message,  # 使用用户消息作为查询
                    knowledge_base_ids=group_info["kb_ids"],
                    query_parameters=group_info["query_parameters"],
                )

                search_time = time.time() - start_time
                result.search_time = search_time
                results.append(result)

                logger.info(
                    f"批量知识库检索完成，用时 {search_time:.2f}s，结果数: {result.total_results}"
                )

            except Exception as e:
                search_time = time.time() - start_time
                logger.error(f"批量知识库检索失败，用时 {search_time:.2f}s，错误: {e}")

                # 创建错误结果
                error_result = KnowledgeBaseResult(
                    success=False,
                    kb_ids=group_info["kb_ids"],
                    query=user_message,
                    docs=[],
                    search_time=search_time,
                    error_message=str(e),
                )
                results.append(error_result)

        return results

    def _extract_file_contents(self, file_resources: List[FileResource]) -> List[FileParseResult]:
        """直接提取文件内容，无需解析"""
        if not file_resources:
            return []

        results = []

        for resource in file_resources:
            try:
                # 内容已经在验证时检查过长度
                result = FileParseResult(
                    success=True,
                    content=resource.content,
                    file_name=resource.file_name or "Unknown",
                    file_type=resource.file_type,
                    file_size=resource.file_size,
                    address=resource.address,
                    upload_time=resource.upload_time,
                )
                results.append(result)
                logger.debug(f"提取文件内容成功: {resource.file_name}")

            except Exception as e:
                logger.error(f"提取文件内容失败: {resource.file_name}, 错误: {e}")
                error_result = FileParseResult(
                    success=False, error_message=str(e), file_name=resource.file_name or "Unknown"
                )
                results.append(error_result)

        logger.info(f"文件内容提取完成: {len(results)} 个文件")
        return results

    def _build_enhanced_message(
        self, rag_results: List[KnowledgeBaseResult], file_results: List[FileParseResult]
    ) -> str:
        """构建增强消息"""
        parts = []

        total_content_length = 0
        max_length = getattr(self.settings, "max_file_content_size", 9_437_184)  # 默认9MB
        max_single_length = max_length // 10  # 单个内容最大长度

        # 添加知识库内容
        successful_rag = [r for r in rag_results if r.success]
        if successful_rag:
            parts.append("=== 相关知识库信息 ===")

            for i, result in enumerate(successful_rag):
                if total_content_length >= max_length:
                    parts.append(
                        f"... (还有 {len(successful_rag) - i} 个知识库结果因长度限制未显示)"
                    )
                    break

                content = result.content
                # 截断单个内容
                if len(content) > max_single_length:
                    content = content[:max_single_length] + "..."

                kb_names = ", ".join(result.kb_ids)
                parts.append(f"知识库检索结果: {content}")
                parts.append("")  # 空行分隔

                total_content_length += len(content)

        # 添加文件内容
        successful_files = [f for f in file_results if f.success]
        if successful_files:
            parts.append("=== 相关文件信息 ===")

            for i, file_result in enumerate(successful_files):
                if total_content_length >= max_length:
                    parts.append(f"... (还有 {len(successful_files) - i} 个文件因长度限制未显示)")
                    break

                content = file_result.content
                # 截断单个内容
                if len(content) > max_single_length:
                    content = content[:max_single_length] + "..."

                file_info = f"文件: {file_result.file_name or 'Unknown'}"
                if file_result.file_type:
                    file_info += f" (类型: {file_result.file_type})"

                parts.append(f"[文件 {i+1}] {file_info}")
                parts.append(f"内容: {content}")
                parts.append("")  # 空行分隔

                total_content_length += len(content)

        # 添加错误提示
        error_rags = [r for r in rag_results if not r.success]
        error_files = [f for f in file_results if not f.success]

        if error_rags or error_files:
            parts.append("=== 处理异常 ===")

            if error_rags:
                error_kbs = []
                for r in error_rags:
                    error_kbs.extend(r.kb_ids)
                parts.append(f"⚠️ 以下知识库检索失败: {', '.join(error_kbs)}")

            if error_files:
                error_filenames = [f.file_name or "Unknown" for f in error_files]
                parts.append(f"⚠️ 以下文件处理失败: {', '.join(error_filenames)}")

        enhanced_message = "\n".join(parts)

        # 记录增强消息的统计信息
        logger.info(f"消息增强完成，总长度: {len(enhanced_message)} 字符")

        return enhanced_message

    def _build_resource_info(
        self, rag_results: List[KnowledgeBaseResult], file_results: List[FileParseResult]
    ) -> ResourceInfo:
        """构建资源信息"""

        # 构建知识库信息
        kb_info_list = []
        for result in rag_results:
            kb_info = KnowledgeBaseInfo(
                kb_ids=result.kb_ids,
                query=result.query,
                success=result.success,
                result_count=result.total_results,
                search_time=result.search_time,
                error_message=result.error_message,
            )
            kb_info_list.append(kb_info)

        # 构建文件信息
        file_info_list = []
        for result in file_results:
            file_info = FileResourceInfo(
                filename=result.file_name,
                file_type=result.file_type,
                file_size=result.file_size,
                address=result.address,
                upload_time=result.upload_time,
                success=result.success,
                error_message=result.error_message,
            )
            file_info_list.append(file_info)

        return ResourceInfo(knowledge_bases=kb_info_list, files=file_info_list)

    async def cleanup(self):
        """清理资源"""
        logger.info("清理ResourceProcessor资源")
        try:
            await self.rag_service.close()
            logger.info("ResourceProcessor资源清理完成")
        except Exception as e:
            logger.error(f"ResourceProcessor资源清理失败: {e}")

    def __del__(self):
        """析构函数 - 确保资源清理"""
        # 注意：在析构函数中不能使用async方法
        # 这里只是记录，实际清理需要显式调用cleanup()
        pass
