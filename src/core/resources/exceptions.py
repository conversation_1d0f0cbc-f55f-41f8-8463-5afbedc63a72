"""
资源处理异常类
定义资源处理过程中可能出现的异常
"""


class ResourceError(Exception):
    """资源处理基础异常"""

    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(message)


class ResourceNotFoundError(ResourceError):
    """资源未找到异常"""

    def __init__(self, resource_id: str):
        message = f"资源未找到: {resource_id}"
        super().__init__(message, "RESOURCE_NOT_FOUND")
        self.resource_id = resource_id


class ResourceProcessingError(ResourceError):
    """资源处理失败异常"""

    def __init__(self, resource_id: str, reason: str):
        message = f"资源处理失败: {resource_id}, 原因: {reason}"
        super().__init__(message, "RESOURCE_PROCESSING_FAILED")
        self.resource_id = resource_id
        self.reason = reason


class ResourceTimeoutError(ResourceError):
    """资源处理超时异常"""

    def __init__(self, resource_id: str, timeout: int):
        message = f"资源处理超时: {resource_id}, 超时时间: {timeout}秒"
        super().__init__(message, "REQUEST_TIMEOUT")
        self.resource_id = resource_id
        self.timeout = timeout


class InvalidResourceTypeError(ResourceError):
    """无效资源类型异常"""

    def __init__(self, resource_type: str):
        message = f"无效的资源类型: {resource_type}"
        super().__init__(message, "INVALID_RESOURCE_TYPE")
        self.resource_type = resource_type
