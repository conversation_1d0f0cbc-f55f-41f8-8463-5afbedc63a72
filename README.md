# WAIY智能体框架

[![Python Version](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-Proprietary-red.svg)](LICENSE)

WAIY智能体框架是一个基于多智能体协同的AI开发框架，专为快速构建复杂AI应用而设计。框架支持单智能体和多智能体场景，具备强大的工具集成能力和灵活的扩展机制。

## ✨ 核心特性

- **🤖 多智能体协同** - 支持单智能体和多智能体handoff机制
- **🔌 MCP协议集成** - 统一的工具和能力接入标准
- **🎯 多模型支持** - 兼容OpenAI、百炼、Deepseek等多种LLM
- **📁 资源对话增强** - 支持知识库检索和文件内容对话，智能增强用户问答
- **🚀 即开即用** - 内置5个生产级应用，开箱即用
- **🌐 多端支持** - 命令行、REST API、Web界面三种使用方式
- **📊 全链路追踪** - 完整的执行链路监控和日志系统
- **🔒 安全可控** - 内置护栏系统和安全检查机制
- **⚡ 高性能** - 异步架构，支持并发处理

## 🏗️ 架构概览

```
waiy_infra/
├── src/
│   ├── applications/        # 智能体应用层
│   │   ├── base.py          # 应用基类（支持资源增强）
│   │   ├── manus/           # PC自动化助手
│   │   ├── customer_service/# 智能客服系统
│   │   ├── deep_research/   # 深度研究工具
│   │   ├── financial_research/ # 金融研究分析
│   │   ├── deeper_research/ # 超级深度研究
│   │   └── chat_namer/      # 对话命名服务
│   ├── core/                # 核心业务层
│   │   ├── agent/           # 智能体基础类和运行器
│   │   ├── guardrails/      # 护栏系统（输入/输出安全检测）
│   │   ├── mcp/             # MCP服务器管理
│   │   ├── memory/          # 记忆和历史管理
│   │   ├── resources/       # 资源处理核心（知识库+文件）
│   │   └── plugins/         # 插件系统（搜索、代码分析等）
│   ├── services/            # 外部服务层
│   │   └── rag_service.py   # 阿里云RAG服务客户端
│   ├── common/              # 通用服务层
│   │   ├── config/          # 配置管理（含资源配置）
│   │   ├── logging/         # 日志系统
│   │   ├── safety/          # 安全检查
│   │   └── models.py        # 通用数据模型
│   ├── api/                 # API服务层
│   │   ├── apps.py          # 应用路由（支持resources参数）
│   │   └── models/          # API数据模型
│   │       ├── request_models.py   # 请求模型
│   │       ├── response_models.py  # 响应模型
│   │       └── resource_models.py  # 资源模型
│   └── web_ui/              # Web界面层
├── main.py                  # 命令行入口
├── api.py                   # API服务入口（支持资源对话）
└── web.py                   # Web界面入口
```

## 🚀 快速开始

### 环境准备

**系统要求**
- 推荐使用Python 3.12

**安装步骤**

#### 方式一：使用uv管理（推荐）

uv是现代化的Python项目管理工具，提供更快的依赖解析和安装速度。

1. **安装uv**
```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 或使用pip安装
pip install uv
```

2. **克隆并初始化项目**
```bash
git clone <repository-url>
cd waiy_infra

# 使用uv同步依赖和创建虚拟环境
uv sync
```

3. **激活虚拟环境**
```bash
# 自动激活并运行命令
uv run python main.py

# 或手动激活虚拟环境
source .venv/bin/activate  # Windows: .venv\Scripts\activate
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要的API密钥
```

#### 方式二：传统pip方式

1. **克隆项目**
```bash
git clone <repository-url>
cd waiy_infra
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
```

3. **安装依赖**
```bash
pip install -e . -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com --extra-index-url http://yum.tbsite.net/aliyun-pypi/simple/ --trusted-host yum.tbsite.net
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要的API密钥
```

**最小配置示例**
```bash
# .env 文件
LLM_MAIN_MODEL_PROVIDER=openai
LLM_MAIN_MODEL=gpt-4o-mini
LLM_OPENAI_API_KEY=your_openai_api_key

# 可选：搜索功能
SEARCH_TAVILY_API_KEY=your_tavily_api_key
```

### 启动方式

#### 🖥️ 命令行模式（推荐用于开发调试）

```bash
# 使用uv运行（推荐）
uv run python main.py

# 或传统方式（需要先激活虚拟环境）
python main.py

# 查看所有可用应用
uv run python main.py --list
```

#### 🌐 API服务模式（推荐用于生产部署）

```bash
# 使用uv启动API服务（推荐）
uv run python api.py

# 或传统方式（需要先激活虚拟环境）
python api.py

# 服务启动后访问：
# - API文档: http://localhost:8000/docs
# - Web界面: http://localhost:8000/web-ui
```

#### 🎨 Web界面模式（推荐用于交互使用）

```bash
# 使用uv运行（推荐）
uv run python web.py --local   # 本地调试
uv run python web.py --pre     # 连接预发环境
uv run python web.py --prod    # 连接生产环境

# 或传统方式（需要先激活虚拟环境）
python web.py --local
python web.py --pre
python web.py --prod
```

## 📱 内置应用

| 应用 | 描述 | 特性 | 使用场景 |
|------|------|------|----------|
| **Manus** | PC自动化助手 | 文件管理、终端操作、浏览器自动化 | 桌面自动化任务 |
| **CustomerService** | 智能客服系统 | 多智能体协同、分诊转接机制 | 客户服务场景 |
| **DeepResearch** | 深度研究工具 | 多源搜索、内容分析 | 研究报告生成 |
| **FinancialResearch** | 金融研究分析 | 财务数据分析、风险评估 | 金融投资分析 |
| **DeeperResearch** | 超级深度研究 | 多智能体联动、长文档生成 | 复杂研究任务 |

## 🔧 API接口

### 应用管理
```bash
# 获取所有应用
GET /apps/

# 获取应用信息
GET /apps/{app_id}/info

# 发送消息（同步）
POST /apps/{app_id}/message
{
  "message": "你的问题",
  "context": {
    "session_id": "optional",
    "runtime_resource": {
      "type": "agentbay",
      "token": "your_api_token",
      "region": "cn-shanghai",
      "cloud_resource_id": "ecd-1234567890"
    }
  },
  "resources": [
    {
      "type": "knowledge_base",
      "kb_id": "technical_docs",
      "query_parameters": {"doc_memory": {}},
      "top_k": 5
    },
    {
      "type": "file",
      "content": "文件解析后的内容",
      "filename": "文档.pdf",
      "file_type": "pdf",
      "file_size": 1024,
      "address": "https://example.com/file.pdf"
    }
  ]
}

# 发送消息（异步）
POST /apps/{app_id}/message/async
```

### 系统监控
```bash
# 健康检查
GET /status.taobao

# 服务信息
GET /
```

**完整API文档**: 启动服务后访问 http://localhost:8000/docs

## 📁 资源对话功能

WAIY框架支持**资源增强对话**，可以在对话中集成知识库检索和文件内容，让AI助手基于特定资源回答问题。

### 功能特性

- **📚 知识库检索** - 支持多个知识库的语义搜索
- **📄 文件内容对话** - 支持直接传入解析后的文件内容
- **🔄 智能增强** - 自动将资源内容与用户问题组合
- **⚡ 高性能处理** - 并行处理多个资源，优化响应速度
- **🛡️ 安全限制** - 文件内容支持最大9MB限制（可通过RESOURCE_MAX_FILE_CONTENT_SIZE配置）

### 运行时资源配置 (runtime_resource)

WAIY框架支持**运行时资源配置**，可以在对话中指定云资源环境，支持不同的执行场景。

**🔧 可选性说明：**
- runtime_resource参数是**完全可选的**
- 如果没有传递此参数，系统将跳过运行时资源处理（等同于type="none"）

#### 配置格式
```json
{
  "type": "agentbay|desktop|none",        // 必填：资源类型
  "token": "资源访问凭证",               // agentbay和desktop场景必填，none场景可选
  "region": "云资源地域",               // desktop场景必填，其他场景可选
  "cloud_resource_id": "云资源唯一标识"   // desktop场景必填，其他场景可选
}
```

#### 资源类型说明

##### 1. agentbay - 动态资源
```json
{
  "type": "agentbay",
  "token": "api-key-xxxxx"    // 必填：API认证密钥
}
```
用于访问动态云资源，通过API Key进行认证。**必填字段：token**

##### 2. desktop - 指定桌面资源
```json
{
  "type": "desktop",
  "token": "access-token-xxxxx",      // 必填：访问令牌
  "region": "cn-shanghai",            // 必填：云资源地域
  "cloud_resource_id": "ecd-1234567890"  // 必填：桌面资源ID
}
```
用于访问指定的桌面云资源。**必填字段：token、region、cloud_resource_id**

##### 3. none - 无资源
```json
{
  "type": "none"    // 仅需指定类型，无其他必填字段
}
```
不使用特定云资源的场景，无需提供任何认证信息。**必填字段：仅type**

### 资源类型

#### 1. 知识库资源 (knowledge_base)
```json
{
  "type": "knowledge_base",
  "kb_id": "technical_docs",           // 知识库ID
  "query_parameters": {               // 可选：查询参数
    "doc_memory":{}
  },
  "top_k": 5                         // 返回结果数量 (1-20)
}
```

#### 2. 文件资源 (file)
```json
{
  "type": "file",
  "content": "解析后的文件内容...",   // 必需：文件内容(默认最大9MB，可通过配置调整)
  "filename": "report.pdf",        // 可选：文件名
  "file_type": "pdf",             // 可选：文件类型
  "file_size": 1024,              // 可选：文件大小(字节)
  "address": "https://...",       // 可选：文件地址
  "upload_time": "2024-01-20T..."  // 可选：上传时间
}
```

### 使用示例

#### 基础资源对话
```bash
# 使用知识库和文件资源的对话
curl -X POST http://localhost:8000/apps/manus/message \
  -H "Content-Type: application/json" \
  -d '{
    "message": "根据技术文档和报告分析当前架构",
    "resources": [
      {
        "type": "knowledge_base",
        "kb_id": "architecture_docs",
        "query_parameters": {"doc_memory":{}},
        "top_k": 3
      },
      {
        "type": "file",
        "content": "当前系统采用微服务架构...",
        "filename": "系统架构报告.md",
        "file_type": "markdown"
      }
    ]
  }'
```

#### 带运行时资源的对话
```bash
# 使用desktop环境的对话
curl -X POST http://localhost:8000/apps/manus/message \
  -H "Content-Type: application/json" \
  -d '{
    "message": "请帮我在桌面环境中创建一个文档",
    "context": {
      "session_id": "session-12345",
      "runtime_resource": {
        "type": "desktop",
        "token": "access-token-xxxxx",
        "region": "cn-shanghai",
        "cloud_resource_id": "ecd-1234567890"
      }
    },
    "resources": [
      {
        "type": "knowledge_base",
        "kb_id": "template_docs",
        "query_parameters": {"doc_memory":{}},
        "top_k": 5
      }
    ]
  }'

# 使用agentbay环境的对话
curl -X POST http://localhost:8000/apps/alpha/message \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好，请分析这个数据",
    "context": {
      "runtime_resource": {
        "type": "agentbay",
        "token": "api-key-xxxxx"
      }
    }
  }'

# 使用none环境的对话（不需要云资源）
curl -X POST http://localhost:8000/apps/alpha/message \
  -H "Content-Type: application/json" \
  -d '{
    "message": "请帮我分析这个问题",
    "context": {
      "runtime_resource": {
        "type": "none"
      }
    }
  }'

# 完全不使用runtime_resource（等同于none类型）
curl -X POST http://localhost:8000/apps/alpha/message \
  -H "Content-Type: application/json" \
  -d '{
    "message": "请帮我分析这个问题",
    "context": {}
  }'
```

### 响应格式

响应格式保持简洁，资源处理信息记录在会话记忆中：

```json
{
  "response": "基于提供的资源内容回答...",
  "trace_id": "...",
  "session_id": "...",
  "turns": 1
}
```

**注意**：资源处理的详细信息（如检索结果数量、处理时间、错误信息等）会自动记录在会话记忆中，用于后续对话的上下文理解，但不会在响应中直接返回。

### 最佳实践

1. **合理控制资源数量** - 建议单次请求不超过5个资源
2. **文件内容预处理** - 确保文件内容已经解析为文本格式
3. **知识库查询优化** - 使用具体的查询词而非泛化概念
4. **查询参数固定** - query_parameters默认为{"doc_memory":{}}，无需手动设置
5. **会话记忆依赖** - 资源处理状态记录在会话记忆中，可通过对话历史了解处理情况

## 🛠️ uv项目管理指南

### 🚀 为什么使用uv？

uv是Astral开发的现代Python项目管理工具，相比传统的pip+virtualenv方案具有以下优势：

- **🚄 超快速度** - 依赖解析和安装速度比pip快10-100倍
- **🔒 锁定文件** - 自动生成和管理`uv.lock`确保依赖一致性
- **🎯 一体化工具** - 集成虚拟环境、依赖管理、脚本运行等功能
- **📦 兼容性好** - 完全兼容PyPI和现有Python生态系统

### 📋 常用uv命令

#### 项目初始化
```bash
# 在现有项目中初始化uv（已完成，无需重复执行）
uv init --no-readme --no-pin-python

# 同步项目依赖和虚拟环境
uv sync

# 仅安装生产依赖（排除dev组）
uv sync --no-group dev
```

#### 依赖管理
```bash
# 添加新的生产依赖
uv add package-name

# 添加开发依赖
uv add --group dev package-name

# 移除依赖
uv remove package-name

# 更新所有依赖到最新版本
uv lock --upgrade

# 更新特定包
uv lock --upgrade-package package-name
```

#### 虚拟环境管理
```bash
# 显示虚拟环境路径
uv venv --show-path

# 激活虚拟环境（手动方式）
source .venv/bin/activate  # macOS/Linux
.venv\Scripts\activate     # Windows

# 或者使用uv run直接运行（推荐，无需手动激活）
uv run python script.py
uv run pytest
```

#### 脚本运行
```bash
# 运行Python脚本
uv run python main.py
uv run python api.py

# 运行测试
uv run pytest
uv run pytest tests/unit/

# 运行格式化工具
uv run black src/
uv run isort src/

# 使用自定义脚本（在pyproject.toml中定义）
uv run waiy           # 等同于 python main.py
uv run waiy-api       # 等同于 python api.py
```

#### 信息查看
```bash
# 查看项目信息
uv info

# 显示依赖树
uv tree

# 检查依赖冲突
uv pip check
```

### ⚙️ uv配置说明

项目中的uv配置位于`pyproject.toml`文件中：

```toml
[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"           # 主索引源（阿里云镜像）
extra-index-url = ["http://yum.tbsite.net/aliyun-pypi/simple/"] # 额外索引源（内部源）
allow-insecure-host = ["mirrors.aliyun.com", "yum.tbsite.net"]  # 允许不安全主机
prerelease = "allow"                                             # 允许预发布版本
index-strategy = "unsafe-best-match"                             # 使用最佳匹配策略
```

### 🔄 迁移指南

如果你之前使用pip管理依赖，可以按以下步骤迁移到uv：

1. **安装uv**
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. **删除旧的虚拟环境**
```bash
rm -rf venv/  # 删除旧的虚拟环境目录
```

3. **使用uv重新同步**
```bash
uv sync  # 创建新的.venv虚拟环境并安装所有依赖
```

4. **更新IDE设置**
   - 将Python解释器路径指向`.venv/bin/python`（macOS/Linux）
   - 或`.venv\Scripts\python.exe`（Windows）

### 🚨 注意事项

- **锁定文件**：`uv.lock`文件记录了确切的依赖版本，应该提交到版本控制中
- **虚拟环境**：`.venv/`目录不应提交到版本控制中，已在`.gitignore`中排除
- **兼容性**：uv完全兼容pip，现有的pip命令可以用`uv pip`替代
- **性能**：首次运行可能需要下载和缓存依赖，后续运行会显著加速

## 🛠️ 开发指南

### 创建新应用

1. **创建应用目录**
```bash
mkdir src/applications/my_app
touch src/applications/my_app/__init__.py
touch src/applications/my_app/app.py
touch src/applications/my_app/prompts.py
```

2. **实现应用类**
```python
# src/applications/my_app/app.py
from src.applications.base import BaseApplication
from src.common.models import AppMetadata

class MyApp(BaseApplication):
    @property
    def metadata(self) -> AppMetadata:
        return AppMetadata(
            id="my-app",
            name="我的应用",
            version="1.0.0",
            description="应用功能描述",
            tags=["标签1", "标签2"],
            mcp_servers=["xxx", "yyy"]
        )

    async def setup_agents(self) -> None:
        self.primary_agent = await self.create_agent(
            name="main_agent",
            instructions="智能体指令",
            is_primary=True
            # 默认自动添加WAIY输入和输出护栏
            # 可选：自定义护栏配置
            # input_guardrails=[],    # 禁用输入护栏
            # output_guardrails=[]    # 禁用输出护栏
        )
```
对于流程复杂的多智能体应用，开发者应重载BaseApplication基类中的_do_process_message方法，定义自己编排的执行过程，此时primary_agent将不会起作用。

**自定义资源处理（可选）**
```python
async def _enhance_message_with_resources(
    self,
    message: str,
    resources: List[Dict[str, Any]],
    context: Dict[str, Any]
) -> tuple[str, Dict[str, Any]]:
    """重写此方法自定义资源处理逻辑"""
    # 调用父类默认处理
    enhanced_message, resource_context = await super()._enhance_message_with_resources(
        message, resources, context
    )

    # 添加应用特定的资源处理逻辑
    # ...

    return enhanced_message, resource_context
```

3. **导出应用**
```python
# src/applications/__init__.py
from .my_app import MyApp
__all__ = [..., "MyApp"]
```

### 添加新工具

```python
# src/core/plugins/my_tools.py
from agents import function_tool

@function_tool
async def my_custom_tool(param: str) -> str:
    """工具功能描述"""
    # 实现工具逻辑
    return "处理结果"
```

### 配置管理

框架支持多层级配置：

1. **环境变量**（最高优先级）
2. **.env文件**（最低优先级）：调试时设置此文件即可覆盖默认值
3. **动态配置中心**（中等优先级） ：在服务端通过diamond动态加载


**主要配置项**
```bash
# LLM配置
LLM_MAIN_MODEL_PROVIDER=openai|bailian|deepseek|others
LLM_MAIN_MODEL=gpt-4o-mini
LLM_OPENAI_API_KEY=your_key

# MCP服务器配置
MCP_ENABLE_FILE_SYSTEM=true
MCP_ENABLE_BROWSER=true
MCP_ENABLE_TERMINAL=true

# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_REQUEST_TIMEOUT=300

# 搜索配置
SEARCH_TAVILY_API_KEY=your_key
SEARCH_IQS_KEY_ID=your_id
SEARCH_IQS_KEY_SECRET=your_secret

# 资源对话配置（阿里云无影AI）
RESOURCE_RAG_ACCESS_KEY_ID=your_access_key_id
RESOURCE_RAG_ACCESS_KEY_SECRET=your_access_key_secret
RESOURCE_RAG_ENDPOINT=wuyingaiinner-pre.aliyuncs.com
RESOURCE_RAG_SERVICE_TIMEOUT=30
RESOURCE_MAX_FILE_CONTENT_SIZE=9437184  # 9MB限制

# 护栏系统配置
GUARDRAIL_ENABLED=true                    # 护栏开关
GUARDRAIL_STRICT_MODE=false              # 严格模式（疑似违规也会拦截）
GUARDRAIL_CONTENT_CHECK_APP_KEY=34686242 # 内容检测API的app_key
GUARDRAIL_CONTENT_CHECK_APP_SECRET=your_secret_here
GUARDRAIL_CONTENT_CHECK_ENDPOINT=http://gw.api.taobao.com/router/rest
GUARDRAIL_CONTENT_CHECK_TIMEOUT=10
```

## 🧪 测试

### 单元测试
```bash
# 使用uv运行测试（推荐）
uv run pytest

# 运行特定测试
uv run pytest tests/unit/

# 查看覆盖率
uv run pytest --cov=src

# 或传统方式（需要先激活虚拟环境）
pytest
pytest tests/unit/
pytest --cov=src
```

### 压力测试
```bash
# 使用uv运行压力测试（推荐）
uv run python tests/load_test/main.py

# 生成测试报告
uv run python tests/load_test/generate_report.py

# 或传统方式
python tests/load_test/main.py
python tests/load_test/generate_report.py
```

### API测试
```bash
# 使用curl测试
curl -X POST http://localhost:8000/apps/manus/message \
  -H "Content-Type: application/json" \
  -d '{"message": "帮我创建一个文件"}'
```

## 📊 监控和运维

### 日志系统
```bash
# 日志配置
LOG_LEVEL=DEBUG|INFO|WARNING|ERROR
LOG_TO_FILE=true
LOG_FILE_PATH=logs/waiy.log
LOG_DETAILED_MODE=true
```

### 性能监控
- 支持OpenTelemetry标准
- 集成Logfire链路追踪
- 内置性能指标收集

### 健康检查
```bash
# 检查服务状态
curl http://localhost:8000/status.taobao
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 提交Pull Request

## 📄 许可证

本项目采用专有许可证。详见 [LICENSE](LICENSE) 文件。

## 🆘 支持

- 📚 **文档**: [在线文档](docs/)
- 🐛 **问题反馈**: [GitHub Issues](issues)
- 💬 **技术交流**: 内部技术群

## 📈 版本历史

- **v0.3.0** (2025-08-12)
  - 🛠️ uv工具支持，提供现代化的Python项目管理
  - 🚀 添加 Runtime 云资源管理能力

- **v0.2.0** (2025-07-17)
  - 📁 新增资源对话功能
  - 🔄 重构资源处理架构，支持前置文件解析
  - 📚 支持知识库检索和文件内容对话
  - 🛡️ 增强安全限制和内容验证
  - ⚡ 优化资源处理性能

- **v0.1.0** (2025-01-20)
  - 🎉 初始版本发布
  - ✅ 基础框架完成
  - 🚀 支持5个内置应用
  - 🌐 Web UI界面完成

---

<div align="center">

**🚀 基于多智能体协同的下一代AI开发框架**

Made with ❤️ by WAIY Team

</div>
