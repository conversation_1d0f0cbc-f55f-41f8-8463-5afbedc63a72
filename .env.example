# LLM 配置
LLM_OPENAI_API_KEY=
LLM_BAILIAN_API_KEY=
LLM_DEEPSEEK_API_KEY=
LLM_OPENROUTER_API_KEY=
LLM_GEMINI_API_KEY=
LLM_ANTHROPIC_API_KEY=
LLM_HUGGINGFACE_API_KEY=
LLM_OTHERS_PROVIDER_URL=xxx
LLM_OTHERS_PROVIDER_API_KEY=xxx

# 模型配置
############ 高性能模型组 ###############
LLM_REASONING_MODEL_PROVIDER_HIGH=dashscope
LLM_REASONING_MODEL_HIGH=qwen-plus
LLM_MAIN_MODEL_PROVIDER_HIGH=dashscope
LLM_MAIN_MODEL_HIGH=qwen-plus
LLM_FAST_MODEL_PROVIDER_HIGH=dashscope
LLM_FAST_MODEL_HIGH=qwen-max
LLM_CODE_MODEL_PROVIDER_HIGH=dashscope
LLM_CODE_MODEL_HIGH=qwen-plus
############ 均衡性能模型组 ###############
LLM_REASONING_MODEL_PROVIDER_MEDIUM=dashscope
LLM_REASONING_MODEL_MEDIUM=qwen-plus
LLM_MAIN_MODEL_PROVIDER_MEDIUM=dashscope
LLM_MAIN_MODEL_MEDIUM=qwen-plus
LLM_FAST_MODEL_PROVIDER_MEDIUM=dashscope
LLM_FAST_MODEL_MEDIUM=qwen-max
LLM_CODE_MODEL_PROVIDER_MEDIUM=dashscope
LLM_CODE_MODEL_MEDIUM=qwen-plus
############ 经济型模型组 ###############
LLM_REASONING_MODEL_PROVIDER_LOW=dashscope
LLM_REASONING_MODEL_LOW=qwen-plus
LLM_MAIN_MODEL_PROVIDER_LOW=dashscope
LLM_MAIN_MODEL_LOW=qwen-plus
LLM_FAST_MODEL_PROVIDER_LOW=dashscope
LLM_FAST_MODEL_LOW=qwen-max
LLM_CODE_MODEL_PROVIDER_LOW=dashscope
LLM_CODE_MODEL_LOW=qwen-plus


# 阿里云IQS搜索配置
SEARCH_IQS_KEY_ID=your_iqs_key_id_here
SEARCH_IQS_KEY_SECRET=your_iqs_key_secret_here
SEARCH_TAVILY_API_KEY=

# MCP服务器配置
MCP_WUYING_SERVER_URL=https://agentbay.wuying.aliyun.com/sse
MCP_WUYING_SERVER_API_KEY=your_mcp_api_key_here
MCP_DEFAULT_WORKSPACE_PATH=.
MCP_ENABLE_FILE_SYSTEM=true
MCP_ENABLE_GIT=true
MCP_ENABLE_TERMINAL=true
MCP_ENABLE_BROWSER=true
MCP_ENABLE_THINKING=true
MCP_ENABLE_PYTHON=true

# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_REQUEST_TIMEOUT=600
API_DEBUG=false
API_CORS_ORIGINS=["*"]

# 数据库配置
DB_MYSQL_HOST=rm-bp16k096fduih61zzyo.mysql.rds.aliyuncs.com
DB_MYSQL_USER=waiy_infra
DB_MYSQL_PASSWORD=
DB_MYSQL_NAME=waiy_infra
DB_MYSQL_PORT=3306

# 对话资源配置
RESOURCE_RAG_ACCESS_KEY_ID=your_access_key_id
RESOURCE_RAG_ACCESS_KEY_SECRET=your_access_key_secret
RESOURCE_RAG_ENDPOINT=wuyingaiinner-pre.aliyuncs.com
RESOURCE_RAG_SERVICE_TIMEOUT=30
RESOURCE_MAX_FILE_CONTENT_SIZE=9437184  # 9MB限制

# 服务器配置
SERVICE_RAM_ROLE=acs:ram::1550203943326350:role/waiy-infra-daily
SERVICE_REGION_ID=cn-hangzhou
SERVICE_APP_NAME=waiy-infra
SERVICE_APP_ENV=testing

GUARDRAIL_ENABLED=true          # 启用护栏
GUARDRAIL_STRICT_MODE=false     # 严格模式
GUARDRAIL_CONTENT_CHECK_APP_SECRET=

# EVN 运行环境配置
IS_SERVER_ENV = False 