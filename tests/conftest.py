"""
全局测试配置文件
包含通用夹具和配置
"""

import asyncio
import os
import sys
from pathlib import Path

import pytest

# 将项目根目录添加到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.common.config import settings


# 测试环境设置
@pytest.fixture(scope="session")
def test_env():
    """设置测试环境变量"""
    # 保存原始环境变量
    original_env = {key: value for key, value in os.environ.items()}

    # 设置测试环境变量
    os.environ["OPENAI_API_KEY"] = "test_api_key"
    os.environ["OPENAI_MODEL"] = "test-model"
    os.environ["IQS_KEY_ID"] = "test_iqs_key_id"
    os.environ["IQS_KEY_SECRET"] = "test_iqs_key_secret"
    os.environ["LOG_LEVEL"] = "DEBUG"
    os.environ["LOG_TO_FILE"] = "false"
    os.environ["MCP_ENABLE_FILE_SYSTEM"] = "false"
    os.environ["MCP_ENABLE_GIT"] = "false"
    os.environ["MCP_ENABLE_TERMINAL"] = "false"
    os.environ["MCP_ENABLE_BROWSER"] = "false"
    os.environ["MCP_ENABLE_THINKING"] = "true"
    os.environ["MCP_ENABLE_PYTHON"] = "true"

    yield

    # 恢复原始环境变量
    for key in list(os.environ.keys()):
        if key in original_env:
            os.environ[key] = original_env[key]
        else:
            del os.environ[key]

    for key, value in original_env.items():
        os.environ[key] = value


# 创建测试数据目录
@pytest.fixture(scope="session")
def test_data_dir():
    """设置临时测试数据目录"""
    test_dir = project_root / "tests" / "fixtures" / "data"
    test_dir.mkdir(parents=True, exist_ok=True)
    return test_dir


# 异步测试支持
@pytest.fixture(scope="session")
def event_loop():
    """创建并提供一个全局事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# 模拟MCP服务器
@pytest.fixture
async def mock_mcp_server():
    """提供一个模拟的MCP服务器对象"""
    from unittest.mock import AsyncMock, MagicMock

    from agents.mcp import MCPServer, MCPToolsDirectoryItem, MCPToolsDirectoryResponse

    mock_server = AsyncMock(spec=MCPServer)
    mock_server.name = "Mock MCP Server"

    # 模拟list_tools方法
    mock_items = [
        MCPToolsDirectoryItem(
            type="function",
            name="test_tool",
            title="Test Tool",
            description="A test tool for testing",
            parameters={},
        )
    ]
    mock_response = MCPToolsDirectoryResponse(items=mock_items)
    mock_server.list_tools.return_value = mock_response

    # 模拟execute_tool方法
    mock_server.execute_tool.return_value = {"result": "success"}

    # 模拟connect和cleanup方法
    mock_server.connect = AsyncMock()
    mock_server.cleanup = AsyncMock()

    return mock_server


# 模拟OpenAI客户端
@pytest.fixture
def mock_openai_client():
    """提供一个模拟的OpenAI客户端"""
    from unittest.mock import AsyncMock, MagicMock

    mock_client = AsyncMock()
    mock_client.chat.completions.create = AsyncMock()

    # 设置模拟响应
    mock_completion = MagicMock()
    mock_completion.choices = [MagicMock()]
    mock_completion.choices[0].message = MagicMock()
    mock_completion.choices[0].message.content = "测试响应"
    mock_completion.choices[0].message.tool_calls = []

    mock_client.chat.completions.create.return_value = mock_completion

    return mock_client
