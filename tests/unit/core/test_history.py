"""
历史记录管理模块的单元测试
"""

import json
import os
import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import mock_open, patch

import pytest

from src.common.config import settings
from src.core.memory.history import HistoryManager


@pytest.fixture
def temp_history_file():
    """创建临时历史记录文件"""
    fd, path = tempfile.mkstemp()
    os.close(fd)
    temp_path = Path(path)
    yield temp_path
    if temp_path.exists():
        temp_path.unlink()


def test_history_manager_init():
    """测试历史记录管理器初始化"""
    # 测试默认初始化
    manager = HistoryManager()
    assert manager.history_file == settings.history.history_file

    # 测试自定义文件路径
    custom_path = Path("custom/path.json")
    manager = HistoryManager(custom_path)
    assert manager.history_file == custom_path


def test_load_history_empty(temp_history_file):
    """测试加载空历史记录"""
    manager = HistoryManager(temp_history_file)

    # 文件不存在的情况
    if temp_history_file.exists():
        temp_history_file.unlink()

    history = manager.load_history()
    assert history == []


def test_load_history_with_data(temp_history_file):
    """测试加载有数据的历史记录"""
    test_data = [
        {"timestamp": datetime.now().isoformat(), "task": "测试任务1", "result": "测试结果1"},
        {"timestamp": datetime.now().isoformat(), "task": "测试任务2", "result": "测试结果2"},
    ]

    # 写入测试数据
    with open(temp_history_file, "w", encoding="utf-8") as f:
        json.dump(test_data, f)

    manager = HistoryManager(temp_history_file)
    history = manager.load_history()

    assert history == test_data
    assert len(history) == 2
    assert history[0]["task"] == "测试任务1"
    assert history[1]["result"] == "测试结果2"


def test_load_history_invalid_json(temp_history_file):
    """测试加载无效的JSON历史记录"""
    # 写入无效的JSON
    with open(temp_history_file, "w", encoding="utf-8") as f:
        f.write("这不是有效的JSON")

    manager = HistoryManager(temp_history_file)
    history = manager.load_history()

    # 应返回空列表
    assert history == []


def test_save_history(temp_history_file):
    """测试保存历史记录"""
    test_data = [
        {"timestamp": datetime.now().isoformat(), "task": "测试任务", "result": "测试结果"}
    ]

    manager = HistoryManager(temp_history_file)
    manager.save_history(test_data)

    # 验证文件内容
    with open(temp_history_file, "r", encoding="utf-8") as f:
        saved_data = json.load(f)

    assert saved_data == test_data


def test_add_entry(temp_history_file):
    """测试添加历史记录条目"""
    manager = HistoryManager(temp_history_file)

    # 添加一个条目
    manager.add_entry("测试任务", "测试结果")

    # 验证历史记录
    history = manager.load_history()
    assert len(history) == 1
    assert history[0]["task"] == "测试任务"
    assert history[0]["result"] == "测试结果"

    # 再添加一个条目
    manager.add_entry("测试任务2", "测试结果2")

    # 验证历史记录
    history = manager.load_history()
    assert len(history) == 2
    assert history[1]["task"] == "测试任务2"
    assert history[1]["result"] == "测试结果2"


def test_add_entry_long_result(temp_history_file):
    """测试添加长结果的历史记录条目"""
    manager = HistoryManager(temp_history_file)

    # 创建超出最大长度的结果
    long_result = "a" * (settings.history.max_result_length + 100)

    # 添加条目
    manager.add_entry("长结果测试", long_result)

    # 验证历史记录
    history = manager.load_history()
    assert len(history) == 1
    assert history[0]["task"] == "长结果测试"
    assert len(history[0]["result"]) == settings.history.max_result_length + 3  # +3是因为"..."
    assert history[0]["result"].endswith("...")


def test_add_entry_max_entries(temp_history_file):
    """测试历史记录条目数量限制"""
    # 修改设置以便于测试
    old_max_entries = settings.history.max_history_entries
    settings.history.max_history_entries = 3

    try:
        manager = HistoryManager(temp_history_file)

        # 添加超过限制的条目
        for i in range(5):
            manager.add_entry(f"测试任务{i}", f"测试结果{i}")

        # 验证历史记录
        history = manager.load_history()
        assert len(history) == 3  # 应只保留最新的3条
        assert history[0]["task"] == "测试任务2"
        assert history[1]["task"] == "测试任务3"
        assert history[2]["task"] == "测试任务4"
    finally:
        # 恢复设置
        settings.history.max_history_entries = old_max_entries


def test_clear_history(temp_history_file):
    """测试清除历史记录"""
    manager = HistoryManager(temp_history_file)

    # 添加一些条目
    manager.add_entry("测试任务1", "测试结果1")
    manager.add_entry("测试任务2", "测试结果2")

    # 清除历史记录
    manager.clear_history()

    # 验证历史记录为空
    history = manager.load_history()
    assert history == []


def test_get_formatted_history(temp_history_file):
    """测试获取格式化的历史记录"""
    manager = HistoryManager(temp_history_file)

    # 添加一些条目
    for i in range(10):
        manager.add_entry(f"测试任务{i}", f"测试结果{i}")

    # 获取格式化的历史记录
    formatted = manager.get_formatted_history(limit=3)

    # 验证格式化的字符串
    assert "最近的任务历史" in formatted
    assert "测试任务7" in formatted
    assert "测试任务8" in formatted
    assert "测试任务9" in formatted
    assert "测试任务6" not in formatted  # 应超出限制

    # 测试没有历史记录的情况
    manager.clear_history()
    formatted = manager.get_formatted_history()
    assert "没有历史记录" in formatted


def test_get_recent_tasks(temp_history_file):
    """测试获取最近任务"""
    manager = HistoryManager(temp_history_file)

    # 添加一些条目
    for i in range(15):
        manager.add_entry(f"测试任务{i}", f"测试结果{i}")

    # 获取最近任务
    recent = manager.get_recent_tasks(limit=5)

    # 验证结果
    assert len(recent) == 5
    assert recent[0]["task"] == "测试任务10"
    assert recent[4]["task"] == "测试任务14"

    # 测试默认限制
    recent = manager.get_recent_tasks()
    assert len(recent) == 10

    # 测试没有历史记录的情况
    manager.clear_history()
    recent = manager.get_recent_tasks()
    assert recent == []
