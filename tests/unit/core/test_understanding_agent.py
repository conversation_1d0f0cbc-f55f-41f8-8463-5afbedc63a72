#!/usr/bin/env python3
"""
测试理解确认agent的功能
"""
import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.applications.deeper_research.agents.understanding_agent import (
    INSTRUCTIONS,
    UnderstandingOutput,
)


def test_understanding_agent():
    """测试理解确认agent的指令和输出格式"""
    print("=== 测试理解确认Agent ===")
    print(f"指令长度: {len(INSTRUCTIONS)} 字符")
    print(f"指令预览: {INSTRUCTIONS[:200]}...")

    # 测试输出格式
    test_output = UnderstandingOutput(
        understanding_summary="我已明确本次研究将聚焦于新能源汽车的市场现状与发展趋势。我将深入探讨市场规模、销量、市场份额、主要参与者、区域分布、政府政策、面临的挑战与机遇、技术进步以及未来发展趋势，并对主要国家和地区进行比较分析。",
        key_areas=[
            "市场规模",
            "销量数据",
            "市场份额",
            "主要参与者",
            "区域分布",
            "政府政策",
            "挑战与机遇",
            "技术进步",
            "未来趋势",
        ],
    )

    print(f"\n测试输出格式:")
    print(f"理解总结: {test_output.understanding_summary}")
    print(f"关键领域: {test_output.key_areas}")

    # 测试JSON序列化
    json_output = test_output.model_dump_json(indent=2)
    print(f"\nJSON输出:\n{json_output}")

    print("\n✅ 理解确认Agent测试完成")


if __name__ == "__main__":
    test_understanding_agent()
