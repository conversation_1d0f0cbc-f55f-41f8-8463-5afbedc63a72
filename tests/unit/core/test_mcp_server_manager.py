"""
MCP服务器管理模块的单元测试
"""

import shutil
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.common.config import settings
from src.core.mcp import MCPServerManager


@pytest.fixture
def mock_shutil_which():
    """模拟shutil.which"""
    with patch("src.core.mcp.server_manager.shutil.which", return_value="/usr/bin/npx") as mock:
        yield mock


@pytest.fixture
def mock_mcp_server():
    """模拟MCPServer"""
    server_mock = AsyncMock()
    server_mock.name = "Mock MCP Server"
    server_mock.connect = AsyncMock()
    server_mock.cleanup = AsyncMock()
    return server_mock


@pytest.fixture
def mock_server_stdio():
    """模拟MCPServerStdio构造函数"""
    with patch("src.core.mcp.server_manager.MCPServerStdio") as mock:
        # 配置模拟对象返回自身
        instance = AsyncMock()
        instance.connect = AsyncMock()
        instance.cleanup = AsyncMock()
        mock.return_value = instance
        yield mock


def test_init_with_dependencies(mock_shutil_which):
    """测试依赖项存在时的初始化"""
    # 配置模拟对象返回True，表示找到命令
    mock_shutil_which.return_value = "/usr/bin/npx"

    # 初始化管理器
    manager = MCPServerManager()

    # 验证初始化成功
    assert manager.servers == []
    mock_shutil_which.assert_called_with("npx")


def test_init_without_dependencies(mock_shutil_which):
    """测试依赖项不存在时的初始化"""
    # 配置模拟对象返回None，表示未找到命令
    mock_shutil_which.return_value = None

    # 初始化管理器应该引发异常
    with pytest.raises(RuntimeError) as excinfo:
        MCPServerManager()

    # 验证异常消息
    assert "缺少必要的依赖" in str(excinfo.value)
    assert "npx" in str(excinfo.value)
    mock_shutil_which.assert_called_with("npx")


@pytest.mark.asyncio
async def test_setup_servers_all_enabled(mock_shutil_which, mock_server_stdio):
    """测试设置所有启用的服务器"""
    # 保存原始设置
    original_settings = {
        "enable_file_system": settings.mcp.enable_file_system,
        "enable_git": settings.mcp.enable_git,
        "enable_terminal": settings.mcp.enable_terminal,
        "enable_browser": settings.mcp.enable_browser,
        "enable_thinking": settings.mcp.enable_thinking,
        "enable_python": settings.mcp.enable_python,
    }

    try:
        # 配置所有服务器为启用
        settings.mcp.enable_file_system = True
        settings.mcp.enable_git = True
        settings.mcp.enable_terminal = True
        settings.mcp.enable_browser = True
        settings.mcp.enable_thinking = True
        settings.mcp.enable_python = True

        # 创建管理器
        manager = MCPServerManager()

        # 设置服务器
        servers = await manager.setup_servers()

        # 验证所有服务器都被启动
        assert len(servers) == 6
        assert mock_server_stdio.call_count == 6

        # 验证每个connect方法都被调用
        for call in mock_server_stdio.return_value.connect.call_args_list:
            assert call == ()  # 无参数调用

    finally:
        # 恢复原始设置
        settings.mcp.enable_file_system = original_settings["enable_file_system"]
        settings.mcp.enable_git = original_settings["enable_git"]
        settings.mcp.enable_terminal = original_settings["enable_terminal"]
        settings.mcp.enable_browser = original_settings["enable_browser"]
        settings.mcp.enable_thinking = original_settings["enable_thinking"]
        settings.mcp.enable_python = original_settings["enable_python"]


@pytest.mark.asyncio
async def test_setup_servers_selective(mock_shutil_which, mock_server_stdio):
    """测试只启用部分服务器"""
    # 保存原始设置
    original_settings = {
        "enable_file_system": settings.mcp.enable_file_system,
        "enable_git": settings.mcp.enable_git,
        "enable_terminal": settings.mcp.enable_terminal,
        "enable_browser": settings.mcp.enable_browser,
        "enable_thinking": settings.mcp.enable_thinking,
        "enable_python": settings.mcp.enable_python,
    }

    try:
        # 只启用部分服务器
        settings.mcp.enable_file_system = True
        settings.mcp.enable_git = False
        settings.mcp.enable_terminal = True
        settings.mcp.enable_browser = False
        settings.mcp.enable_thinking = True
        settings.mcp.enable_python = False

        # 创建管理器
        manager = MCPServerManager()

        # 设置服务器
        servers = await manager.setup_servers()

        # 验证只有3个服务器被启动
        assert len(servers) == 3
        assert mock_server_stdio.call_count == 3

    finally:
        # 恢复原始设置
        settings.mcp.enable_file_system = original_settings["enable_file_system"]
        settings.mcp.enable_git = original_settings["enable_git"]
        settings.mcp.enable_terminal = original_settings["enable_terminal"]
        settings.mcp.enable_browser = original_settings["enable_browser"]
        settings.mcp.enable_thinking = original_settings["enable_thinking"]
        settings.mcp.enable_python = original_settings["enable_python"]


@pytest.mark.asyncio
async def test_server_creation_methods(mock_shutil_which, mock_server_stdio):
    """测试每个服务器创建方法"""
    manager = MCPServerManager()

    # 测试文件系统服务器创建
    fs_server = await manager._create_filesystem_server()
    assert fs_server is not None
    mock_server_stdio.assert_called_with(
        name="Filesystem Server",
        cache_tools_list=False,
        params={
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-filesystem",
                settings.mcp.default_workspace_path,
            ],
        },
    )

    # 重置mock
    mock_server_stdio.reset_mock()

    # 测试Git服务器创建
    git_server = await manager._create_git_server()
    assert git_server is not None
    mock_server_stdio.assert_called_with(
        name="Git Server",
        cache_tools_list=False,
        params={
            "command": "npx",
            "args": ["-y", "mcp-server-git"],
        },
    )

    # 重置mock
    mock_server_stdio.reset_mock()

    # 测试终端服务器创建
    terminal_server = await manager._create_terminal_server()
    assert terminal_server is not None
    mock_server_stdio.assert_called_with(
        name="Terminal Server",
        cache_tools_list=False,
        params={
            "command": "npx",
            "args": ["-y", "@wonderwhy-er/desktop-commander"],
        },
    )


@pytest.mark.asyncio
async def test_cleanup(mock_shutil_which):
    """测试清理方法"""
    # 创建管理器
    manager = MCPServerManager()

    # 添加模拟服务器
    server1 = AsyncMock()
    server1.name = "Server 1"
    server2 = AsyncMock()
    server2.name = "Server 2"

    manager.servers = [server1, server2]

    # 调用清理方法
    await manager.cleanup()

    # 验证每个服务器的cleanup方法都被调用
    server1.cleanup.assert_called_once()
    server2.cleanup.assert_called_once()

    # 验证服务器列表被清空
    assert manager.servers == []


@pytest.mark.asyncio
async def test_cleanup_with_errors(mock_shutil_which):
    """测试清理出错的情况"""
    # 创建管理器
    manager = MCPServerManager()

    # 添加模拟服务器，第一个会引发异常
    server1 = AsyncMock()
    server1.name = "Server 1"
    server1.cleanup.side_effect = Exception("清理错误")

    server2 = AsyncMock()
    server2.name = "Server 2"

    manager.servers = [server1, server2]

    # 模拟logger.error
    with patch("src.core.mcp.server_manager.logger.error") as mock_error:
        # 调用清理方法
        await manager.cleanup()

        # 验证错误被记录
        mock_error.assert_called_once()
        assert "清理服务器" in mock_error.call_args[0][0]
        assert "Server 1" in mock_error.call_args[0][0]

    # 验证第二个服务器的cleanup方法仍然被调用
    server2.cleanup.assert_called_once()

    # 验证服务器列表被清空
    assert manager.servers == []


@pytest.mark.asyncio
async def test_setup_servers_error_handling(mock_shutil_which, mock_server_stdio):
    """测试服务器设置错误处理"""
    # 保存原始设置
    original_settings = {
        "enable_file_system": settings.mcp.enable_file_system,
        "enable_git": settings.mcp.enable_git,
    }

    try:
        # 启用两个服务器，一个成功，一个失败
        settings.mcp.enable_file_system = True
        settings.mcp.enable_git = True
        settings.mcp.enable_terminal = False
        settings.mcp.enable_browser = False
        settings.mcp.enable_thinking = False
        settings.mcp.enable_python = False

        # 创建管理器
        manager = MCPServerManager()

        # 让第一个服务器创建成功，第二个引发异常
        original_fs_server = manager._create_filesystem_server
        original_git_server = manager._create_git_server

        async def mock_create_fs_server():
            return await original_fs_server()

        async def mock_create_git_server():
            raise Exception("Git服务器创建失败")

        manager._create_filesystem_server = mock_create_fs_server
        manager._create_git_server = mock_create_git_server

        # 模拟logger.error
        with patch("src.core.mcp.server_manager.print_error") as mock_print_error:
            with patch("src.core.mcp.server_manager.logger.error") as mock_error:
                # 设置服务器
                servers = await manager.setup_servers()

                # 验证只有一个服务器被启动
                assert len(servers) == 1

                # 验证错误被记录
                mock_print_error.assert_called_once()
                assert "Git服务器" in mock_print_error.call_args[0][0]
                mock_error.assert_called_once()
                assert "Git服务器" in mock_error.call_args[0][0]

    finally:
        # 恢复原始设置
        settings.mcp.enable_file_system = original_settings["enable_file_system"]
        settings.mcp.enable_git = original_settings["enable_git"]
