"""
API服务器模块的单元测试
"""

from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient

from src.api.models import AgentRequest, AgentResponse, ErrorResponse
from src.api.server import app, get_agent


@pytest.fixture
def test_client():
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def mock_agent():
    """创建模拟智能体"""
    mock = MagicMock()
    return mock


@pytest.fixture
def mock_agent_factory():
    """模拟AgentFactory"""
    with patch("src.api.server.AgentFactory") as mock_factory:
        yield mock_factory


@pytest.fixture
def mock_runner():
    """模拟Runner"""
    with patch("src.api.server.Runner") as mock_runner:
        # 设置run方法的返回值
        mock_result = MagicMock()
        mock_result.final_output = "测试响应"
        mock_result.turns = ["turn1"]
        mock_runner.run.return_value = mock_result
        yield mock_runner


@pytest.fixture
def mock_history_manager():
    """模拟历史记录管理器"""
    with patch("src.api.server.history_manager") as mock_manager:
        yield mock_manager


def test_root_endpoint(test_client):
    """测试根路径端点"""
    response = test_client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "name" in data
    assert "version" in data
    assert "description" in data
    assert "docs_url" in data
    assert data["name"] == "WAIY智能体框架API"


def test_chat_with_agent_success(test_client, mock_agent, mock_runner, mock_history_manager):
    """测试成功的智能体对话"""
    # 替换依赖项
    app.dependency_overrides[get_agent] = lambda: mock_agent

    # 发送请求
    response = test_client.post("/agent/chat", json={"prompt": "你好", "max_turns": 1})

    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["result"] == "测试响应"
    assert "trace_id" in data
    assert data["turns"] == 1
    assert "elapsed_time" in data
    assert data["message"] == "处理成功"

    # 验证调用
    mock_runner.run.assert_called_once()
    mock_history_manager.add_entry.assert_called_once()

    # 清理
    app.dependency_overrides.clear()


def test_chat_with_agent_error(test_client, mock_agent, mock_runner):
    """测试智能体对话出错的情况"""
    # 替换依赖项
    app.dependency_overrides[get_agent] = lambda: mock_agent

    # 设置模拟错误
    mock_runner.run.side_effect = ValueError("测试错误")

    # 发送请求
    response = test_client.post("/agent/chat", json={"prompt": "你好", "max_turns": 1})

    # 验证响应
    assert response.status_code == 500
    data = response.json()
    assert data["success"] is False
    assert "测试错误" in data["error"]

    # 清理
    app.dependency_overrides.clear()


def test_async_chat_with_agent(test_client, mock_agent):
    """测试异步智能体对话"""
    # 替换依赖项
    app.dependency_overrides[get_agent] = lambda: mock_agent

    # 发送请求
    response = test_client.post("/agent/async-chat", json={"prompt": "你好", "max_turns": 1})

    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "请求已接受" in data["result"]
    assert "trace_id" in data
    assert data["turns"] == 0
    assert data["elapsed_time"] == 0.0
    assert "处理已开始" in data["message"]

    # 清理
    app.dependency_overrides.clear()


@patch("src.api.server.trace")
@patch("src.api.server._run_agent_in_background")
def test_background_task_added(mock_run_bg, mock_trace, test_client, mock_agent):
    """测试后台任务添加"""
    # 替换依赖项
    app.dependency_overrides[get_agent] = lambda: mock_agent

    # 发送异步请求
    test_client.post("/agent/async-chat", json={"prompt": "你好", "max_turns": 1})

    # 验证后台任务被添加
    # 注意：由于TestClient立即执行后台任务，我们只能验证函数是否被导入，
    # 而不是验证它是否被调用。在实际运行时，BackgroundTasks会调用它。

    # 清理
    app.dependency_overrides.clear()


@patch("src.api.server.trace")
async def test_run_agent_in_background(mock_trace, mock_runner, mock_history_manager):
    """测试后台运行智能体"""
    from src.api.server import _run_agent_in_background

    # 运行后台任务
    mock_agent = MagicMock()
    await _run_agent_in_background(mock_agent, "你好", 1, "test-trace-id")

    # 验证调用
    mock_runner.run.assert_called_once()
    mock_history_manager.add_entry.assert_called_once()


@patch("src.api.server.trace")
async def test_run_agent_in_background_error(mock_trace, mock_runner):
    """测试后台运行智能体出错的情况"""
    from src.api.server import _run_agent_in_background, logger

    # 设置模拟错误
    mock_runner.run.side_effect = ValueError("测试错误")

    # 模拟logger.error
    with patch.object(logger, "error") as mock_error:
        # 运行后台任务
        mock_agent = MagicMock()
        await _run_agent_in_background(mock_agent, "你好", 1, "test-trace-id")

        # 验证错误被记录
        mock_error.assert_called_once()
        assert "测试错误" in mock_error.call_args[0][0]


async def test_get_agent_creates_new_instance(mock_agent_factory):
    """测试get_agent创建新实例"""
    from src.api.server import _cached_agent, server_manager

    # 确保缓存为空
    _cached_agent = None

    # 设置模拟返回值
    mock_servers = MagicMock()
    server_manager.setup_servers = MagicMock(return_value=mock_servers)
    mock_agent = MagicMock()
    mock_agent_factory.create_manus_agent.return_value = mock_agent

    # 调用函数
    result = await get_agent()

    # 验证结果
    assert result == mock_agent
    server_manager.setup_servers.assert_called_once()
    mock_agent_factory.create_manus_agent.assert_called_once_with(mock_servers)


async def test_get_agent_returns_cached_instance():
    """测试get_agent返回缓存实例"""
    from src.api.server import _cached_agent, server_manager

    # 设置缓存
    mock_agent = MagicMock()
    global _cached_agent
    _cached_agent = mock_agent

    # 模拟函数
    server_manager.setup_servers = MagicMock()

    # 调用函数
    result = await get_agent()

    # 验证结果
    assert result == mock_agent
    server_manager.setup_servers.assert_not_called()  # 不应该被调用

    # 清理
    _cached_agent = None


def test_exception_handler_http_exception(test_client):
    """测试HTTP异常处理器"""

    # 创建一个会引发HTTPException的路由
    @app.get("/test-http-exception")
    async def test_route():
        from fastapi import HTTPException

        raise HTTPException(status_code=400, detail="测试HTTP错误")

    # 发送请求
    response = test_client.get("/test-http-exception")

    # 验证响应
    assert response.status_code == 400
    data = response.json()
    assert data["success"] is False
    assert "测试HTTP错误" in data["error"]
    assert data["error_code"] == "400"


def test_exception_handler_generic_exception(test_client):
    """测试通用异常处理器"""

    # 创建一个会引发通用异常的路由
    @app.get("/test-generic-exception")
    async def test_route():
        raise ValueError("测试通用错误")

    # 发送请求
    response = test_client.get("/test-generic-exception")

    # 验证响应
    assert response.status_code == 500
    data = response.json()
    assert data["success"] is False
    assert "测试通用错误" in data["error"]
    assert data["error_code"] == "INTERNAL_SERVER_ERROR"
