"""
配置模块的单元测试
"""

import os

import pytest

from src.common.config import settings


def test_settings_initialized():
    """测试配置实例是否正确初始化"""
    assert settings is not None
    assert hasattr(settings, "openai")
    assert hasattr(settings, "mcp")
    assert hasattr(settings, "security")
    assert hasattr(settings, "history")
    assert hasattr(settings, "logging")
    assert hasattr(settings, "api")


def test_openai_settings_default_values():
    """测试OpenAI配置的默认值"""
    assert settings.openai.model == "qwen-max"
    assert settings.openai.max_tokens == 8192
    assert settings.openai.temperature == 0.0


def test_openai_settings_from_env(test_env):
    """测试从环境变量读取OpenAI配置"""
    from src.common.config.settings import OpenAISettings

    # 重新加载配置以应用测试环境变量
    openai_settings = OpenAISettings()

    assert openai_settings.api_key == "test_api_key"
    assert openai_settings.model == "test-model"


def test_mcp_settings_from_env(test_env):
    """测试从环境变量读取MCP配置"""
    from src.common.config.settings import MCPSettings

    # 重新加载配置以应用测试环境变量
    mcp_settings = MCPSettings()

    assert mcp_settings.enable_file_system is False
    assert mcp_settings.enable_git is False
    assert mcp_settings.enable_terminal is False
    assert mcp_settings.enable_browser is False
    assert mcp_settings.enable_thinking is True
    assert mcp_settings.enable_python is True


def test_security_settings():
    """测试安全配置"""
    dangerous_commands = settings.security.dangerous_commands
    assert isinstance(dangerous_commands, list)
    assert len(dangerous_commands) > 0
    assert "rm -rf" in dangerous_commands


def test_history_settings():
    """测试历史记录配置"""
    assert settings.history.max_history_entries == 100
    assert settings.history.max_result_length == 500
    assert hasattr(settings.history, "history_file")


def test_api_settings():
    """测试API配置"""
    assert settings.api.host == "0.0.0.0"
    assert settings.api.port == 8000
    assert isinstance(settings.api.cors_origins, list)
