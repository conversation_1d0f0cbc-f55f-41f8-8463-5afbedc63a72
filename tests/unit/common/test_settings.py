# test_settings.py
import os

import pytest

from src.common.config.settings import APISettings, OpenAISettings, Settings


@pytest.fixture
def clean_env():
    """清理环境变量的 fixture"""
    # 保存原始环境变量
    original_temp = os.environ.get("OPENAI_TEMPERATURE")

    # 清理测试相关的环境变量
    if "OPENAI_TEMPERATURE" in os.environ:
        del os.environ["OPENAI_TEMPERATURE"]

    yield

    # 测试后恢复环境变量
    if original_temp is not None:
        os.environ["OPENAI_TEMPERATURE"] = original_temp
    elif "OPENAI_TEMPERATURE" in os.environ:
        del os.environ["OPENAI_TEMPERATURE"]


@pytest.mark.daily
class TestSettings:
    def test_settings_refresh(self, clean_env):
        """测试配置刷新功能"""
        # 获取初始配置
        settings = OpenAISettings.get_instance()
        initial_temp = settings.temperature
        print(f"Initial temperature: {initial_temp}")
        assert isinstance(initial_temp, float), "temperature 应该是浮点数类型"

        # 记录初始实例 ID
        initial_id = id(settings)

        # 修改环境变量
        new_temp_value = "0.8"
        os.environ["OPENAI_TEMPERATURE"] = new_temp_value

        # 刷新配置
        new_settings = OpenAISettings.refresh()

        # 验证配置是否更新
        assert new_settings.temperature == float(
            new_temp_value
        ), f"配置未正确更新，期望: {new_temp_value}, 实际: {new_settings.temperature}"

        # 验证实例 ID
        assert id(new_settings) != initial_id, "refresh 应该返回新的实例"

        # 验证缓存更新
        cached_settings = OpenAISettings.get_instance()
        assert id(cached_settings) != initial_id, "缓存应该被更新为新实例"

        # 验证配置值一致性
        assert (
            cached_settings.temperature == new_settings.temperature
        ), "缓存实例的配置值应该与新实例一致"

    def test_settings_dict(self):
        """测试设置字典序列化"""
        # 创建设置实例
        settings = Settings()

        # 获取设置字典
        settings_dict = settings.model_dump()

        # 验证关键配置项存在
        expected_keys = {
            "openai",
            "llm",
            "mcp",
            "security",
            "search",
            "history",
            "logging",
            "api",
            "online",
        }

        actual_keys = set(settings_dict.keys())
        assert expected_keys.issubset(
            actual_keys
        ), f"缺少必要的配置项。期望: {expected_keys}, 实际: {actual_keys}"

        # 验证配置项类型
        assert isinstance(settings_dict["online"], bool), "online 应该是布尔类型"
        assert isinstance(settings_dict["openai"], dict), "openai 应该是字典类型"

        # 验证嵌套配置
        openai_config = settings_dict["openai"]
        assert "temperature" in openai_config, "openai 配置中应该包含 temperature"
        assert isinstance(openai_config["temperature"], float), "temperature 应该是浮点数类型"

    @pytest.mark.parametrize("temp_value", ["0.5", "0.0", "1.0"])
    def test_different_temperature_values(self, clean_env, temp_value):
        """测试不同温度值的配置更新"""
        # 设置环境变量
        os.environ["OPENAI_TEMPERATURE"] = temp_value

        # 刷新配置
        settings = OpenAISettings.refresh()

        # 验证配置更新
        assert settings.temperature == float(
            temp_value
        ), f"温度值未正确更新，期望: {temp_value}, 实际: {settings.temperature}"

    def test_invalid_temperature_value(self, clean_env):
        """测试无效温度值"""
        # 设置无效的环境变量值
        os.environ["OPENAI_TEMPERATURE"] = "invalid"

        # 验证是否抛出异常或使用默认值
        with pytest.raises(Exception) as exc_info:
            OpenAISettings.refresh()

        assert "invalid" in str(exc_info.value), "应该对无效的温度值抛出异常"

    def test_concurrent_refresh(self, clean_env):
        # """测试并发刷新情况"""
        import threading
        import time

        results = []

        def refresh_config():
            try:
                settings = OpenAISettings.refresh()
                results.append(id(settings))
            except Exception as e:
                results.append(e)

        # 创建多个线程同时刷新配置
        threads = [threading.Thread(target=refresh_config) for _ in range(5)]

        # 启动所有线程
        for t in threads:
            t.start()

        # 等待所有线程完成
        for t in threads:
            t.join()

        print(f"Results: {results}")
        # 验证所有刷新操作是否成功
        assert all(isinstance(x, int) for x in results), "某些刷新操作失败"

        # 验证最终实例
        final_settings = OpenAISettings.get_instance()
        print(f"Final settings id: {id(final_settings)}")
        assert id(final_settings) in results, "缓存的实例应该是最后刷新的实例之一"

    def test_refresh_with_dynamic_config(self, clean_env, monkeypatch):
        """测试使用 DynamicConfig 更新配置"""
        from src.common.config.diamond_config import dynamicConfig

        # 跟踪配置获取
        config_calls = []

        def mock_get_config(key):
            config_calls.append(key)
            print(f"Getting config for key: {key}")
            if key == "OPENAI_TEMPERATURE":
                print(f"Returning mocked value: 0.9")
                return "0.9"
            print(f"Returning None")
            return None

        monkeypatch.setattr(dynamicConfig, "get_config_by_key", mock_get_config)

        # 获取初始配置
        initial_settings = OpenAISettings.get_instance()
        print(f"Initial temperature: {initial_settings.temperature}")

        # 刷新配置
        new_settings = OpenAISettings.refresh()
        print(f"New temperature: {new_settings.temperature}")

        # 打印调试信息
        print(f"Config get attempts: {config_calls}")
        print(f"Settings dict: {new_settings.dict()}")

        # 验证配置值
        assert (
            new_settings.temperature == 0.9
        ), f"配置更新失败。期望: 0.9, 实际: {new_settings.temperature}"

        # 验证缓存更新
        cached_settings = OpenAISettings.get_instance()
        assert cached_settings.temperature == 0.9, "缓存的配置未更新"

    @pytest.mark.parametrize(
        "test_id, input_value, expected_result",
        [
            ("json_string", '["*"]', ["*"]),
            ("single_value", "http://localhost:3000", ["http://localhost:3000"]),
            ("list_value", ["*"], ["*"]),
            ("comma_separated", "host1,host2,host3", ["host1", "host2", "host3"]),
        ],
    )
    def test_api_settings_cors_origins(self, test_id, input_value, expected_result):
        """测试 APISettings 的 cors_origins 不同输入格式"""
        # 创建设置实例
        settings = APISettings(cors_origins=input_value)

        # 验证结果
        assert settings.cors_origins == expected_result, (
            f"测试用例 {test_id} 失败：\n"
            f"输入值: {input_value}\n"
            f"期望结果: {expected_result}\n"
            f"实际结果: {settings.cors_origins}"
        )

    @pytest.mark.parametrize(
        "test_case, input_value, expected_result, expect_error",
        [
            ("valid_default", ["*"], ["*"], False),
            ("invalid_json", "[invalid json]", ["*"], False),
            ("empty_string", "", [""], False),
            ("none_value", None, ["*"], False),
        ],
    )
    def test_api_settings_input_handling(
        self, test_case, input_value, expected_result, expect_error
    ):
        """测试 APISettings 各种输入情况的处理"""
        if expect_error:
            with pytest.raises(ValueError) as exc_info:
                APISettings(cors_origins=input_value)
            assert "Invalid" in str(
                exc_info.value
            ), f"测试用例 {test_case}: 应该对无效输入抛出包含 'Invalid' 的异常"
        else:
            settings = APISettings(cors_origins=input_value)
            assert (
                settings.cors_origins == expected_result
            ), f"测试用例 {test_case}: 期望结果 {expected_result}, 实际结果 {settings.cors_origins}"

    def test_api_settings_edge_cases(self):
        """测试 APISettings 的边界情况"""
        # 测试空列表
        settings = APISettings(cors_origins=[])
        assert settings.cors_origins == ["*"], "空列表应该返回默认值 ['*']"

        # 测试空格处理
        settings = APISettings(cors_origins=" host1 , host2 , host3 ")
        assert settings.cors_origins == ["host1", "host2", "host3"], "应该正确处理包含空格的输入"

        # 测试重复值
        settings = APISettings(cors_origins="host1,host1,host2")
        assert settings.cors_origins == ["host1", "host1", "host2"], "应该保持原始顺序和重复值"

    def test_api_settings_from_env(self, monkeypatch):
        """测试从环境变量加载 APISettings"""
        # 测试 JSON 字符串格式
        monkeypatch.setenv("API_CORS_ORIGINS", '["http://localhost:3000"]')
        settings = APISettings()
        assert settings.cors_origins == ["http://localhost:3000"]

        # 测试逗号分隔格式
        monkeypatch.setenv("API_CORS_ORIGINS", "host1,host2,host3")
        settings = APISettings()
        assert settings.cors_origins == ["host1", "host2", "host3"]

    def test_api_settings_dynamic_config(self, monkeypatch):
        """测试从 DynamicConfig 加载 APISettings"""
        from src.common.config.diamond_config import dynamicConfig

        def mock_get_config(key):
            if key == "API_CORS_ORIGINS":
                return '["http://example.com"]'
            return None

        monkeypatch.setattr(dynamicConfig, "get_config_by_key", mock_get_config)

        settings = APISettings()
        assert settings.cors_origins == ["http://example.com"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
