import json
import threading
import time
from typing import Any, Dict

import nacos

# 弹内默认服务地址，用于本地测试
# 正式环境地址：从aone配置的环境变量中获取
# 配置常量
DEFAULT_ENDPOINT = "http://jmenv.tbsite.net:8080/diamond-server/diamond"
DATA_ID = "waiy-infra:application"
DEFAULT_GROUP = "DEFAULT_GROUP"


class ConfigTest:
    def __init__(self):
        self.client = nacos.NacosClient(endpoint=DEFAULT_ENDPOINT, namespace="")
        self.config_changed = threading.Event()
        self.latest_config: Dict[str, Any] = {}

    def config_callback(self, args):
        """配置变更回调函数"""
        print("\n收到配置变更通知:")
        print(f"变更内容: {args}")

        try:
            if "content" in args:
                self.latest_config = json.loads(args["content"])
                print(f"解析后的新配置: {json.dumps(self.latest_config, indent=2)}")
            self.config_changed.set()  # 设置事件标志
        except Exception as e:
            print(f"处理配置变更时出错: {e}")

    def publish_config(self, config: Dict[str, Any]) -> bool:
        """发布配置"""
        try:
            content = json.dumps(config)
            result = self.client.publish_config(DATA_ID, DEFAULT_GROUP, content)
            print(f"\n发布配置{'成功' if result else '失败'}")
            print(f"发布的配置内容: {json.dumps(config, indent=2)}")
            return result
        except Exception as e:
            print(f"发布配置时出错: {e}")
            return False

    def get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        try:
            config_str = self.client.get_config(DATA_ID, DEFAULT_GROUP)
            config = json.loads(config_str) if config_str else {}
            print(f"\n当前配置: {json.dumps(config, indent=2)}")
            return config
        except Exception as e:
            print(f"获取配置时出错: {e}")
            return {}

    def run_test(self):
        """运行测试流程"""
        print("开始配置监听测试...")

        # 初始配置
        initial_config = {
            "LLM_REASONING_MODEL_PROVIDER": "others",
            "LLM_REASONING_MODEL": "o4-mini",
            "LLM_MAIN_MODEL_PROVIDER": "bailian",
            "LLM_MAIN_MODEL": "qwen-plus-latest",
            "MCP_ENABLE_FILE_SYSTEM": True,
            "API_REQUEST_TIMEOUT": 900,
        }

        # 注册配置监听器
        print("\n正在注册配置监听器...")
        self.client.add_config_watcher(
            data_id=DATA_ID, group=DEFAULT_GROUP, cb=self.config_callback
        )
        print("配置监听器注册成功")

        # 发布初始配置
        print("\n发布初始配置...")
        self.publish_config(initial_config)

        # 等待确认初始配置
        time.sleep(2)
        current_config = self.get_current_config()
        assert current_config == initial_config, "初始配置验证失败"

        # 更新配置测试
        print("\n准备更新配置...")
        self.config_changed.clear()  # 清除事件标志

        updated_config = initial_config.copy()
        updated_config.update(
            {"LLM_MAIN_MODEL": "gpt-4", "API_REQUEST_TIMEOUT": 1800, "NEW_SETTING": "test_value"}
        )

        self.publish_config(updated_config)

        # 等待配置变更通知
        print("\n等待配置变更通知...")
        if self.config_changed.wait(timeout=10):  # 最多等待10秒
            print("成功接收到配置变更通知")

            # 验证配置是否正确更新
            current_config = self.get_current_config()
            assert current_config == updated_config, "更新后的配置验证失败"
        else:
            print("等待配置变更通知超时")

        # 删除配置测试
        print("\n测试删除配置...")
        self.config_changed.clear()
        delete_result = self.client.remove_config(DATA_ID, DEFAULT_GROUP)
        print(f"删除配置{'成功' if delete_result else '失败'}")

        if self.config_changed.wait(timeout=10):
            print("成功接收到配置删除通知")
        else:
            print("等待配置删除通知超时")

        print("\n测试完成!")


# def main():
#     try:
#         test = ConfigTest()
#         test.run_test()
#     except KeyboardInterrupt:
#         print("\n测试被用户中断")
#     except Exception as e:
#         print(f"\n测试过程中出错: {e}")
#     finally:
#         print("\n测试结束")

# if __name__ == "__main__":
#     main()

# test code
if __name__ == "__main__":
    # get config
    client = nacos.NacosClient(endpoint=DEFAULT_ENDPOINT, namespace="")

    config = {
        "API_CORS_ORIGINS": ["*"],
        "API_DEBUG": False,
        "API_HOST": "0.0.0.0",
        "API_PORT": 8000,
        "API_REQUEST_TIMEOUT": 600,
        "LLM_REASONING_MODEL_PROVIDER": "others",
        "LLM_REASONING_MODEL": "gemini-2.5-pro-preview-05-06",
        "LLM_MAIN_MODEL_PROVIDER": "others",
        "LLM_MAIN_MODEL": "gpt-4.1-2025-04-14",
        "LLM_FAST_MODEL_PROVIDER": "others",
        "LLM_FAST_MODEL": "gpt-4o-mini",
        "LLM_CODE_MODEL_PROVIDER": "others",
        "LLM_CODE_MODEL": "claude-3-7-sonnet-20250219",
        "LLM_OTHERS_PROVIDER_URL": "https://vip.apiyi.com/v1",
        "MCP_DEFAULT_WORKSPACE_PATH": ".",
        "MCP_ENABLE_FILE_SYSTEM": True,
        "MCP_ENABLE_GIT": True,
        "MCP_ENABLE_TERMINAL": True,
        "MCP_ENABLE_BROWSER": True,
        "MCP_ENABLE_THINKING": True,
        "MCP_ENABLE_PYTHON": True,
        "MCP_WUYING_SERVER_URL": "https://agentbay.wuying.aliyun.com/sse",
        "MCP_WUYING_SERVER_API_KEY": "akm-5bcd851c-16b7-473f-809c-7580310abb7c",
        "LOG_DETAILED_MODE": True,
        "LOG_FILE_PATH": "logs/waiy.log",
        "LOG_LEVEL": "DEBUG",
        "LOG_TO_CONSOLE": True,
        "LOG_LOG_TO_FILE": True,
    }
    content = json.dumps(config)

    publishresult = client.publish_config(DATA_ID, DEFAULT_GROUP, content)

    print("result:" + client.get_config(DATA_ID, DEFAULT_GROUP))
