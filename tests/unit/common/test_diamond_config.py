import json
import os
import unittest
from unittest.mock import MagicMock, call, mock_open, patch

from src.common.config.diamond_config import DynamicConfig

DEFAULT_ENDPOINT = "http://jmenv.tbsite.net:8080/diamond-server/diamond"
DEFAULT_GROUP = "DEFAULT_GROUP"
DATA_ID = "com.taobao.test.qamp.lab.conf"


class TestDynamicConfig(unittest.TestCase):

    def setUp(self):
        # 设置默认环境变量
        os.environ["DIAMOND_ENDPOINT"] = DEFAULT_ENDPOINT
        os.environ["DIAMOND_DATA_ID"] = DATA_ID

    @patch("src.common.config.diamond_config.nacos.NacosClient")
    @patch("src.common.config.diamond_config.DynamicConfig.get_config")
    def test_init_with_env_endpoint(self, mock_get_config, mock_nacos_client):
        """测试 __init__ 正确读取环境变量并初始化 NacosClient"""
        mock_client_instance = MagicMock()
        mock_nacos_client.return_value = mock_client_instance

        dc = DynamicConfig()

        self.assertEqual(dc.client, mock_client_instance)
        mock_nacos_client.assert_called_once_with(endpoint=DEFAULT_ENDPOINT, namespace="")
        mock_get_config.assert_called_once_with()

    @patch("src.common.config.diamond_config.nacos.NacosClient")
    def test_get_config_success(self, mock_nacos_client):
        """测试 get_config 成功获取配置"""
        mock_client = MagicMock()
        mock_client.get_config.return_value = '{"key": "value"}'
        dc = DynamicConfig()
        dc.client = mock_client

        result = dc.get_config(group="TEST_GROUP")

        self.assertEqual(result, '{"key": "value"}')
        mock_client.get_config.assert_called_once_with(DATA_ID, "TEST_GROUP")

    @patch("src.common.config.diamond_config.nacos.NacosClient")
    def test_get_config_by_key_success(self, mock_nacos_client):
        """测试 get_config_by_key 成功获取键值"""
        dc = DynamicConfig()
        dc.config = '{"key": "value"}'

        result = dc.get_config_by_key("key")

        self.assertEqual(result, "value")

    @patch("src.common.config.diamond_config.nacos.NacosClient")
    def test_get_config_by_key_not_found(self, mock_nacos_client):
        """测试 get_config_by_key 键不存在"""
        dc = DynamicConfig()
        dc.config = '{"key": "value"}'

        result = dc.get_config_by_key("not_exists")

        self.assertIsNone(result)

    @patch("src.common.config.diamond_config.nacos.NacosClient")
    def test_get_config_by_key_config_none(self, mock_nacos_client):
        """测试 get_config_by_key config 为 None"""
        dc = DynamicConfig()
        dc.config = None

        result = dc.get_config_by_key("key")

        self.assertIsNone(result)

    @patch("json.loads")
    @patch("src.common.config.diamond_config.nacos.NacosClient")
    def test_get_config_by_key_json_parse_error(self, mock_nacos_client, mock_json_loads):
        """测试 get_config_by_key 配置不是合法 JSON"""
        mock_json_loads.side_effect = json.JSONDecodeError("Expecting value", "", 0)
        dc = DynamicConfig()
        dc.config = "invalid json"

        result = dc.get_config_by_key("key")

        self.assertIsNone(result)

    @patch("src.common.config.diamond_config.nacos.NacosClient")
    def test_get_config_by_key_list_success(self, mock_nacos_client):
        """测试 get_config_by_key_list 成功获取多个键值"""
        dc = DynamicConfig()
        dc.config = '{"key1": "value1", "key2": "value2"}'

        result = dc.get_config_by_key_list(["key1", "key2", "key3"])

        self.assertEqual(result, ["value1", "value2", None])

    @patch("src.common.config.diamond_config.nacos.NacosClient")
    def test_get_config_by_key_list_config_none(self, mock_nacos_client):
        """测试 get_config_by_key_list config 为 None"""
        dc = DynamicConfig()
        dc.config = None

        result = dc.get_config_by_key_list(["key1"])

        self.assertEqual([], result)

    @patch("json.loads")
    @patch("src.common.config.diamond_config.nacos.NacosClient")
    def test_get_config_by_key_list_json_parse_error(self, mock_nacos_client, mock_json_loads):
        """测试 get_config_by_key_list 配置不是合法 JSON"""
        mock_json_loads.side_effect = json.JSONDecodeError("Expecting value", "", 0)
        dc = DynamicConfig()
        dc.config = "invalid json"

        result = dc.get_config_by_key_list(["key1"])

        self.assertEqual([], result)


if __name__ == "__main__":
    unittest.main()
