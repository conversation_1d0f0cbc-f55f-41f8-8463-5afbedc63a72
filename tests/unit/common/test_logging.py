"""
日志模块的单元测试
"""

import io
import logging
from unittest.mock import MagicMock, patch

import pytest

from src.common.logging import logger
from src.common.logging.logger import (
    WaiyTracingProcessor,
    print_divider,
    print_error,
    print_header,
    print_info,
    print_success,
    print_trace,
    print_warning,
    setup_waiy_tracing,
)


def test_logger_initialization():
    """测试日志记录器是否正确初始化"""
    assert logger is not None
    assert isinstance(logger, logging.Logger)
    assert logger.name == "waiy"
    assert len(logger.handlers) > 0


@pytest.mark.parametrize(
    "log_func,expected_level",
    [
        (print_info, logging.INFO),
        (print_success, logging.INFO),
        (print_warning, logging.WARNING),
        (print_error, logging.ERROR),
        (print_trace, logging.DEBUG),
    ],
)
def test_print_functions_log_at_correct_level(log_func, expected_level):
    """测试各打印函数是否使用正确的日志级别"""
    with patch.object(logger, "log") as mock_log:
        log_func("Test message")
        mock_log.assert_called_once()
        assert mock_log.call_args[0][0] == expected_level


def test_print_functions_output_to_console():
    """测试各打印函数是否输出到控制台"""
    test_message = "Test message"

    with patch("src.common.logging.logger.console.print") as mock_print:
        print_header(test_message)
        assert mock_print.called
        assert test_message in str(mock_print.call_args)

        mock_print.reset_mock()
        print_info(test_message)
        assert mock_print.called
        assert test_message in str(mock_print.call_args)

        mock_print.reset_mock()
        print_success(test_message)
        assert mock_print.called
        assert test_message in str(mock_print.call_args)

        mock_print.reset_mock()
        print_warning(test_message)
        assert mock_print.called
        assert test_message in str(mock_print.call_args)

        mock_print.reset_mock()
        print_error(test_message)
        assert mock_print.called
        assert test_message in str(mock_print.call_args)

        mock_print.reset_mock()
        print_divider()
        assert mock_print.called

        mock_print.reset_mock()
        print_trace(test_message)
        assert mock_print.called
        assert test_message in str(mock_print.call_args)


def test_waiy_tracing_processor_init():
    """测试WaiyTracingProcessor初始化"""
    processor = WaiyTracingProcessor(log_to_console=True, log_to_file=False, detailed_mode=False)

    assert processor.log_to_console is True
    assert processor.log_to_file is False
    assert processor.detailed_mode is False
    assert processor.log_file is None
    assert processor.indent_level == 0
    assert isinstance(processor.active_traces, dict)
    assert isinstance(processor.active_spans, dict)


def test_setup_waiy_tracing():
    """测试跟踪设置函数"""
    with patch("src.common.logging.logger.set_trace_processors") as mock_set_processors:
        processor = setup_waiy_tracing(log_to_console=True, log_to_file=False, detailed_mode=True)

        assert isinstance(processor, WaiyTracingProcessor)
        assert processor.log_to_console is True
        assert processor.log_to_file is False
        assert processor.detailed_mode is True
        mock_set_processors.assert_called_once()


@patch("src.common.logging.logger.WaiyTracingProcessor._log_message")
def test_on_trace_start_end(mock_log_message):
    """测试跟踪开始和结束处理"""
    processor = WaiyTracingProcessor(log_to_console=True, log_to_file=False)

    # 创建模拟的Trace对象
    mock_trace = MagicMock()
    mock_trace.trace_id = "test-trace-id"
    mock_trace.name = "Test Trace"

    # 测试跟踪开始
    processor.on_trace_start(mock_trace)
    mock_log_message.assert_called_once()
    assert "TRACE START" in mock_log_message.call_args[0][0]
    assert processor.indent_level == 1
    assert "test-trace-id" in processor.active_traces

    mock_log_message.reset_mock()

    # 测试跟踪结束
    processor.on_trace_end(mock_trace)
    mock_log_message.assert_called_once()
    assert "TRACE END" in mock_log_message.call_args[0][0]
    assert processor.indent_level == 0
    assert "test-trace-id" not in processor.active_traces


@patch("src.common.logging.logger.WaiyTracingProcessor._log_message")
def test_on_span_start_end(mock_log_message):
    """测试Span开始和结束处理"""
    processor = WaiyTracingProcessor(log_to_console=True, log_to_file=False)

    # 添加一个模拟的trace
    mock_trace = MagicMock()
    mock_trace.trace_id = "test-trace-id"
    processor.active_traces["test-trace-id"] = {"spans": []}

    # 创建模拟的Span对象
    mock_span = MagicMock()
    mock_span.span_id = "test-span-id"
    mock_span.trace_id = "test-trace-id"
    mock_span.parent_id = None
    mock_span.span_data.type = "generation"
    mock_span.span_data.model = "test-model"
    mock_span.span_data.input = ["Test input"]

    # 测试Span开始
    processor.on_span_start(mock_span)
    mock_log_message.assert_called_once()
    assert "LLM CALL" in mock_log_message.call_args[0][0]
    assert "test-span-id" in processor.active_spans
    assert "test-span-id" in processor.active_traces["test-trace-id"]["spans"]

    mock_log_message.reset_mock()

    # 测试Span结束
    mock_span.span_data.output = "Test output"
    processor.on_span_end(mock_span)
    mock_log_message.assert_called_once()
    assert "COMPLETED" in mock_log_message.call_args[0][0]
    assert "test-span-id" not in processor.active_spans


def test_cleanup():
    """测试清理功能"""
    # 创建带有模拟文件的处理器
    mock_file = MagicMock()
    processor = WaiyTracingProcessor(log_to_file=True)
    processor.log_file = mock_file

    # 测试清理
    processor.cleanup()
    mock_file.close.assert_called_once()
