"""
安全检查模块的单元测试
"""

import pytest

from src.common.safety import SafetyChecker


def test_is_command_safe():
    """测试命令安全检查功能"""
    # 安全命令
    assert SafetyChecker.is_command_safe("ls -la")
    assert SafetyChecker.is_command_safe("echo 'Hello World'")
    assert SafetyChecker.is_command_safe("cd /tmp")
    assert SafetyChecker.is_command_safe("mkdir test_dir")

    # 危险命令
    assert not SafetyChecker.is_command_safe("rm -rf /")
    assert not SafetyChecker.is_command_safe("mkfs /dev/sda")
    assert not SafetyChecker.is_command_safe("chmod -R 777 /")
    assert not SafetyChecker.is_command_safe("dd if=/dev/zero of=/dev/sda")
    assert not SafetyChecker.is_command_safe("shutdown now")

    # 空命令
    assert SafetyChecker.is_command_safe("")
    assert SafetyChecker.is_command_safe(None)


def test_sanitize_path():
    """测试路径清理功能"""
    # 标准路径
    assert SafetyChecker.sanitize_path("test/path") == "test/path"

    # 路径遍历攻击
    assert SafetyChecker.sanitize_path("../../../etc/passwd") == "etcpasswd"
    assert SafetyChecker.sanitize_path("test/../secret") == "testsecret"

    # 根目录访问
    assert SafetyChecker.sanitize_path("/etc/passwd") == "etc/passwd"
    assert SafetyChecker.sanitize_path("////root") == "root"

    # 重复斜杠
    assert SafetyChecker.sanitize_path("test//path") == "test/path"


def test_is_url_safe():
    """测试URL安全检查功能"""
    # 安全URL
    assert SafetyChecker.is_url_safe("https://example.com")
    assert SafetyChecker.is_url_safe("http://api.example.com/v1/data")
    assert SafetyChecker.is_url_safe("https://subdomain.example.com:8080/path?query=value")

    # 危险URL
    assert not SafetyChecker.is_url_safe("file:///etc/passwd")
    assert not SafetyChecker.is_url_safe("data:text/html,<script>alert('XSS')</script>")
    assert not SafetyChecker.is_url_safe("javascript:alert('XSS')")

    # 空URL
    assert not SafetyChecker.is_url_safe("")
    assert not SafetyChecker.is_url_safe(None)
