"""
PC操作应用示例
展示如何在多实例环境中使用pc_operator工具
"""

from typing import Any, Dict

from agents import Agent, ModelSettings

from src.applications.base import BaseApplication
from src.common.models import AppMetadata
from src.core.plugins.pc_operator import get_session_manager, pc_operator


class PCOperatorApp(BaseApplication):
    """PC操作应用示例 - 多实例安全"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 每个应用实例都有自己的会话管理器引用
        self.session_manager = get_session_manager()

    @property
    def metadata(self) -> AppMetadata:
        """获取应用元数据"""
        return AppMetadata(
            id="pc_operator",
            name="PC操作助手",
            version="1.0.0",
            description="基于AgentBay的PC操作助手",
            tags=["pc-operator", "agentbay"],
            mcp_servers=[],
        )

    async def setup_agents(self) -> None:
        """设置智能体"""

        # 为当前实例设置会话信息
        # 在实际应用中，这些信息应该从请求context中获取
        self.session_manager.set_session_info(self.session_id, "your_agentbay_api_key_here")

        # 创建PC操作智能体
        await self.create_agent(
            name="PCOperatorAgent",
            instructions="""
            你是一个PC操作助手，可以帮助用户执行各种PC操作任务。

            你可以使用pc_operator工具来：
            1. 查看文件系统信息
            2. 执行系统命令
            3. 查看系统状态
            4. 执行各种Linux命令

            请根据用户的需求选择合适的命令来执行。
            """,
            tools=[pc_operator],
            model=self.config.fast_model,
            model_settings=ModelSettings(temperature=0.1),
        )

    async def _do_process_message(
        self, message: str, context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """处理用户消息"""
        context = context or {}

        # 从context中获取token和session_id（如果提供）
        if "runtime_resource" in context:
            runtime_resource = context["runtime_resource"]
            if runtime_resource.get("type") == "agentbay":
                token = runtime_resource.get("token")
                session_id = context.get("session_id", self.session_id)
                if token:
                    # 为当前实例设置会话信息
                    self.session_manager.set_session_info(session_id, token)

        # 调用父类方法处理消息
        return await super()._do_process_message(message, context)

    async def cleanup(self) -> None:
        """清理资源"""
        # 清理当前实例的会话信息
        self.session_manager.remove_session(self.session_id)
        await super().cleanup()


# 多实例测试示例
if __name__ == "__main__":
    import asyncio

    async def test_multiple_instances():
        """测试多实例场景"""

        # 创建多个应用实例
        app1 = PCOperatorApp(session_id="session_1")
        app2 = PCOperatorApp(session_id="session_2")

        # 为不同实例设置不同的token
        app1.session_manager.set_session_info("session_1", "token_1")
        app2.session_manager.set_session_info("session_2", "token_2")

        # 初始化应用
        await app1.initialize()
        await app2.initialize()

        # 测试消息
        result1 = await app1.process_message("请帮我查看当前目录")
        result2 = await app2.process_message("请帮我查看系统信息")

        print("实例1结果:", result1)
        print("实例2结果:", result2)

        # 清理资源
        await app1.cleanup()
        await app2.cleanup()

    asyncio.run(test_multiple_instances())
