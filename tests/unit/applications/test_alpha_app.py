"""
AlphaApp使用示例
展示如何在AlphaApp中使用PC操作功能
"""

import asyncio
from typing import Any, Dict

from src.applications.alpha.app import AlphaApp


async def example_usage():
    """使用示例"""

    # 创建应用实例，传入context
    context = {
        "session_id": "alpha_session_123",
        "agentbay_token": "your_agentbay_token_here",
        "resources": [],  # 可选的资源列表
        "runtime_resource": {"type": "agentbay", "token": "your_agentbay_token_here"},
    }

    app = AlphaApp(context=context)

    try:
        # 初始化应用
        await app.initialize()

        # 测试消息
        messages = [
            "你好，请帮我查看当前目录的文件列表",
            "请帮我搜索关于Python的最新信息",
            "请帮我执行命令：echo 'Hello World'",
        ]

        for message in messages:
            print(f"\n用户消息: {message}")
            result = await app.process_message(message)
            print(f"AI回复: {result['response']}")
            print(f"会话ID: {result['session_id']}")
            print(f"追踪ID: {result['trace_id']}")

    finally:
        # 清理资源
        await app.cleanup()


async def multi_instance_example():
    """多实例示例"""

    # 创建多个应用实例
    app1 = AlphaApp(context={"session_id": "alpha_session_1", "agentbay_token": "token_1"})

    app2 = AlphaApp(context={"session_id": "alpha_session_2", "agentbay_token": "token_2"})

    try:
        # 初始化两个实例
        await app1.initialize()
        await app2.initialize()

        # 并行处理消息
        results = await asyncio.gather(
            app1.process_message("请帮我查看系统信息"), app2.process_message("请帮我列出当前目录")
        )

        for i, result in enumerate(results, 1):
            print(f"\n实例{i}结果:")
            print(f"回复: {result['response']}")
            print(f"会话ID: {result['session_id']}")

    finally:
        # 清理资源
        await asyncio.gather(app1.cleanup(), app2.cleanup())


if __name__ == "__main__":
    # 运行示例
    asyncio.run(example_usage())
    print("\n" + "=" * 50 + "\n")
    asyncio.run(multi_instance_example())
