"""
Tests for Chat Namer Application
"""

from unittest.mock import AsyncMock, patch

import pytest

from src.applications.chat_namer.app import ChatNamerApp


class TestChatNamerApp:
    """Test cases for ChatNamerApp"""

    @pytest.fixture
    async def app(self):
        """Create a ChatNamerApp instance for testing"""
        app = ChatNamerApp()
        return app

    def test_metadata(self, app):
        """Test application metadata"""
        metadata = app.metadata
        assert metadata.id == "chat_namer"
        assert metadata.name == "会话标题生成器"
        assert "utility" in metadata.tags
        assert "chat" in metadata.tags

    def test_clean_title(self, app):
        """Test title cleaning functionality"""
        # Test removing prefixes
        assert app._clean_title("标题：测试标题") == "测试标题"
        assert app._clean_title("Title: Test Title") == "Test Title"

        # Test removing quotes
        assert app._clean_title('"测试标题"') == "测试标题"
        assert app._clean_title("'Test Title'") == "Test Title"

        # Test whitespace handling
        assert app._clean_title("  测试   标题  ") == "测试 标题"

        # Test length limiting
        long_title = "这是一个非常长的标题，需要被截断处理"
        cleaned = app._clean_title(long_title)
        assert len(cleaned) <= 33  # 30 + "..."

    @pytest.mark.asyncio
    async def test_process_message_empty_input(self, app):
        """Test handling of empty input"""
        result = await app._do_process_message("")
        assert result["response"] == "新会话"

        result = await app._do_process_message("   ")
        assert result["response"] == "新会话"

    @pytest.mark.asyncio
    async def test_process_message_long_input(self, app):
        """Test handling of overly long input"""
        long_message = "x" * 3000

        with patch.object(app, "_BaseApplication__do_process_message") as mock_super:
            mock_super.return_value = {"response": "测试标题", "turns": 1}

            result = await app._do_process_message(long_message)

            # Verify that the input was truncated
            args, kwargs = mock_super.call_args
            assert len(args[0]) <= 2003  # 2000 + "..."

    @pytest.mark.asyncio
    async def test_process_message_error_handling(self, app):
        """Test error handling in message processing"""
        with patch.object(app, "_BaseApplication__do_process_message") as mock_super:
            mock_super.side_effect = Exception("Test error")

            result = await app._do_process_message("test message")

            assert result["response"] == "新会话"
            assert result["turns"] == 1
