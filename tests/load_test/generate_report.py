import json
import os
from datetime import datetime
from pathlib import Path

import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns


class ReportGenerator:
    def __init__(self, csv_file: str):
        self.df = pd.read_csv(csv_file)
        self.df["request_time"] = pd.to_datetime(self.df["request_time"])
        self.df["response_time"] = pd.to_datetime(self.df["response_time"])
        self.report_dir = Path("reports")
        self.report_dir.mkdir(exist_ok=True)

    def generate_summary_stats(self) -> dict:
        """Generate summary statistics for the test results."""
        total_requests = len(self.df)
        successful_requests = self.df["success"].sum()
        failed_requests = total_requests - successful_requests
        avg_response_time = self.df["duration"].mean()

        # Calculate success rate by endpoint
        endpoint_stats = (
            self.df.groupby("path")
            .agg({"success": ["count", "sum", "mean"], "duration": ["mean", "min", "max"]})
            .round(4)
        )

        # Calculate success rate by method
        method_stats = (
            self.df.groupby("method")
            .agg({"success": ["count", "sum", "mean"], "duration": ["mean", "min", "max"]})
            .round(4)
        )

        # Calculate time-based statistics
        time_stats = (
            self.df.groupby(self.df["request_time"].dt.floor("1min"))
            .agg({"success": ["count", "mean"], "duration": ["mean", "min", "max"]})
            .round(4)
        )

        return {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "avg_response_time": avg_response_time,
            "endpoint_stats": endpoint_stats,
            "method_stats": method_stats,
            "time_stats": time_stats,
        }

    def create_visualizations(self):
        """Create various visualizations of the test results."""
        # Set style
        plt.style.use("default")

        # Response Time Distribution
        plt.figure(figsize=(10, 6))
        sns.histplot(data=self.df, x="duration", bins=50)
        plt.title("Response Time Distribution")
        plt.xlabel("Response Time (seconds)")
        plt.ylabel("Count")
        plt.savefig(self.report_dir / "response_time_dist.png")
        plt.close()

        # Success Rate Over Time
        plt.figure(figsize=(12, 6))
        success_rate = self.df.groupby(self.df["request_time"].dt.floor("1min"))["success"].mean()
        success_rate.plot(kind="line")
        plt.title("Success Rate Over Time")
        plt.xlabel("Time")
        plt.ylabel("Success Rate")
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(self.report_dir / "success_rate.png")
        plt.close()

        # Response Time by Endpoint
        plt.figure(figsize=(12, 6))
        sns.boxplot(data=self.df, x="path", y="duration")
        plt.title("Response Time by Endpoint")
        plt.xlabel("Endpoint")
        plt.ylabel("Response Time (seconds)")
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(self.report_dir / "response_time_by_endpoint.png")
        plt.close()

        # Requests Per Minute
        plt.figure(figsize=(12, 6))
        requests_per_minute = self.df.groupby(self.df["request_time"].dt.floor("1min")).size()
        requests_per_minute.plot(kind="bar")
        plt.title("Requests Per Minute")
        plt.xlabel("Time")
        plt.ylabel("Number of Requests")
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(self.report_dir / "requests_per_minute.png")
        plt.close()

    def generate_html_report(self):
        """Generate HTML report with statistics and visualizations."""
        stats = self.generate_summary_stats()

        # 生成 API 调用详情表格
        api_details = []
        for _, row in self.df.iterrows():
            api_details.append(
                {
                    "request_id": row["request_id"],
                    "request_time": row["request_time"],
                    "response_time": row["response_time"],
                    "duration": f"{row['duration']:.3f}s",
                    "path": row["path"],
                    "method": row["method"],
                    "status": row["status"],
                    "success": "Yes" if row["success"] else "No",
                    "response": row["response"],
                }
            )

        # 生成 API 详情表格的 HTML
        api_details_html = """
        <div class="api-details">
            <h2>API 调用详情</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>请求ID</th>
                            <th>请求时间</th>
                            <th>响应时间</th>
                            <th>耗时</th>
                            <th>接口</th>
                            <th>方法</th>
                            <th>状态码</th>
                            <th>是否成功</th>
                            <th>响应内容</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        for detail in api_details:
            api_details_html += f"""
                        <tr>
                            <td>{detail['request_id']}</td>
                            <td>{detail['request_time']}</td>
                            <td>{detail['response_time']}</td>
                            <td>{detail['duration']}</td>
                            <td>{detail['path']}</td>
                            <td>{detail['method']}</td>
                            <td>{detail['status']}</td>
                            <td>{detail['success']}</td>
                            <td><pre>{detail['response']}</pre></td>
                        </tr>
            """
        api_details_html += """
                    </tbody>
                </table>
            </div>
        </div>
        """

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Load Test Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .stats {{ display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0; }}
                .stat-card {{ background: #f5f5f5; padding: 20px; border-radius: 5px; }}
                .visualization {{ margin: 20px 0; }}
                img {{ max-width: 100%; }}
                .api-details {{ margin: 20px 0; }}
                .table-container {{ overflow-x: auto; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f5f5f5; }}
                pre {{ white-space: pre-wrap; word-wrap: break-word; max-height: 200px; overflow-y: auto; }}
                tr:nth-child(even) {{ background-color: #f9f9f9; }}
                .stats-table {{ margin: 20px 0; }}
                .stats-table table {{ width: auto; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Load Test Report</h1>
                <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

                <div class="stats">
                    <div class="stat-card">
                        <h3>Total Requests</h3>
                        <p>{stats['total_requests']}</p>
                    </div>
                    <div class="stat-card">
                        <h3>Successful Requests</h3>
                        <p>{stats['successful_requests']}</p>
                    </div>
                    <div class="stat-card">
                        <h3>Failed Requests</h3>
                        <p>{stats['failed_requests']}</p>
                    </div>
                    <div class="stat-card">
                        <h3>Success Rate</h3>
                        <p>{(stats['successful_requests']/stats['total_requests'])*100:.2f}%</p>
                    </div>
                    <div class="stat-card">
                        <h3>Average Response Time</h3>
                        <p>{stats['avg_response_time']:.3f} seconds</p>
                    </div>
                </div>

                <div class="stats-table">
                    <h2>Endpoint Statistics</h2>
                    {stats['endpoint_stats'].to_html()}
                </div>

                <div class="stats-table">
                    <h2>Method Statistics</h2>
                    {stats['method_stats'].to_html()}
                </div>

                <div class="stats-table">
                    <h2>Time-based Statistics (per minute)</h2>
                    {stats['time_stats'].to_html()}
                </div>

                <div class="visualization">
                    <h2>Response Time Distribution</h2>
                    <img src="response_time_dist.png" alt="Response Time Distribution">
                </div>

                <div class="visualization">
                    <h2>Success Rate Over Time</h2>
                    <img src="success_rate.png" alt="Success Rate Over Time">
                </div>

                <div class="visualization">
                    <h2>Response Time by Endpoint</h2>
                    <img src="response_time_by_endpoint.png" alt="Response Time by Endpoint">
                </div>

                <div class="visualization">
                    <h2>Requests Per Minute</h2>
                    <img src="requests_per_minute.png" alt="Requests Per Minute">
                </div>

                {api_details_html}
            </div>
        </body>
        </html>
        """

        report_file = self.report_dir / f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        with open(report_file, "w") as f:
            f.write(html_content)

        return report_file


def main():
    # Find the most recent CSV file in the data directory
    data_dir = Path("data")
    if not data_dir.exists():
        print("No data directory found!")
        return

    csv_files = list(data_dir.glob("load_test_results_*.csv"))
    if not csv_files:
        print("No result files found!")
        return

    latest_csv = max(csv_files, key=lambda x: x.stat().st_mtime)

    generator = ReportGenerator(str(latest_csv))
    generator.create_visualizations()
    report_file = generator.generate_html_report()
    print(f"Report generated: {report_file}")


if __name__ == "__main__":
    main()
