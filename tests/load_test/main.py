import asyncio
import json
import logging
import os
import random
import time
from datetime import datetime
from typing import Any, Dict, List

import aiohttp
import pandas as pd
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Create logs directory if it doesn't exist
logs_dir = "logs"
os.makedirs(logs_dir, exist_ok=True)

# Generate timestamp for log files
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = os.path.join(logs_dir, f"load_test_{timestamp}.log")
error_log_filename = os.path.join(logs_dir, f"error_{timestamp}.log")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler(log_filename), logging.StreamHandler()],
)

# Configure error logging
error_logger = logging.getLogger("error_logger")
error_logger.setLevel(logging.ERROR)
error_handler = logging.FileHandler(error_log_filename)
error_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
error_logger.addHandler(error_handler)

# Log the start of a new test run
logging.info(f"Starting new load test run at {timestamp}")
logging.info(f"Log files: {log_filename}, {error_log_filename}")

# Configuration (all as constants now)
BASE_URL = "https://pre-waiy-infra-api.wuying.aliyuncs.com"
API_KEY = "2bda943c-ba2b-11ec-ba07-test"
CONCURRENCY_LEVEL = 10  # 并发数
TEST_DURATION_HOURS = 1  # 压测时长（小时）
TEST_DURATION = TEST_DURATION_HOURS * 3600  # Convert hours to seconds

# Log configuration values
logging.info(f"Configuration loaded:")
logging.info(f"BASE_URL: {BASE_URL}")
logging.info(f"CONCURRENCY_LEVEL: {CONCURRENCY_LEVEL}")
logging.info(f"TEST_DURATION: {TEST_DURATION} seconds ({TEST_DURATION/3600:.1f} hours)")

# API Endpoints and their configurations
ENDPOINTS = [
    {"method": "GET", "path": "/apps/customer_service/info"},
    # {"method": "GET", "path": "/apps/deep-research/info"},
    # {"method": "GET", "path": "/apps/deeper/info"},
    # {"method": "GET", "path": "/apps/fin-research/info"},
    # {"method": "GET", "path": "/apps/manus/info"},
    {"method": "GET", "path": "/apps"},
]

# Message templates for POST requests
MESSAGE_TEMPLATES = [
    "你是谁？",
    "你能帮我做些什么？",
    "你有哪些能力？",
    "生成最近一周的杭州旅游攻略？",
    "分析一下当前阿里巴巴财报？",
    "国庆北京自由行5天攻略",
    "调研一下阿里巴巴通义千问大模型在行业中的位置，请你广泛检索互联网信息，生成图文并茂的报告",
    "你觉得谁会是中美竞争的赢家？请你检索信息，并生成图文并茂的HTML格式的报告",
    "对比当前主流LLM模型的API定价方案，包括输入输出token成本、速率限制和响应延迟。需要抓取官方文档数据，生成对比矩阵，并推荐最适合高频小文本处理的三个选项。把结果汇总生成一个html文件并打开预览。",
]

POST_ENDPOINTS = [
    "/apps/customer_service/message",
    # "/apps/deep-research/message",
    # "/apps/deeper/message",
    # "/apps/fin-research/message",
    # "/apps/manus/message"
]


class LoadTest:
    def __init__(self):
        self.results = []
        self.start_time = None
        self.end_time = None
        self.session = None
        self.headers = {"apikey": API_KEY, "Content-Type": "application/json"}
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        logging.info("LoadTest initialized")

    async def init_session(self):
        self.session = aiohttp.ClientSession(headers=self.headers)
        logging.info("HTTP session initialized")

    async def close_session(self):
        if self.session:
            await self.session.close()
            logging.info("HTTP session closed")

    def get_random_message(self) -> Dict[str, Any]:
        message = random.choice(MESSAGE_TEMPLATES)
        logging.debug(f"Selected random message: {message}")
        return {"message": message, "context": {}}

    def format_json(self, data: Any) -> str:
        """Format JSON data with proper indentation and ensure_ascii=False"""
        return json.dumps(data, indent=2, ensure_ascii=False, sort_keys=True)

    def log_request(self, request_id: int, method: str, path: str, data: Dict = None):
        """Log request details"""
        request_info = {
            "request_id": request_id,
            "method": method,
            "url": f"{BASE_URL}{path}",
            "headers": self.headers,
            "data": data,
        }
        logging.info(f"Request #{request_id}:\n{self.format_json(request_info)}")

    def log_response(self, request_id: int, status: int, response_text: str, duration: float):
        """Log response details"""
        try:
            # Try to parse response as JSON
            response_json = json.loads(response_text)
            response_info = {
                "request_id": request_id,
                "status": status,
                "duration": f"{duration:.2f}s",
                "response": response_json,
            }
        except json.JSONDecodeError:
            # If not JSON, use raw text
            response_info = {
                "request_id": request_id,
                "status": status,
                "duration": f"{duration:.2f}s",
                "response": response_text,
            }
        logging.info(f"Response #{request_id}:\n{self.format_json(response_info)}")

    def log_error(self, request_id: int, method: str, path: str, error: str, duration: float):
        """Log error details to both main log and error log"""
        error_info = {
            "request_id": request_id,
            "method": method,
            "path": path,
            "error": error,
            "duration": f"{duration:.2f}s",
        }
        error_message = f"Request #{request_id} failed:\n{self.format_json(error_info)}"
        logging.error(error_message)
        error_logger.error(error_message)

    async def make_request(self, method: str, path: str, data: Dict = None) -> Dict:
        start_time = time.time()
        request_id = self.total_requests + 1
        request_timestamp = datetime.now()

        # Log request details
        self.log_request(request_id, method, path, data)

        try:
            if method == "GET":
                async with self.session.get(f"{BASE_URL}{path}") as response:
                    response_text = await response.text()
                    status = response.status
            else:
                async with self.session.post(f"{BASE_URL}{path}", json=data) as response:
                    response_text = await response.text()
                    status = response.status

            end_time = time.time()
            response_timestamp = datetime.now()
            duration = end_time - start_time
            success = 200 <= status < 300

            # Log response details
            self.log_response(request_id, status, response_text, duration)

            if success:
                self.successful_requests += 1
                logging.info(
                    f"Request #{request_id} successful - Status: {status}, Duration: {duration:.2f}s"
                )
            else:
                self.failed_requests += 1
                error_message = f"Request failed with status {status}"
                self.log_error(request_id, method, path, error_message, duration)

            self.total_requests += 1
            return {
                "request_id": request_id,
                "request_time": request_timestamp.isoformat(),
                "response_time": response_timestamp.isoformat(),
                "method": method,
                "path": path,
                "status": status,
                "duration": duration,
                "response": response_text,
                "success": success,
            }
        except Exception as e:
            end_time = time.time()
            response_timestamp = datetime.now()
            self.failed_requests += 1
            self.total_requests += 1
            self.log_error(request_id, method, path, str(e), end_time - start_time)
            return {
                "request_id": request_id,
                "request_time": request_timestamp.isoformat(),
                "response_time": response_timestamp.isoformat(),
                "method": method,
                "path": path,
                "status": 0,
                "duration": end_time - start_time,
                "response": str(e),
                "success": False,
            }

    async def run_test(self):
        self.start_time = time.time()
        logging.info(
            f"Starting load test for {TEST_DURATION} seconds with {CONCURRENCY_LEVEL} concurrent requests"
        )
        await self.init_session()

        while time.time() - self.start_time < TEST_DURATION:
            tasks = []
            # Create a batch of requests for each concurrent user
            for _ in range(CONCURRENCY_LEVEL):
                # Each concurrent user will make multiple requests simultaneously
                user_tasks = []

                # Add a GET request
                endpoint = random.choice(ENDPOINTS)
                user_tasks.append(self.make_request(endpoint["method"], endpoint["path"]))

                # Add a POST request
                path = random.choice(POST_ENDPOINTS)
                data = self.get_random_message()
                user_tasks.append(self.make_request("POST", path, data))

                # Add all user's requests to the main task list
                tasks.extend(user_tasks)

            # Execute all requests concurrently
            results = await asyncio.gather(*tasks)
            self.results.extend(results)

            # Log progress every 100 requests
            if self.total_requests % 100 == 0:
                elapsed_time = time.time() - self.start_time
                logging.info(
                    f"Progress: {self.total_requests} requests completed in {elapsed_time:.2f}s"
                )
                logging.info(
                    f"Success rate: {(self.successful_requests/self.total_requests)*100:.2f}%"
                )

            # Small delay to prevent overwhelming the server
            await asyncio.sleep(0.1)

        self.end_time = time.time()
        await self.close_session()
        logging.info("Load test completed")

    def save_results(self):
        # Create data directory if it doesn't exist
        os.makedirs("data", exist_ok=True)

        # Save to CSV
        df = pd.DataFrame(self.results)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_path = f"data/load_test_results_{timestamp}.csv"
        df.to_csv(csv_path, index=False)

        # Calculate statistics
        total_requests = len(self.results)
        successful_requests = sum(1 for r in self.results if r["success"])
        failed_requests = total_requests - successful_requests
        avg_duration = sum(r["duration"] for r in self.results) / total_requests

        # Calculate success rate by endpoint
        endpoint_stats = (
            df.groupby("path")
            .agg({"success": ["count", "sum", "mean"], "duration": ["mean", "min", "max"]})
            .round(4)
        )

        # Calculate success rate by method
        method_stats = (
            df.groupby("method")
            .agg({"success": ["count", "sum", "mean"], "duration": ["mean", "min", "max"]})
            .round(4)
        )

        # Calculate time-based statistics
        df["request_time"] = pd.to_datetime(df["request_time"])
        df["response_time"] = pd.to_datetime(df["response_time"])
        time_stats = (
            df.groupby(df["request_time"].dt.floor("1min"))
            .agg({"success": ["count", "mean"], "duration": ["mean", "min", "max"]})
            .round(4)
        )

        summary = f"""
Load Test Summary:
-----------------
Test Duration: {self.end_time - self.start_time:.2f} seconds
Total Requests: {total_requests}
Successful Requests: {successful_requests}
Failed Requests: {failed_requests}
Overall Success Rate: {(successful_requests/total_requests)*100:.2f}%
Average Response Time: {avg_duration:.2f} seconds

Endpoint Statistics:
------------------
{endpoint_stats}

Method Statistics:
----------------
{method_stats}

Time-based Statistics (per minute):
--------------------------------
{time_stats}

Results saved to: {csv_path}
"""
        logging.info(summary)
        print(summary)

        # Save detailed statistics to a separate file
        stats_path = f"data/load_test_stats_{timestamp}.txt"
        with open(stats_path, "w") as f:
            f.write(summary)
        logging.info(f"Detailed statistics saved to: {stats_path}")


async def main():
    try:
        load_test = LoadTest()
        await load_test.run_test()
        load_test.save_results()
    except Exception as e:
        error_message = f"Error during load test: {str(e)}"
        logging.error(error_message)
        error_logger.error(error_message)
        raise


if __name__ == "__main__":
    asyncio.run(main())
